{% extends 'base/base.html' %}
{% load static %}

{% block title %}车位管理 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-car me-2"></i>地下车位出租列表
                        <span class="badge bg-success ms-2">正常 {{ page_obj.paginator.count }}个</span>
                    </h4>
                </div>

               

                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'parking:add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>新增车位
                                </a>
                                <a href="{% url 'parking:export' %}" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>导出数据
                                </a>
                                <a href="{% url 'parking:import' %}" class="btn btn-warning">
                                    <i class="fas fa-upload me-1"></i>导入数据
                                </a>
                                <a href="{% url 'parking:overdue' %}" class="btn btn-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>逾期车位
                                </a>
                                <a href="{% url 'parking:checkout' %}" class="btn btn-secondary">
                                    <i class="fas fa-sign-out-alt me-1"></i>退车位列表
                                </a>
                                <a href="{% url 'parking:payment_history' %}" class="btn btn-success">
                                    <i class="fas fa-history me-1"></i>缴费流水
                                </a>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'parking:list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th>车位号</th>
                                        <th>房号</th>
                                        <th>租户姓名</th>
                                        <th>电话</th>
                                        <th>车牌号</th>
                                        <th>车位所有者</th>
                                        <th>所有者电话</th>
                                        <th>租车位时间</th>
                                        <th>物业费到期时间</th>
                                        <th>剩余天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th>车位号</th>
                                        <th>房号</th>
                                        <th>租户姓名</th>
                                        <th>电话</th>
                                        <th>车牌号</th>
                                        <th>车位所有者</th>
                                        <th>所有者电话</th>
                                        <th>租车位时间</th>
                                        <th>物业费到期时间</th>
                                        <th>剩余天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for parking in parkings %}
                                <tr>
                                    <td>{{ parking.parking_number }}</td>
                                    <td>{{ parking.room_number }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator status-active"></div>
                                            {{ parking.tenant_name }}
                                        </div>
                                    </td>
                                    <td>{{ parking.phone }}</td>
                                    <td>{{ parking.license_plate }}</td>
                                    <td>{{ parking.owner_name|default:"-" }}</td>
                                    <td>{{ parking.owner_phone|default:"-" }}</td>
                                    <td>{{ parking.lease_start_date }}</td>
                                    <td>{{ parking.property_fee_due_date }}</td>
                                    <td>
                                        {% if parking.days_until_due > 0 %}
                                            <span class="badge bg-success">{{ parking.days_until_due }}天</span>
                                        {% elif parking.days_until_due == 0 %}
                                            <span class="badge bg-warning">今天到期</span>
                                        {% else %}
                                            <span class="badge bg-danger">已逾期</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="parking-action-buttons">
                                            <a href="{% url 'parking:edit' parking.pk %}" class="hex-btn edit-hex" title="编辑车位信息">
                                                <i class="fas fa-edit"></i>
                                                <span>编辑</span>
                                            </a>
                                            <a href="{% url 'parking:renew' parking.pk %}" class="hex-btn renew-hex" title="续费缴费">
                                                <i class="fas fa-money-bill-wave"></i>
                                                <span>续费</span>
                                            </a>
                                            <button type="button" class="hex-btn checkout-hex" title="办理退车位"
                                                    onclick="checkoutParking({{ parking.pk }})">
                                                <i class="fas fa-sign-out-alt"></i>
                                                <span>退车</span>
                                            </button>
                                            <a href="{% url 'parking:delete' parking.pk %}" class="hex-btn delete-hex" title="删除车位">
                                                <i class="fas fa-trash"></i>
                                                <span>删除</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-car fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无车位数据</p>
                                        <a href="{% url 'parking:add' %}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>添加第一个车位
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }
    
    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }
    
    .search-box input {
        padding-left: 35px;
    }
    
    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-active {
        background-color: #28a745;
    }
    
    .toolbar {
        margin-bottom: 20px;
    }

    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1200px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1200px;
    }

    #body-table thead {
        margin: 0 !important;
        padding: 0 !important;
        height: 0 !important;
        line-height: 0 !important;
    }

    #body-table thead th {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        line-height: 0 !important;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        font-size: 0.9rem;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    .table-body-scroll tr:hover {
        background-color: #f8f9fa;
    }

    .table-body-scroll tr:last-child td {
        border-bottom: none;
    }

    /* 列宽设置 - 地下车位表格 */
    .table-header-fixed th:nth-child(1),
    .table-body-scroll td:nth-child(1) { width: 80px; }
    .table-header-fixed th:nth-child(2),
    .table-body-scroll td:nth-child(2) { width: 100px; }
    .table-header-fixed th:nth-child(3),
    .table-body-scroll td:nth-child(3) { width: 120px; }
    .table-header-fixed th:nth-child(4),
    .table-body-scroll td:nth-child(4) { width: 120px; }
    .table-header-fixed th:nth-child(5),
    .table-body-scroll td:nth-child(5) { width: 100px; }
    .table-header-fixed th:nth-child(6),
    .table-body-scroll td:nth-child(6) { width: 120px; }
    .table-header-fixed th:nth-child(7),
    .table-body-scroll td:nth-child(7) { width: 120px; }
    .table-header-fixed th:nth-child(8),
    .table-body-scroll td:nth-child(8) { width: 120px; }
    .table-header-fixed th:nth-child(9),
    .table-body-scroll td:nth-child(9) { width: 140px; }
    .table-header-fixed th:nth-child(10),
    .table-body-scroll td:nth-child(10) { width: 90px; }
    .table-header-fixed th:nth-child(11),
    .table-body-scroll td:nth-child(11) { width: 180px; }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }
    }

    /* 车位管理专用六边形科技风格按钮 */
    .parking-action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
        padding: 6px;
    }

    .hex-btn {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 14px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 65px;
        overflow: hidden;
        background: #1a1a2e;
        color: white;
        clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .hex-btn i {
        font-size: 14px;
        margin-right: 6px;
        transition: all 0.3s ease;
    }

    .hex-btn span {
        font-size: 11px;
        font-weight: 700;
        position: relative;
        z-index: 2;
    }

    .hex-btn:hover {
        transform: translateY(-3px) scale(1.05);
        text-decoration: none;
        color: white;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .hex-btn:hover i {
        transform: scale(1.2);
    }

    .hex-btn:active {
        transform: translateY(-1px) scale(1.02);
    }

    /* 编辑按钮 - 蓝色科技 */
    .edit-hex {
        background: linear-gradient(135deg, #0f3460, #16537e);
        box-shadow: 0 4px 15px rgba(15, 52, 96, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .edit-hex:hover {
        background: linear-gradient(135deg, #16537e, #1e6091);
        color: white;
        box-shadow: 0 8px 25px rgba(15, 52, 96, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .edit-hex::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(22, 83, 126, 0.3), transparent);
        clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .edit-hex:hover::before {
        opacity: 1;
    }

    /* 续费按钮 - 绿色科技 */
    .renew-hex {
        background: linear-gradient(135deg, #0d5d2e, #198754);
        box-shadow: 0 4px 15px rgba(13, 93, 46, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .renew-hex:hover {
        background: linear-gradient(135deg, #198754, #20c997);
        color: white;
        box-shadow: 0 8px 25px rgba(13, 93, 46, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .renew-hex::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(25, 135, 84, 0.3), transparent);
        clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .renew-hex:hover::before {
        opacity: 1;
    }

    /* 退车按钮 - 橙色科技 */
    .checkout-hex {
        background: linear-gradient(135deg, #b45309, #fd7e14);
        box-shadow: 0 4px 15px rgba(180, 83, 9, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .checkout-hex:hover {
        background: linear-gradient(135deg, #fd7e14, #fd9843);
        color: white;
        box-shadow: 0 8px 25px rgba(180, 83, 9, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .checkout-hex::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(253, 126, 20, 0.3), transparent);
        clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .checkout-hex:hover::before {
        opacity: 1;
    }

    /* 删除按钮 - 红色科技 */
    .delete-hex {
        background: linear-gradient(135deg, #842029, #dc3545);
        box-shadow: 0 4px 15px rgba(132, 32, 41, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .delete-hex:hover {
        background: linear-gradient(135deg, #dc3545, #e35d6a);
        color: white;
        box-shadow: 0 8px 25px rgba(132, 32, 41, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .delete-hex::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(220, 53, 69, 0.3), transparent);
        clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .delete-hex:hover::before {
        opacity: 1;
    }

    /* 六边形按钮扫描线动画 */
    @keyframes hexScan {
        0% {
            transform: translateX(-100%);
        }
        100% {
            transform: translateX(100%);
        }
    }

    .hex-btn::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
        transition: left 0.6s ease;
        z-index: 3;
    }

    .hex-btn:hover::after {
        left: 100%;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .parking-action-buttons {
            gap: 6px;
            flex-wrap: wrap;
        }

        .hex-btn {
            padding: 6px 10px;
            min-width: 55px;
            font-size: 10px;
        }

        .hex-btn i {
            font-size: 12px;
            margin-right: 4px;
        }

        .hex-btn span {
            font-size: 9px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头表体列宽
        syncTableColumns();

        // 同步表头表体列宽函数
        function syncTableColumns() {
            const headerTable = document.getElementById('header-table');
            const bodyTable = document.getElementById('body-table');

            if (!headerTable || !bodyTable) {
                console.log('表头或表体表格未找到');
                return;
            }

            const headerCells = headerTable.querySelectorAll('th');
            const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

            // 定义列宽 - 地下车位表格的列宽配置
            const columnWidths = [
                '80px',   // 车位号
                '100px',  // 房号
                '120px',  // 租户姓名
                '120px',  // 电话
                '100px',  // 车牌号
                '120px',  // 车位所有者
                '120px',  // 所有者电话
                '120px',  // 租车位时间
                '140px',  // 物业费到期时间
                '90px',   // 剩余天数
                '300px'   // 操作
            ];

            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 设置表体隐藏表头列宽（用于对齐）
            bodyHeaderCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 确保两个表格宽度一致
            headerTable.style.width = '100%';
            bodyTable.style.width = '100%';
            headerTable.style.minWidth = '1200px';
            bodyTable.style.minWidth = '1200px';

            console.log('✅ 地下车位表头表体列宽同步完成');
        }

        // 窗口大小改变时重新同步列宽
        window.addEventListener('resize', function() {
            setTimeout(syncTableColumns, 50);
        });


    });
    
    // 退车位功能
    function checkoutParking(parkingId) {
        if (confirm('确定要退车位吗？退车位后车位将移至退车位列表。')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/parking/' + parkingId + '/checkout-action/';
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
