from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random
from dateutil.relativedelta import relativedelta

from tenants.models import Tenant, TenantPaymentHistory


class Command(BaseCommand):
    help = '修复缴费记录，使用正确的费用期间计算'

    def handle(self, *args, **options):
        self.stdout.write('开始修复缴费记录...')
        
        # 清除现有的缴费记录
        TenantPaymentHistory.objects.all().delete()
        self.stdout.write('已清除现有缴费记录')
        
        # 获取所有租客
        tenants = Tenant.objects.all()
        
        if not tenants:
            self.stdout.write(self.style.ERROR('没有找到租客'))
            return
        
        payment_methods = ['现金', '银行转账', '支付宝', '微信支付', '刷卡']
        created_count = 0
        
        # 为每个租客创建缴费记录
        for tenant in tenants:
            # 1. 创建入住时的初始缴费记录
            move_in_date = tenant.move_in_date
            due_date = tenant.property_fee_due_date
            
            # 计算从入住到第一次到期的月数
            rd = relativedelta(due_date, move_in_date)
            months_diff = rd.years * 12 + rd.months
            
            if rd.days > 0:
                months_diff += 1  # 有剩余天数就算一个月
            
            if months_diff > 0:
                # 计算缴费金额
                total_amount = tenant.property_fee * months_diff
                
                # 创建入住缴费记录
                TenantPaymentHistory.objects.create(
                    tenant=tenant,
                    amount=total_amount,
                    fee_start_date=move_in_date,
                    fee_end_date=due_date,
                    payment_method=random.choice(payment_methods),
                    notes='入住缴费',
                    payment_date=timezone.now() - timedelta(days=random.randint(30, 180))
                )
                created_count += 1
            
            # 2. 创建1-2条续费记录
            renewal_count = random.randint(1, 2)
            current_due = due_date
            
            for i in range(renewal_count):
                # 随机续费月数
                renewal_months = random.choice([1, 3, 6, 12])
                
                # 计算费用期间
                fee_start_date = current_due + timedelta(days=1)
                fee_end_date = fee_start_date + relativedelta(months=renewal_months) - timedelta(days=1)
                
                # 计算金额
                amount = tenant.property_fee * renewal_months
                
                # 创建续费记录
                payment_date = timezone.now() - timedelta(days=random.randint(1, 90))
                
                TenantPaymentHistory.objects.create(
                    tenant=tenant,
                    amount=amount,
                    fee_start_date=fee_start_date,
                    fee_end_date=fee_end_date,
                    payment_method=random.choice(payment_methods),
                    notes=f'续费{renewal_months}个月',
                    payment_date=payment_date
                )
                created_count += 1
                
                # 更新当前到期日期
                current_due = fee_end_date
        
        self.stdout.write(
            self.style.SUCCESS(f'成功创建 {created_count} 条缴费记录！')
        )
        
        # 显示一些示例记录
        self.stdout.write('\n示例缴费记录：')
        sample_payments = TenantPaymentHistory.objects.select_related('tenant')[:5]
        for payment in sample_payments:
            self.stdout.write(
                f'{payment.tenant.tenant_name} - {payment.fee_start_date} 至 {payment.fee_end_date} - ¥{payment.amount}'
            )
