// 主要JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 自动隐藏警告消息
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // 确认删除对话框
    var deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            var message = this.getAttribute('data-message') || '确定要删除这条记录吗？';
            if (confirm(message)) {
                window.location.href = this.href;
            }
        });
    });

    // 批量操作
    var selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            var checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllCheckbox.checked;
            });
            updateBatchButtons();
        });
    }

    // 单个复选框变化
    var itemCheckboxes = document.querySelectorAll('.item-checkbox');
    itemCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            updateBatchButtons();
            updateSelectAllCheckbox();
        });
    });

    // 更新批量操作按钮状态
    function updateBatchButtons() {
        var checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        var batchButtons = document.querySelectorAll('.batch-action');
        
        batchButtons.forEach(function(button) {
            button.disabled = checkedBoxes.length === 0;
        });
    }

    // 更新全选复选框状态
    function updateSelectAllCheckbox() {
        var selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            var itemCheckboxes = document.querySelectorAll('.item-checkbox');
            var checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedBoxes.length === itemCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    // 批量删除
    var batchDeleteButton = document.getElementById('batch-delete');
    if (batchDeleteButton) {
        batchDeleteButton.addEventListener('click', function(e) {
            e.preventDefault();
            var checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            
            if (confirm('确定要删除选中的 ' + checkedBoxes.length + ' 条记录吗？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = this.getAttribute('data-url');
                
                var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                var csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
                
                checkedBoxes.forEach(function(checkbox) {
                    var input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'selected_items';
                    input.value = checkbox.value;
                    form.appendChild(input);
                });
                
                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    // 搜索功能
    var searchForm = document.getElementById('search-form');
    if (searchForm) {
        var searchInput = searchForm.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(function() {
                    searchForm.submit();
                }, 500);
            });
        }
    }

    // 日期选择器
    var dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(input) {
        // 设置默认日期格式
        if (!input.value && input.hasAttribute('data-default-today')) {
            var today = new Date();
            input.value = today.toISOString().split('T')[0];
        }
    });

    // 房号自动计算平米数和楼层
    var roomNumberInput = document.getElementById('id_room_number');
    var areaInput = document.getElementById('id_area');
    var floorInput = document.getElementById('id_floor');

    if (roomNumberInput && areaInput) {
        roomNumberInput.addEventListener('input', function() {
            var roomNumber = this.value;
            if (roomNumber) {
                var lastTwoDigits = roomNumber.slice(-2);
                if (lastTwoDigits === '01' || lastTwoDigits === '04') {
                    areaInput.value = '130';
                } else if (lastTwoDigits === '02' || lastTwoDigits === '03') {
                    areaInput.value = '90';
                }

                // 自动计算楼层：从房号中提取楼层数
                if (roomNumber.length >= 3 && floorInput) {
                    // 房号格式：101(1楼), 502(5楼), 1003(10楼)
                    // 取除了最后两位之外的前面数字作为楼层
                    var floorNumber = roomNumber.slice(0, -2);
                    if (floorNumber && !isNaN(floorNumber)) {
                        floorInput.value = parseInt(floorNumber);
                    }
                }

                calculatePropertyFee();
            }
        });
    }

    // 自动计算物业费
    function calculatePropertyFee() {
        var areaInput = document.getElementById('id_area');
        var floorInput = document.getElementById('id_floor');
        var propertyFeeInput = document.getElementById('id_property_fee');
        
        if (areaInput && floorInput && propertyFeeInput) {
            var area = parseFloat(areaInput.value) || 0;
            var floor = parseInt(floorInput.value) || 1;
            
            if (area > 0) {
                var baseFee = area * 1; // 每平米1元
                var elevatorFee = floor <= 11 ? area * 0.3 : area * 0.35;
                var totalFee = baseFee + elevatorFee;
                
                propertyFeeInput.value = totalFee.toFixed(2);
            }
        }
    }

    // 楼层输入变化时重新计算
    var floorInput = document.getElementById('id_floor');
    if (floorInput) {
        floorInput.addEventListener('input', calculatePropertyFee);
    }

    // 面积输入变化时重新计算
    if (areaInput) {
        areaInput.addEventListener('input', calculatePropertyFee);
    }

    // 导出功能
    var exportButtons = document.querySelectorAll('.btn-export');
    exportButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            var url = this.getAttribute('data-url');
            var form = document.createElement('form');
            form.method = 'GET';
            form.action = url;
            
            // 添加当前搜索参数
            var searchParams = new URLSearchParams(window.location.search);
            searchParams.forEach(function(value, key) {
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            });
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        });
    });

    // 数据表格排序
    var sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(function(header) {
        header.addEventListener('click', function() {
            var field = this.getAttribute('data-field');
            var currentOrder = this.getAttribute('data-order') || 'asc';
            var newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
            
            var url = new URL(window.location);
            url.searchParams.set('order_by', field);
            url.searchParams.set('order', newOrder);
            
            window.location.href = url.toString();
        });
    });

    // 页面加载动画
    var loadingElements = document.querySelectorAll('.loading');
    loadingElements.forEach(function(element) {
        setTimeout(function() {
            element.style.display = 'none';
        }, 1000);
    });
});

// 工具函数
function formatCurrency(amount) {
    return '¥' + parseFloat(amount).toFixed(2);
}

function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

function showLoading(button) {
    var originalText = button.innerHTML;
    button.innerHTML = '<span class="loading"></span> 处理中...';
    button.disabled = true;
    
    return function() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// AJAX请求封装
function ajaxRequest(url, method, data, callback) {
    var xhr = new XMLHttpRequest();
    xhr.open(method, url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.setRequestHeader('X-CSRFToken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    callback(null, response);
                } catch (e) {
                    callback(e, null);
                }
            } else {
                callback(new Error('请求失败'), null);
            }
        }
    };
    
    xhr.send(data ? JSON.stringify(data) : null);
}
