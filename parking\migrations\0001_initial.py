# Generated by Django 5.2.3 on 2025-06-19 01:59

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ParkingSpace',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租户姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('parking_number', models.CharField(max_length=20, verbose_name='车位号')),
                ('license_plate', models.CharField(max_length=20, verbose_name='车牌号')),
                ('lease_start_date', models.DateField(verbose_name='租车位时间')),
                ('property_fee_due_date', models.DateField(verbose_name='物业费到期时间')),
                ('property_fee', models.DecimalField(decimal_places=2, default=Decimal('20.00'), max_digits=10, verbose_name='物业费用')),
                ('status', models.CharField(choices=[('active', '正常'), ('overdue', '逾期'), ('checkout', '已退车位')], default='active', max_length=10, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '车位',
                'verbose_name_plural': '车位',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ParkingPaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='交费时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='缴费金额')),
                ('fee_start_date', models.DateField(verbose_name='物业费开始时间')),
                ('fee_end_date', models.DateField(verbose_name='物业费结束时间')),
                ('payment_method', models.CharField(default='现金', max_length=20, verbose_name='缴费方式')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('parking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='parking.parkingspace', verbose_name='车位')),
            ],
            options={
                'verbose_name': '车位缴费记录',
                'verbose_name_plural': '车位缴费记录',
                'ordering': ['-payment_date'],
            },
        ),
    ]
