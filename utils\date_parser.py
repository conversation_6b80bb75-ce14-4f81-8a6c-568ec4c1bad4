# utils/date_parser.py - 通用日期解析工具
from datetime import datetime, date, timedelta
from django.utils import timezone


def parse_date(date_value, default_date=None, date_formats=None):
    """
    统一的日期解析函数
    
    Args:
        date_value: 要解析的日期值（可以是字符串、datetime、date对象或None）
        default_date: 默认日期（如果解析失败或值为空时使用）
        date_formats: 支持的日期格式列表
    
    Returns:
        date: 解析后的日期对象
    """
    if date_formats is None:
        date_formats = [
            '%Y-%m-%d',      # 2024-01-01
            '%Y/%m/%d',      # 2024/01/01
            '%Y.%m.%d',      # 2024.01.01
            '%d/%m/%Y',      # 01/01/2024
            '%d-%m-%Y',      # 01-01-2024
            '%d.%m.%Y',      # 01.01.2024
        ]
    
    # 如果值为空，返回默认日期
    if not date_value:
        return default_date or timezone.now().date()
    
    # 如果是字符串，尝试解析
    if isinstance(date_value, str):
        date_value = date_value.strip()
        if not date_value:
            return default_date or timezone.now().date()
        
        # 尝试不同的日期格式
        for date_format in date_formats:
            try:
                return datetime.strptime(date_value, date_format).date()
            except ValueError:
                continue
        
        # 如果所有格式都失败，返回默认日期
        return default_date or timezone.now().date()
    
    # 如果是datetime对象，转换为date
    elif hasattr(date_value, 'date'):
        return date_value.date()
    
    # 如果已经是date对象，直接返回
    elif hasattr(date_value, 'year') and hasattr(date_value, 'month') and hasattr(date_value, 'day'):
        return date_value
    
    # 其他情况返回默认日期
    else:
        return default_date or timezone.now().date()


def parse_datetime(datetime_value, default_datetime=None, datetime_formats=None):
    """
    统一的日期时间解析函数
    
    Args:
        datetime_value: 要解析的日期时间值
        default_datetime: 默认日期时间
        datetime_formats: 支持的日期时间格式列表
    
    Returns:
        datetime: 解析后的日期时间对象（带时区信息）
    """
    if datetime_formats is None:
        datetime_formats = [
            '%Y-%m-%d %H:%M:%S',     # 2024-01-01 12:00:00
            '%Y-%m-%d %H:%M',        # 2024-01-01 12:00
            '%Y-%m-%d',              # 2024-01-01 (自动添加00:00:00)
            '%Y/%m/%d %H:%M:%S',     # 2024/01/01 12:00:00
            '%Y/%m/%d %H:%M',        # 2024/01/01 12:00
            '%Y/%m/%d',              # 2024/01/01 (自动添加00:00:00)
        ]
    
    # 如果值为空，返回默认日期时间
    if not datetime_value:
        return default_datetime or timezone.now()
    
    # 如果是字符串，尝试解析
    if isinstance(datetime_value, str):
        datetime_value = datetime_value.strip()
        if not datetime_value:
            return default_datetime or timezone.now()
        
        # 尝试不同的日期时间格式
        for datetime_format in datetime_formats:
            try:
                dt = datetime.strptime(datetime_value, datetime_format)
                # 确保返回带时区信息的datetime
                return timezone.make_aware(dt, timezone.get_current_timezone())
            except ValueError:
                continue
        
        # 如果所有格式都失败，返回默认日期时间
        return default_datetime or timezone.now()
    
    # 如果已经是datetime对象
    elif isinstance(datetime_value, datetime):
        # 如果没有时区信息，添加时区
        if timezone.is_naive(datetime_value):
            return timezone.make_aware(datetime_value, timezone.get_current_timezone())
        return datetime_value
    
    # 如果是date对象，转换为datetime
    elif isinstance(datetime_value, date):
        dt = datetime.combine(datetime_value, datetime.min.time())
        return timezone.make_aware(dt, timezone.get_current_timezone())
    
    # 其他情况返回默认日期时间
    else:
        return default_datetime or timezone.now()


def validate_date_range(start_date, end_date):
    """
    验证日期范围
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not start_date or not end_date:
        return True, None
    
    try:
        start = parse_date(start_date)
        end = parse_date(end_date)
        
        if start > end:
            return False, "开始日期不能晚于结束日期"
        
        # 检查日期范围是否合理（不超过10年）
        if (end - start).days > 3650:
            return False, "日期范围不能超过10年"
        
        return True, None
        
    except Exception as e:
        return False, f"日期格式错误: {str(e)}"


def format_date_for_display(date_value, format_string='%Y-%m-%d'):
    """
    格式化日期用于显示
    
    Args:
        date_value: 日期值
        format_string: 格式字符串
    
    Returns:
        str: 格式化后的日期字符串
    """
    if not date_value:
        return ''
    
    try:
        parsed_date = parse_date(date_value)
        return parsed_date.strftime(format_string)
    except Exception:
        return str(date_value)


def get_date_range_for_month(year, month):
    """
    获取指定年月的日期范围
    
    Args:
        year: 年份
        month: 月份
    
    Returns:
        tuple: (month_start, month_end)
    """
    try:
        # 月初
        month_start = date(year, month, 1)
        
        # 月末
        if month == 12:
            month_end = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            month_end = date(year, month + 1, 1) - timedelta(days=1)
        
        return month_start, month_end
        
    except ValueError:
        # 如果日期无效，返回当前月份
        today = timezone.now().date()
        return get_date_range_for_month(today.year, today.month)


def calculate_months_between(start_date, end_date):
    """
    计算两个日期之间的月数
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        int: 月数
    """
    try:
        start = parse_date(start_date)
        end = parse_date(end_date)
        
        return (end.year - start.year) * 12 + (end.month - start.month)
        
    except Exception:
        return 0


# 常用的日期格式常量
DATE_FORMATS = {
    'STANDARD': '%Y-%m-%d',           # 2024-01-01
    'SLASH': '%Y/%m/%d',              # 2024/01/01
    'DOT': '%Y.%m.%d',                # 2024.01.01
    'CHINESE': '%Y年%m月%d日',         # 2024年01月01日
    'DISPLAY': '%Y年%m月%d日',         # 用于显示的中文格式
}

DATETIME_FORMATS = {
    'STANDARD': '%Y-%m-%d %H:%M:%S',  # 2024-01-01 12:00:00
    'SHORT': '%Y-%m-%d %H:%M',        # 2024-01-01 12:00
    'CHINESE': '%Y年%m月%d日 %H:%M',   # 2024年01月01日 12:00
}
