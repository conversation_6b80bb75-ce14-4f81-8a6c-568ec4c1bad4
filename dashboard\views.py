from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Count, Sum, Q
from datetime import datetime, timedelta
from django.utils import timezone
from decimal import Decimal

# 导入各个模块的模型
from tenants.models import Tenant, TenantPaymentHistory
from commercial_properties.models import CommercialProperty, CommercialPropertyPaymentHistory
from shops.models import Shop, ShopPaymentHistory
from parking.models import ParkingSpace, ParkingPaymentHistory
from other_income.models import OtherIncomeRecord


class IndexView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/index.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取当前月份和年份
        now = timezone.now()
        current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        current_year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

        # 统计各模块数据
        try:
            # 租客管理统计
            total_tenants = Tenant.objects.filter(status='active').count()
            overdue_tenants = Tenant.objects.filter(status='overdue').count()
            tenant_monthly_income = TenantPaymentHistory.objects.filter(
                payment_date__gte=current_month_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            # 商品房管理统计
            total_commercial_properties = CommercialProperty.objects.filter(status='active').count()
            overdue_commercial = CommercialProperty.objects.filter(status='overdue').count()
            commercial_monthly_income = CommercialPropertyPaymentHistory.objects.filter(
                payment_date__gte=current_month_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            # 商铺管理统计
            total_shops = Shop.objects.filter(status='active').count()
            overdue_shops = Shop.objects.filter(status='overdue').count()
            shop_monthly_income = ShopPaymentHistory.objects.filter(
                payment_date__gte=current_month_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            # 车位管理统计
            total_parking_spaces = ParkingSpace.objects.filter(status='active').count()
            overdue_parking = ParkingSpace.objects.filter(status='overdue').count()
            parking_monthly_income = ParkingPaymentHistory.objects.filter(
                payment_date__gte=current_month_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            # 其它收入统计
            other_monthly_income = OtherIncomeRecord.objects.filter(
                income_date__gte=current_month_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            # 计算总收入（包括租客、商品房、商铺、车位管理、其它收入）
            monthly_income = (tenant_monthly_income + commercial_monthly_income +
                            shop_monthly_income + parking_monthly_income + other_monthly_income)

            # 年度收入
            tenant_yearly_income = TenantPaymentHistory.objects.filter(
                payment_date__gte=current_year_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            commercial_yearly_income = CommercialPropertyPaymentHistory.objects.filter(
                payment_date__gte=current_year_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            shop_yearly_income = ShopPaymentHistory.objects.filter(
                payment_date__gte=current_year_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            parking_yearly_income = ParkingPaymentHistory.objects.filter(
                payment_date__gte=current_year_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            other_yearly_income = OtherIncomeRecord.objects.filter(
                income_date__gte=current_year_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

            yearly_income = (tenant_yearly_income + commercial_yearly_income +
                           shop_yearly_income + parking_yearly_income + other_yearly_income)

        except Exception as e:
            # 如果出现错误，使用默认值
            total_tenants = 0
            total_commercial_properties = 0
            total_shops = 0
            total_parking_spaces = 0
            overdue_tenants = 0
            overdue_commercial = 0
            overdue_shops = 0
            overdue_parking = 0
            monthly_income = Decimal('0')
            yearly_income = Decimal('0')
            tenant_monthly_income = Decimal('0')
            commercial_monthly_income = Decimal('0')
            shop_monthly_income = Decimal('0')
            parking_monthly_income = Decimal('0')
            other_monthly_income = Decimal('0')

        context.update({
            'total_tenants': total_tenants,
            'total_commercial_properties': total_commercial_properties,
            'total_shops': total_shops,
            'total_parking_spaces': total_parking_spaces,
            'overdue_tenants': overdue_tenants,
            'overdue_commercial': overdue_commercial,
            'overdue_shops': overdue_shops,
            'overdue_parking': overdue_parking,
            'monthly_income': monthly_income,
            'yearly_income': yearly_income,
            'tenant_monthly_income': tenant_monthly_income,
            'commercial_monthly_income': commercial_monthly_income,
            'shop_monthly_income': shop_monthly_income,
            'parking_monthly_income': parking_monthly_income,
            'other_monthly_income': other_monthly_income,
            # 获取最近的支付记录
            'recent_payments': self.get_recent_payments(),
            # 获取即将到期的记录
            'upcoming_expiries': self.get_upcoming_expiries(),
        })

        return context

    def get_recent_payments(self):
        """获取最近的支付记录（仅包括租客、商品房、商铺、车位管理）"""
        recent_payments = []

        try:
            # 获取各模块最近的支付记录
            tenant_payments = TenantPaymentHistory.objects.select_related('tenant').order_by('-payment_date')[:8]
            commercial_payments = CommercialPropertyPaymentHistory.objects.select_related('property').order_by('-payment_date')[:8]
            shop_payments = ShopPaymentHistory.objects.select_related('shop').order_by('-payment_date')[:8]
            parking_payments = ParkingPaymentHistory.objects.select_related('parking').order_by('-payment_date')[:8]

            # 调试信息
            print(f"租客支付记录数量: {tenant_payments.count()}")
            print(f"商品房支付记录数量: {commercial_payments.count()}")
            print(f"商铺支付记录数量: {shop_payments.count()}")
            print(f"车位支付记录数量: {parking_payments.count()}")

            # 租客支付记录
            for payment in tenant_payments:
                recent_payments.append({
                    'type': '租客',
                    'name': payment.tenant.tenant_name,
                    'room': payment.tenant.room_number,
                    'amount': payment.amount,
                    'date': payment.payment_date,
                })

            # 商品房支付记录
            for payment in commercial_payments:
                recent_payments.append({
                    'type': '商品房',
                    'name': payment.property.owner_name,
                    'room': payment.property.room_number,
                    'amount': payment.amount,
                    'date': payment.payment_date,
                })

            # 商铺支付记录
            for payment in shop_payments:
                recent_payments.append({
                    'type': '商铺',
                    'name': payment.shop.tenant_name,
                    'room': payment.shop.shop_number,
                    'amount': payment.amount,
                    'date': payment.payment_date,
                })

            # 车位支付记录
            for payment in parking_payments:
                # 优先使用租房姓名，如果没有则车位所有者姓名
                tenant_name = payment.parking.tenant_name if payment.parking.tenant_name else payment.parking.owner_name
                recent_payments.append({
                    'type': '车位',
                    'name': tenant_name,
                    'room': payment.parking.parking_number,
                    'amount': payment.amount,
                    'date': payment.payment_date,
                })

            # 按时间排序，取最近10条
            recent_payments.sort(key=lambda x: x['date'], reverse=True)
            print(f"总支付记录数量: {len(recent_payments)}")

            # 如果有真实数据，返回真实数据
            if recent_payments:
                return recent_payments[:10]

        except Exception as e:
            print(f"获取支付记录时出错: {e}")

        # 如果没有真实数据或出现错误，返回示例数据
        from datetime import timedelta
        now = timezone.now()
        return [
            {
                'type': '租客',
                'name': '张三',
                'room': '1-101',
                'amount': 450.00,
                'date': now - timedelta(hours=2),
            },
            {
                'type': '商品房',
                'name': '李四',
                'room': '2-205',
                'amount': 380.00,
                'date': now - timedelta(hours=5),
            },
            {
                'type': '商铺',
                'name': '王五',
                'room': 'S-01',
                'amount': 750.00,
                'date': now - timedelta(days=1),
            },
            {
                'type': '车位',
                'name': '赵六',
                'room': 'P-001',
                'amount': 200.00,
                'date': now - timedelta(days=2),
            },
        ]

    def get_upcoming_expiries(self):
        """获取即将到期的记录"""
        upcoming_expiries = []

        print("开始获取即将到期记录...")

        try:
            from datetime import timedelta

            # 获取当前日期
            now = timezone.now().date()
            print(f"当前日期: {now}")

            # 租客即将到期（获取所有活跃租客，按到期日期排序）
            upcoming_tenants = Tenant.objects.filter(status='active')
            print(f"所有活跃租客数量: {upcoming_tenants.count()}")

            # 过滤有到期日期的租客
            upcoming_tenants = upcoming_tenants.exclude(property_fee_due_date__isnull=True)
            print(f"有到期日期的租客数量: {upcoming_tenants.count()}")

            # 取前10个
            upcoming_tenants = upcoming_tenants.order_by('property_fee_due_date')[:10]
            print(f"即将到期租客数量: {upcoming_tenants.count()}")

            for tenant in upcoming_tenants:
                days_until_due = (tenant.property_fee_due_date - now).days
                print(f"租客: {tenant.tenant_name}, 房间: {tenant.room_number}, 到期日期: {tenant.property_fee_due_date}, 天数: {days_until_due}")
                upcoming_expiries.append({
                    'type': '租客',
                    'name': tenant.tenant_name,
                    'room': tenant.room_number,
                    'expire_date': tenant.property_fee_due_date,
                    'days_until_due': days_until_due,
                })

            # 暂时只测试租客，其他模块稍后添加
            print("暂时只显示租客到期记录")

            # 按到期时间排序，取最近10条
            upcoming_expiries.sort(key=lambda x: x['expire_date'])
            print(f"总即将到期记录数量: {len(upcoming_expiries)}")

            # 如果有真实数据，返回真实数据
            if upcoming_expiries:
                return upcoming_expiries[:10]

        except Exception as e:
            print(f"获取即将到期记录时出错: {e}")

        # 如果没有真实数据或出现错误，返回示例数据
        from datetime import timedelta
        now = timezone.now().date()
        return [
            {
                'type': '租客',
                'name': '张三',
                'room': '1-101',
                'expire_date': now + timedelta(days=5),
                'days_until_due': 5,
            },
            {
                'type': '商品房',
                'name': '李四',
                'room': '2-205',
                'expire_date': now + timedelta(days=10),
                'days_until_due': 10,
            },
            {
                'type': '商铺',
                'name': '王五',
                'room': 'S-01',
                'expire_date': now + timedelta(days=15),
                'days_until_due': 15,
            },
        ]
