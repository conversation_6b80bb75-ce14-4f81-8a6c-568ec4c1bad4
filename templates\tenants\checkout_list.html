{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客退房列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-out-alt me-2 text-warning"></i>租客退房列表
                        <span class="badge bg-warning ms-2">{{ total_checkout_count }} 户</span>
                    </h4>
                </div>



                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <!-- 所有按钮放在同一行 -->
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-1"></i>返回租客列表
                                    </a>
                                    <a href="{% url 'tenants:overdue' %}" class="btn btn-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>物业费逾期列表
                                    </a>
                                    <a href="{% url 'tenants:payment_history' %}" class="btn btn-success">
                                        <i class="fas fa-history me-1"></i>物业费流水
                                    </a>
                                    <a href="{% url 'tenants:export_checkout' %}" class="btn btn-info">
                                        <i class="fas fa-download me-1"></i>导出退房数据
                                    </a>
                                    <button type="button" class="btn btn-danger" onclick="showBatchDeleteModal()" disabled id="checkout-batch-delete">
                                        <i class="fas fa-trash me-1"></i>批量删除
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex justify-content-end gap-2">
                                    <div class="search-box" style="width: 200px;">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    {{ search_form.search_type }}
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'tenants:checkout' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="select-all" class="form-check-input">
                                        </th>
                                        <th>楼号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>租客姓名</th>
                                        <th>身份证号</th>
                                        <th>租客电话</th>
                                        <th>房东姓名</th>
                                        <th>房东电话</th>
                                        <th>入住时间</th>
                                        <th>退房日期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input">
                                        </th>
                                        <th>楼号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>租客姓名</th>
                                        <th>身份证号</th>
                                        <th>租客电话</th>
                                        <th>房东姓名</th>
                                        <th>房东电话</th>
                                        <th>入住时间</th>
                                        <th>退房日期</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for tenant in tenants %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input item-checkbox" value="{{ tenant.pk }}">
                                        </td>
                                        <td>{{ tenant.building_number }}</td>
                                        <td>{{ tenant.room_number }}</td>
                                        <td>{{ tenant.area }}㎡</td>
                                        <td class="text-center name-cell">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="status-indicator" style="background-color: #6c757d;"></div>
                                                {{ tenant.tenant_name }}
                                            </div>
                                        </td>
                                        <td>{{ tenant.id_card }}</td>
                                        <td class="phone-cell">{{ tenant.get_formatted_phone|safe }}</td>
                                        <td class="name-cell">{{ tenant.landlord_name }}</td>
                                        <td>{{ tenant.landlord_phone }}</td>
                                        <td>{{ tenant.move_in_date }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ tenant.updated_at|date:"Y-m-d" }}</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons-container">
                                                <button type="button" class="action-btn restore-btn" title="恢复租客"
                                                        onclick="restoreTenant({{ tenant.pk }})">
                                                    <i class="fas fa-undo"></i>
                                                    <span>恢复</span>
                                                </button>
                                                <button type="button" class="action-btn delete-btn single-delete-btn"
                                                        data-tenant-id="{{ tenant.pk }}"
                                                        data-tenant-name="{{ tenant.tenant_name }}"
                                                        title="彻底删除">
                                                    <i class="fas fa-trash"></i>
                                                    <span>删除</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="12" class="text-center py-4">
                                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                            <p class="text-muted">太好了！目前没有退房的租客</p>
                                            <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                                <i class="fas fa-list me-1"></i>查看所有租客
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    {% if tenants %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-secondary">
                                <h6 class="alert-heading">
                                    <i class="fas fa-chart-bar me-2"></i>退房统计
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>总退房数：</strong>{{ total_checkout_count }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>本月退房：</strong>{{ this_month_checkout_count }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>最近退房：</strong>
                                        {% if latest_checkout %}
                                            {{ latest_checkout|date:"Y-m-d" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>退房率：</strong>
                                        <span class="badge bg-info">{{ checkout_rate }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量删除确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="selectedCount">0</span> 个退房租客吗？</p>
                <p class="text-danger">⚠️ 警告：此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="batchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 单个删除模态框 -->
<div class="modal fade" id="singleDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除租客 <span id="tenantNameToDelete"></span> 吗？</p>
                <p class="text-danger">⚠️ 警告：此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="singleDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    /* 电话号码显示样式 */
    .phone-cell {
        line-height: 1.2;
        vertical-align: top;
        padding: 8px 12px;
    }

    .phone-number {
        margin: 1px 0;
        word-break: break-all;
    }

    .phone-number.primary {
        font-weight: 500;
        color: #333;
    }

    .phone-number.secondary {
        font-size: 0.9em;
        color: #666;
        margin-top: 2px;
    }

    /* 确保表格行高度自适应 */
    .table td {
        vertical-align: top;
    }
    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1300px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1300px;
    }

    /* 表格容器间距最小化 */
    .table-container {
        margin-bottom: 0 !important;
    }

    /* 消除表头和表体之间的空隙 */
    .table-header-fixed {
        margin: 0 !important;
        padding: 0 !important;
    }

    .table-body-scroll {
        margin: 0 !important;
        padding: 0 !important;
        border-top: 1px solid #667eea !important;
    }

    #body-table thead {
        margin: 0 !important;
        padding: 0 !important;
        height: 0 !important;
        line-height: 0 !important;
    }

    #body-table thead th {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        line-height: 0 !important;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        font-size: 0.9rem;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    .table-body-scroll tr:hover {
        background-color: #f8f9fa;
    }

    .table-body-scroll tr:last-child td {
        border-bottom: none;
    }

    /* 列宽设置 */
    .table-header-fixed th:nth-child(1),
    .table-body-scroll td:nth-child(1) { width: 50px; }
    .table-header-fixed th:nth-child(2),
    .table-body-scroll td:nth-child(2) { width: 80px; }
    .table-header-fixed th:nth-child(3),
    .table-body-scroll td:nth-child(3) { width: 80px; }
    .table-header-fixed th:nth-child(4),
    .table-body-scroll td:nth-child(4) { width: 80px; }
    .table-header-fixed th:nth-child(5),
    .table-body-scroll td:nth-child(5) { width: 120px; }
    .table-header-fixed th:nth-child(6),
    .table-body-scroll td:nth-child(6) { width: 180px; }
    .table-header-fixed th:nth-child(7),
    .table-body-scroll td:nth-child(7) { width: 120px; }
    .table-header-fixed th:nth-child(8),
    .table-body-scroll td:nth-child(8) { width: 120px; }
    .table-header-fixed th:nth-child(9),
    .table-body-scroll td:nth-child(9) { width: 120px; }
    .table-header-fixed th:nth-child(10),
    .table-body-scroll td:nth-child(10) { width: 120px; }
    .table-header-fixed th:nth-child(11),
    .table-body-scroll td:nth-child(11) { width: 120px; }
    .table-header-fixed th:nth-child(12),
    .table-body-scroll td:nth-child(12) { width: 120px; }

    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    .toolbar {
        margin-bottom: 20px;
    }

    .btn-group .btn {
        border-radius: 0;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 0.375rem;
        border-bottom-left-radius: 0.375rem;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 0.375rem;
        border-bottom-right-radius: 0.375rem;
    }

    .btn-group + .btn-group {
        margin-left: 0.5rem;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }

        .btn-group {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 0.5rem;
        }

        .btn-group .btn {
            flex: 1;
            min-width: 120px;
            margin-bottom: 0.25rem;
        }
    }

    /* 租客管理专用渐变流光按钮风格 */
    .action-buttons-container {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        padding: 12px;
        position: relative;
        z-index: 200;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
    }

    .action-btn {
        position: relative;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 10px 16px;
        border-radius: 25px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        min-width: 80px;
        min-height: 40px;
        overflow: hidden;
        white-space: nowrap;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 300% 300%;
        color: white;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        box-shadow:
            0 6px 20px rgba(102, 126, 234, 0.3),
            0 3px 10px rgba(118, 75, 162, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        animation: gradientFlow 4s ease infinite;
        transform: translateZ(0);
        z-index: 201;
    }

    @keyframes gradientFlow {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .action-btn i {
        font-size: 14px;
        margin-right: 6px;
        transition: all 0.4s ease;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        opacity: 0.95;
    }

    .action-btn span {
        font-size: 12px;
        font-weight: 700;
        position: relative;
        z-index: 2;
        transition: all 0.4s ease;
        text-transform: none;
        letter-spacing: 0.5px;
        white-space: nowrap;
    }

    .action-btn:hover {
        transform: translateY(-3px) scale(1.05);
        text-decoration: none;
        box-shadow:
            0 12px 35px rgba(102, 126, 234, 0.4),
            0 6px 20px rgba(118, 75, 162, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        animation: gradientFlow 2s ease infinite, streamingLight 1.5s ease infinite;
    }

    @keyframes streamingLight {
        0% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.4),
                0 6px 20px rgba(118, 75, 162, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
        50% {
            box-shadow:
                0 12px 35px rgba(245, 87, 108, 0.4),
                0 6px 20px rgba(240, 147, 251, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }
        100% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.4),
                0 6px 20px rgba(118, 75, 162, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
    }

    .action-btn:hover i {
        transform: scale(1.15) rotate(8deg);
        filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.3));
        opacity: 1;
    }

    .action-btn:hover span {
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        transform: translateY(-1px);
    }

    .action-btn:active {
        transform: translateY(-1px) scale(1.02);
        transition: all 0.1s ease;
        box-shadow:
            0 6px 20px rgba(102, 126, 234, 0.3),
            0 3px 10px rgba(118, 75, 162, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* 恢复按钮 - 青蓝流光渐变 */
    .restore-btn {
        background: linear-gradient(45deg, #06b6d4, #0ea5e9, #3b82f6, #8b5cf6);
        background-size: 300% 300%;
        animation: gradientFlow 3.2s ease infinite;
    }

    .restore-btn:hover {
        background: linear-gradient(45deg, #06b6d4, #0ea5e9, #3b82f6, #8b5cf6);
        background-size: 300% 300%;
        animation: gradientFlow 1.6s ease infinite, restoreGlow 2s ease-in-out infinite alternate;
    }

    @keyframes restoreGlow {
        from {
            box-shadow:
                0 12px 35px rgba(6, 182, 212, 0.4),
                0 6px 20px rgba(14, 165, 233, 0.3),
                0 0 20px rgba(59, 130, 246, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(6, 182, 212, 0.6),
                0 6px 20px rgba(14, 165, 233, 0.5),
                0 0 30px rgba(139, 92, 246, 0.7);
        }
    }

    /* 删除按钮 - 红粉流光渐变 */
    .delete-btn {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientFlow 2.8s ease infinite;
    }

    .delete-btn:hover {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientFlow 1.4s ease infinite, deleteGlow 2s ease-in-out infinite alternate;
    }

    @keyframes deleteGlow {
        from {
            box-shadow:
                0 12px 35px rgba(239, 68, 68, 0.4),
                0 6px 20px rgba(244, 63, 94, 0.3),
                0 0 20px rgba(236, 72, 153, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(239, 68, 68, 0.6),
                0 6px 20px rgba(244, 63, 94, 0.5),
                0 0 30px rgba(217, 70, 239, 0.7);
        }
    }

    /* 流光扫过效果 */
    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
        border-radius: 25px;
        z-index: 1;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    /* 内部高光效果 */
    .action-btn::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        height: 50%;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
        border-radius: 23px 23px 0 0;
        z-index: 2;
    }

    /* 流光脉冲效果 */
    .action-btn:active {
        animation: lightPulse 0.3s ease-out;
    }

    @keyframes lightPulse {
        0% {
            box-shadow:
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 3px 10px rgba(118, 75, 162, 0.2);
        }
        50% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.6),
                0 6px 20px rgba(118, 75, 162, 0.4),
                0 0 30px rgba(240, 147, 251, 0.5);
        }
        100% {
            box-shadow:
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 3px 10px rgba(118, 75, 162, 0.2);
        }
    }

    /* 恢复按钮 - 青蓝渐变流光 */
    .restore-btn {
        background: linear-gradient(45deg, #06b6d4, #0ea5e9, #3b82f6, #8b5cf6);
        background-size: 300% 300%;
        animation: gradientShift 3.2s ease infinite;
    }

    .restore-btn:hover {
        animation: gradientShift 1.6s ease infinite, restoreGlow 2s ease-in-out infinite alternate;
    }

    @keyframes restoreGlow {
        from { box-shadow: 0 15px 35px rgba(6, 182, 212, 0.4), 0 8px 25px rgba(14, 165, 233, 0.3), 0 0 20px rgba(59, 130, 246, 0.5); }
        to { box-shadow: 0 15px 35px rgba(6, 182, 212, 0.6), 0 8px 25px rgba(14, 165, 233, 0.5), 0 0 30px rgba(139, 92, 246, 0.7); }
    }

    /* 删除按钮 - 红粉渐变流光 */
    .delete-btn {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientShift 2.8s ease infinite;
    }

    .delete-btn:hover {
        animation: gradientShift 1.4s ease infinite, deleteGlow 2s ease-in-out infinite alternate;
    }

    @keyframes deleteGlow {
        from { box-shadow: 0 15px 35px rgba(239, 68, 68, 0.4), 0 8px 25px rgba(244, 63, 94, 0.3), 0 0 20px rgba(236, 72, 153, 0.5); }
        to { box-shadow: 0 15px 35px rgba(239, 68, 68, 0.6), 0 8px 25px rgba(244, 63, 94, 0.5), 0 0 30px rgba(217, 70, 239, 0.7); }
    }



    /* 响应式设计 */
    @media (max-width: 768px) {
        .action-buttons-container {
            gap: 6px;
        }

        .action-btn {
            padding: 6px 12px;
            min-width: 60px;
            font-size: 11px;
        }

        .action-btn i {
            font-size: 13px;
            margin-right: 4px;
        }

        .action-btn span {
            font-size: 10px;
        }
    }

    /* 强制姓名单元格内容完全显示 */
    .table td.name-cell {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4 !important;
    }

    /* 确保姓名单元格内的div也支持换行 */
    .table td.name-cell div {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4 !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头表体列宽
        syncTableColumns();

        // 初始化复选框和按钮
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const batchDeleteBtn = document.getElementById('checkout-batch-delete');

        // 全选复选框事件
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                updateBatchDeleteButton();
            });
        }

        // 单个复选框事件
        itemCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                updateSelectAllState();
                updateBatchDeleteButton();
            });
        });

        // 更新全选复选框状态
        function updateSelectAllState() {
            const totalCheckboxes = itemCheckboxes.length;
            const checkedCheckboxes = document.querySelectorAll('.item-checkbox:checked').length;
            
            if (checkedCheckboxes === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes === totalCheckboxes) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 更新批量删除按钮状态
        function updateBatchDeleteButton() {
            if (batchDeleteBtn) {
                const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
                batchDeleteBtn.disabled = checkedCount === 0;
            }
        }

        // 单个删除按钮事件
        document.querySelectorAll('.single-delete-btn').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                const tenantId = this.getAttribute('data-tenant-id');
                const tenantName = this.getAttribute('data-tenant-name');

                // 显示单个删除模态框
                showSingleDeleteModal(tenantId, tenantName);
            });
        });

        // 移除原来的批量删除按钮事件，改为使用模态框
    });

    // 显示批量删除模态框
    function showBatchDeleteModal() {
        const selectedCount = document.querySelectorAll('.item-checkbox:checked').length;
        if (selectedCount === 0) {
            // 不显示任何弹窗，直接返回
            return;
        }

        document.getElementById('selectedCount').textContent = selectedCount;
        const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
        modal.show();
    }

    // 显示单个删除模态框
    let currentTenantId = null;
    function showSingleDeleteModal(tenantId, tenantName) {
        currentTenantId = tenantId;
        document.getElementById('tenantNameToDelete').textContent = tenantName;
        const modal = new bootstrap.Modal(document.getElementById('singleDeleteModal'));
        modal.show();
    }

    // 执行单个删除
    function singleDelete() {
        if (!currentTenantId) return;

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('singleDeleteModal'));
        modal.hide();

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "tenants:batch_delete_checkout" %}';
        form.style.display = 'none';

        // 添加CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // 添加租客ID
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'selected_items';
        input.value = currentTenantId;
        form.appendChild(input);

        document.body.appendChild(form);
        form.submit();
    }

    // 执行批量删除
    function batchDelete() {
        const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        const selectedIds = Array.from(checkedBoxes).map(cb => cb.value);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal'));
        modal.hide();

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "tenants:batch_delete_checkout" %}';
        form.style.display = 'none';

        // 添加CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // 添加选中的ID
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_items';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }

    // 恢复租客功能 - 添加确认提示
    function restoreTenant(tenantId) {
        if (confirm('确定要恢复此租客吗？恢复后租客将重新变为活跃状态。')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/tenants/' + tenantId + '/restore/';

            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // 同步表头表体列宽
    function syncTableColumns() {
        const headerTable = document.getElementById('header-table');
        const bodyTable = document.getElementById('body-table');

        if (!headerTable || !bodyTable) {
            console.log('表头或表体表格未找到');
            return;
        }

        const headerCells = headerTable.querySelectorAll('th');
        const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

        // 定义列宽 - 退房列表的列宽配置
        const columnWidths = [
            '50px',   // 选择框
            '80px',   // 楼号
            '80px',   // 房号
            '80px',   // 平米数
            '120px',  // 租客姓名
            '180px',  // 身份证号
            '120px',  // 租客电话
            '120px',  // 房东姓名
            '120px',  // 房东电话
            '120px',  // 入住时间
            '120px',  // 退房日期
            '180px'   // 操作 - 增加宽度以容纳3D浮动卡片按钮
        ];

        // 设置表头列宽
        headerCells.forEach((th, index) => {
            if (index < columnWidths.length) {
                th.style.width = columnWidths[index];
                th.style.minWidth = columnWidths[index];
                th.style.maxWidth = columnWidths[index];
            }
        });

        // 设置表体隐藏表头列宽（用于对齐）
        bodyHeaderCells.forEach((th, index) => {
            if (index < columnWidths.length) {
                th.style.width = columnWidths[index];
                th.style.minWidth = columnWidths[index];
                th.style.maxWidth = columnWidths[index];
            }
        });

        // 确保两个表格宽度一致
        headerTable.style.width = '100%';
        bodyTable.style.width = '100%';
        headerTable.style.minWidth = '1300px';
        bodyTable.style.minWidth = '1300px';

        console.log('✅ 退房列表表头表体列宽同步完成');
    }
</script>
{% endblock %}
