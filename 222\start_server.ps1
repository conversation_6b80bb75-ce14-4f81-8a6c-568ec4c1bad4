# 设置工作目录
Set-Location "E:\dy"

# 创建日志目录
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
}

# 生成日志文件名
$logFile = "logs\server_$(Get-Date -Format 'yyyyMMdd').log"

# 启动进程（隐藏窗口）
$processInfo = New-Object System.Diagnostics.ProcessStartInfo
$processInfo.FileName = "C:\ProgramData\miniforge3\envs\hcd\python.exe"
$processInfo.Arguments = "run_waitress.py"
$processInfo.WorkingDirectory = "E:\dy"
$processInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Hidden
$processInfo.CreateNoWindow = $true
$processInfo.UseShellExecute = $false
$processInfo.RedirectStandardOutput = $true
$processInfo.RedirectStandardError = $true

$process = [System.Diagnostics.Process]::Start($processInfo)

# 重定向输出到日志文件
$process.StandardOutput.ReadToEnd() | Out-File -FilePath $logFile -Append
$process.StandardError.ReadToEnd() | Out-File -FilePath $logFile -Append