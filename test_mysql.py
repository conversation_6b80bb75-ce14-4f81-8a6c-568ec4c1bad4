import MySQLdb

# 测试不同的连接配置
configs = [
    {'host': 'localhost', 'user': 'root', 'password': 'hcd147258', 'port': 3306},
    {'host': '127.0.0.1', 'user': 'root', 'password': 'hcd147258', 'port': 3306},
    {'host': 'localhost', 'user': 'root', 'password': 'hcd147258', 'port': 3307},
    {'host': '127.0.0.1', 'user': 'root', 'password': 'hcd147258', 'port': 3307},
]

for config in configs:
    try:
        print(f"尝试连接: {config}")
        conn = MySQLdb.connect(**config)
        print(f"成功连接MySQL！")

        # 创建数据库
        cursor = conn.cursor()
        cursor.execute("CREATE DATABASE IF NOT EXISTS property_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("数据库 property_management 创建成功")

        cursor.close()
        conn.close()
        break

    except Exception as e:
        print(f"连接失败: {e}")
        continue
else:
    print("所有配置都尝试失败，请手动检查MySQL配置")
