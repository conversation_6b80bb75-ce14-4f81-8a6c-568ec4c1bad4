{% extends 'base/base.html' %}
{% load static %}

{% block title %}物业费到期列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>物业费到期列表
                        <span class="badge bg-danger ms-2">{{ total_overdue }} 户</span>
                    </h4>
                </div>


                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <!-- 所有按钮放在同一行 -->
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-1"></i>返回租客列表
                                    </a>
                                    <a href="{% url 'tenants:checkout' %}" class="btn btn-secondary">
                                        <i class="fas fa-sign-out-alt me-1"></i>退房列表
                                    </a>
                                    <a href="{% url 'tenants:payment_history' %}" class="btn btn-success">
                                        <i class="fas fa-history me-1"></i>物业费流水
                                    </a>
                                    <a href="{% url 'tenants:export_overdue' %}" class="btn btn-info">
                                        <i class="fas fa-download me-1"></i>导出逾期数据
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex justify-content-end gap-2">
                                    <div class="search-box" style="width: 200px;">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    {{ search_form.search_type }}
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'tenants:overdue' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th>楼号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>租客姓名</th>
                                        <th>租客电话</th>
                                        <th>房东姓名</th>
                                        <th>房东电话</th>
                                        <th>物业费到期时间</th>
                                        <th>已逾期天数</th>
                                        <th>月物业费</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th>楼号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>租客姓名</th>
                                        <th>租客电话</th>
                                        <th>房东姓名</th>
                                        <th>房东电话</th>
                                        <th>物业费到期时间</th>
                                        <th>已逾期天数</th>
                                        <th>月物业费</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for tenant in tenants %}
                                <tr class="{% if tenant.overdue_days > 30 %}table-danger{% elif tenant.overdue_days > 7 %}table-warning{% endif %}">
                                    <td>{{ tenant.building_number }}</td>
                                    <td>{{ tenant.room_number }}</td>
                                    <td>{{ tenant.area }}㎡</td>
                                    <td class="text-center name-cell">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="status-indicator status-overdue"></div>
                                            {{ tenant.tenant_name }}
                                        </div>
                                    </td>
                                    <td class="phone-cell">{{ tenant.get_formatted_phone|safe }}</td>
                                    <td class="name-cell">{{ tenant.landlord_name }}</td>
                                    <td>{{ tenant.landlord_phone }}</td>
                                    <td>{{ tenant.property_fee_due_date }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ tenant.overdue_days }}天</span>
                                    </td>
                                    <td class="text-success fw-bold">¥{{ tenant.property_fee }}</td>
                                    <td>
                                        <div class="action-buttons-container">
                                            <a href="{% url 'tenants:renew' tenant.pk %}" class="action-btn renew-btn" title="续费缴费">
                                                <i class="fas fa-money-bill-wave"></i>
                                                <span>续费</span>
                                            </a>
                                            <button type="button" class="action-btn checkout-btn" title="办理退房"
                                                    onclick="checkoutTenant({{ tenant.pk }})">
                                                <i class="fas fa-sign-out-alt"></i>
                                                <span>退房</span>
                                            </button>
                                            <button type="button" class="action-btn delete-btn" title="删除租客"
                                                    onclick="deleteTenant({{ tenant.pk }}, '{{ tenant.tenant_name }}')">
                                                <i class="fas fa-trash"></i>
                                                <span>删除</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">太好了！目前没有逾期的租客</p>
                                        <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>查看所有租客
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    {% if tenants %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-chart-bar me-2"></i>逾期统计
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>总逾期数：</strong>{{ total_overdue }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>逾期7天内：</strong>{{ short_overdue }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>逾期30天以上：</strong>{{ long_overdue }}户
                                    </div>
                                  
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    /* 电话号码显示样式 */
    .phone-cell {
        line-height: 1.2;
        vertical-align: top;
        padding: 8px 12px;
    }

    .phone-number {
        margin: 1px 0;
        word-break: break-all;
    }

    .phone-number.primary {
        font-weight: 500;
        color: #333;
    }

    .phone-number.secondary {
        font-size: 0.9em;
        color: #666;
        margin-top: 2px;
    }

    /* 确保表格行高度自适应 */
    .table td {
        vertical-align: top;
    }
    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin-bottom: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1150px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin-bottom: 0 !important;
        margin-top: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1150px;
    }

    /* 表格容器间距最小化 */
    .table-container {
        margin-bottom: 0 !important;
    }

    #header-table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1150px;
    }

    /* 表体隐藏表头 */
    #body-table thead {
        visibility: hidden;
        height: 0 !important;
        line-height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    #body-table thead th {
        padding: 0 !important;
        height: 0 !important;
        border: none !important;
        margin: 0 !important;
        line-height: 0 !important;
    }

    /* 逾期租客列表列宽设置 - 确保表头和表体对齐 */
    #header-table th:nth-child(1), #body-table td:nth-child(1), #body-table thead th:nth-child(1) { width: 80px; min-width: 80px; } /* 楼号 */
    #header-table th:nth-child(2), #body-table td:nth-child(2), #body-table thead th:nth-child(2) { width: 80px; min-width: 80px; } /* 房号 */
    #header-table th:nth-child(3), #body-table td:nth-child(3), #body-table thead th:nth-child(3) { width: 80px; min-width: 80px; } /* 平米数 */
    #header-table th:nth-child(4), #body-table td:nth-child(4), #body-table thead th:nth-child(4) { width: 100px; min-width: 100px; } /* 租客姓名 */
    #header-table th:nth-child(5), #body-table td:nth-child(5), #body-table thead th:nth-child(5) { width: 120px; min-width: 120px; } /* 租客电话 */
    #header-table th:nth-child(6), #body-table td:nth-child(6), #body-table thead th:nth-child(6) { width: 100px; min-width: 100px; } /* 房东姓名 */
    #header-table th:nth-child(7), #body-table td:nth-child(7), #body-table thead th:nth-child(7) { width: 100px; min-width: 100px; } /* 房东电话 */
    #header-table th:nth-child(8), #body-table td:nth-child(8), #body-table thead th:nth-child(8) { width: 120px; min-width: 120px; } /* 物业费到期时间 */
    #header-table th:nth-child(9), #body-table td:nth-child(9), #body-table thead th:nth-child(9) { width: 90px; min-width: 90px; } /* 已逾期天数 */
    #header-table th:nth-child(10), #body-table td:nth-child(10), #body-table thead th:nth-child(10) { width: 80px; min-width: 80px; } /* 月物业费 */
    #header-table th:nth-child(11), #body-table td:nth-child(11), #body-table thead th:nth-child(11) { width: 200px; min-width: 200px; } /* 操作 */

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        white-space: nowrap;
        padding: 0.25rem 0.5rem;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 0.25rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .info-item i {
        font-size: 0.875rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 表格内容样式 */
    #body-table td {
        padding: 0.6rem 0.5rem;
        text-align: center;
        vertical-align: middle;
        border-bottom: 1px solid #dee2e6;
        border-right: 1px solid #dee2e6;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #body-table td:last-child {
        border-right: none;
    }

    #body-table tr:hover {
        background-color: #f8f9fa;
    }

    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    .toolbar {
        margin-bottom: 20px;
    }

    .btn-group .btn {
        border-radius: 0;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 0.375rem;
        border-bottom-left-radius: 0.375rem;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 0.375rem;
        border-bottom-right-radius: 0.375rem;
    }

    .btn-group + .btn-group {
        margin-left: 0.5rem;
    }

    /* 租客管理专用渐变流光按钮风格 */
    .action-buttons-container {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        padding: 12px;
        position: relative;
        z-index: 200;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
    }

    .action-btn {
        position: relative;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 10px 16px;
        border-radius: 25px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        min-width: 80px;
        min-height: 40px;
        overflow: hidden;
        white-space: nowrap;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 300% 300%;
        color: white;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        box-shadow:
            0 6px 20px rgba(102, 126, 234, 0.3),
            0 3px 10px rgba(118, 75, 162, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        animation: gradientFlow 4s ease infinite;
        transform: translateZ(0);
        z-index: 201;
    }

    @keyframes gradientFlow {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .action-btn i {
        font-size: 14px;
        margin-right: 6px;
        transition: all 0.4s ease;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        opacity: 0.95;
    }

    .action-btn span {
        font-size: 12px;
        font-weight: 700;
        position: relative;
        z-index: 2;
        transition: all 0.4s ease;
        text-transform: none;
        letter-spacing: 0.5px;
        white-space: nowrap;
    }

    .action-btn:hover {
        transform: translateY(-3px) scale(1.05);
        text-decoration: none;
        box-shadow:
            0 12px 35px rgba(102, 126, 234, 0.4),
            0 6px 20px rgba(118, 75, 162, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        animation: gradientFlow 2s ease infinite, streamingLight 1.5s ease infinite;
    }

    @keyframes streamingLight {
        0% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.4),
                0 6px 20px rgba(118, 75, 162, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
        50% {
            box-shadow:
                0 12px 35px rgba(245, 87, 108, 0.4),
                0 6px 20px rgba(240, 147, 251, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }
        100% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.4),
                0 6px 20px rgba(118, 75, 162, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
    }

    .action-btn:hover i {
        transform: scale(1.15) rotate(8deg);
        filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.3));
        opacity: 1;
    }

    .action-btn:hover span {
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        transform: translateY(-1px);
    }

    .action-btn:active {
        transform: translateY(-1px) scale(1.02);
        transition: all 0.1s ease;
        box-shadow:
            0 6px 20px rgba(102, 126, 234, 0.3),
            0 3px 10px rgba(118, 75, 162, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* 续费按钮 - 绿青流光渐变 */
    .renew-btn {
        background: linear-gradient(45deg, #10b981, #06d6a0, #34d399, #6ee7b7);
        background-size: 300% 300%;
        animation: gradientFlow 3.5s ease infinite;
    }

    .renew-btn:hover {
        background: linear-gradient(45deg, #10b981, #06d6a0, #34d399, #6ee7b7);
        background-size: 300% 300%;
        animation: gradientFlow 1.8s ease infinite, renewGlow 2s ease-in-out infinite alternate;
    }

    @keyframes renewGlow {
        from {
            box-shadow:
                0 12px 35px rgba(16, 185, 129, 0.4),
                0 6px 20px rgba(6, 214, 160, 0.3),
                0 0 20px rgba(52, 211, 153, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(16, 185, 129, 0.6),
                0 6px 20px rgba(6, 214, 160, 0.5),
                0 0 30px rgba(110, 231, 183, 0.7);
        }
    }

    /* 删除按钮 - 红粉流光渐变 */
    .delete-btn {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientFlow 2.8s ease infinite;
    }

    .delete-btn:hover {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientFlow 1.4s ease infinite, deleteGlow 2s ease-in-out infinite alternate;
    }

    @keyframes deleteGlow {
        from {
            box-shadow:
                0 12px 35px rgba(239, 68, 68, 0.4),
                0 6px 20px rgba(244, 63, 94, 0.3),
                0 0 20px rgba(236, 72, 153, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(239, 68, 68, 0.6),
                0 6px 20px rgba(244, 63, 94, 0.5),
                0 0 30px rgba(217, 70, 239, 0.7);
        }
    }

    /* 流光扫过效果 */
    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
        border-radius: 25px;
        z-index: 1;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    /* 内部高光效果 */
    .action-btn::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        height: 50%;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
        border-radius: 23px 23px 0 0;
        z-index: 2;
    }

    /* 流光脉冲效果 */
    .action-btn:active {
        animation: lightPulse 0.3s ease-out;
    }

    @keyframes lightPulse {
        0% {
            box-shadow:
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 3px 10px rgba(118, 75, 162, 0.2);
        }
        50% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.6),
                0 6px 20px rgba(118, 75, 162, 0.4),
                0 0 30px rgba(240, 147, 251, 0.5);
        }
        100% {
            box-shadow:
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 3px 10px rgba(118, 75, 162, 0.2);
        }
    }



    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-overdue {
        background-color: #dc3545;
    }

    @media (max-width: 768px) {
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 0.5rem;
        }

        .btn-group .btn {
            flex: 1;
            min-width: 120px;
            margin-bottom: 0.25rem;
        }
    }

    /* 强制姓名单元格内容完全显示 */
    .table td.name-cell {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4 !important;
    }

    /* 确保姓名单元格内的div也支持换行 */
    .table td.name-cell div {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4 !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        
        // 高亮显示严重逾期的行
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach(function(row) {
            const overdueCell = row.querySelector('.badge');
            if (overdueCell) {
                const days = parseInt(overdueCell.textContent);
                if (days > 30) {
                    row.classList.add('table-danger');
                } else if (days > 7) {
                    row.classList.add('table-warning');
                }
            }
        });
    });

    // 退房功能 - 直接提交，无需确认
    function checkoutTenant(tenantId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/tenants/${tenantId}/checkout-action/?next=tenants:overdue`;

        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    }

    // 删除功能 - 直接删除，无需确认
    function deleteTenant(tenantId, tenantName) {
        // 直接跳转到删除页面
        window.location.href = `/tenants/${tenantId}/delete/?next=/tenants/overdue/`;
    }
</script>
{% endblock %}
