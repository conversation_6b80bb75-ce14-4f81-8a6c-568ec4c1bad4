{% extends 'base/base.html' %}
{% load static %}

{% block title %}
    {% if object %}编辑客户{% else %}新增客户{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-user text-primary me-2"></i>
            {% if object %}编辑客户{% else %}新增客户{% endif %}
        </h2>
        <a href="{% url 'water_machine:customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>

    <!-- 表单 -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">姓名 <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">电话 <span class="text-danger">*</span></label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">住址 <span class="text-danger">*</span></label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="text-danger">{{ form.address.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.water_bucket }}
                                <label class="form-check-label" for="{{ form.water_bucket.id_for_label }}">
                                    有水桶
                                </label>
                            </div>
                            {% if form.water_bucket.errors %}
                                <div class="text-danger">{{ form.water_bucket.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'water_machine:customer_list' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if object %}更新{% else %}创建{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
