{% extends 'base/base.html' %}
{% load static %}

{% block title %}逾期商铺列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>逾期商铺列表
                        <span class="badge bg-danger ms-2">{{ page_obj.paginator.count }}家</span>
                    </h4>
                </div>


     

                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 所有按钮放在同一行 -->
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{% url 'shops:list' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-1"></i>返回商铺租房管理
                                    </a>
                                    <a href="{% url 'shops:checkout' %}" class="btn btn-dark">
                                        <i class="fas fa-sign-out-alt me-1"></i>商铺退房列表
                                    </a>
                                    <a href="{% url 'shops:payment_history' %}" class="btn btn-secondary">
                                        <i class="fas fa-history me-1"></i>商铺缴费流水
                                    </a>
                                    <a href="{% url 'shops:export_overdue' %}" class="btn btn-info">
                                        <i class="fas fa-download me-1"></i>导出逾期数据
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'shops:overdue' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th>商铺号</th>
                                        <th>门牌号</th>
                                        <th>租户或业主姓名</th>
                                        <th>身份证号</th>
                                        <th>电话</th>
                                        <th>平米数</th>
                                        <th>业主姓名</th>
                                        <th>业主电话</th>
                                        <th>物业费到期时间</th>
                                        <th>已逾期天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th>商铺号</th>
                                        <th>门牌号</th>
                                        <th>租户或业主姓名</th>
                                        <th>身份证号</th>
                                        <th>电话</th>
                                        <th>平米数</th>
                                        <th>业主姓名</th>
                                        <th>业主电话</th>
                                        <th>物业费到期时间</th>
                                        <th>已逾期天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for shop in shops %}
                                <tr class="{% if shop.overdue_days > 30 %}table-danger{% elif shop.overdue_days > 7 %}table-warning{% endif %}">
                                    <td>{{ shop.shop_number }}</td>
                                    <td>{{ shop.door_number|default:"-" }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator status-overdue"></div>
                                            {{ shop.tenant_name }}
                                        </div>
                                    </td>
                                    <td>{{ shop.id_card }}</td>
                                    <td>{{ shop.phone }}</td>
                                    <td>{{ shop.area }}㎡</td>
                                    <td>{{ shop.owner_name|default:"-" }}</td>
                                    <td>{{ shop.owner_phone|default:"-" }}</td>
                                    <td>{{ shop.property_fee_due_date }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ shop.overdue_days }}天</span>
                                    </td>
                                    <td>
                                        <div class="shop-action-buttons">
                                            <a href="{% url 'shops:renew' shop.pk %}" class="card-btn renew-card" title="续费缴费">
                                                <i class="fas fa-money-bill-wave"></i>
                                                <span>续费</span>
                                            </a>
                                            <form method="post" action="{% url 'shops:checkout_action' shop.pk %}" style="display: inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="card-btn checkout-card" title="办理退房"
                                                        onclick="return confirm('确定要将此商铺标记为退房吗？')">
                                                    <i class="fas fa-sign-out-alt"></i>
                                                    <span>退房</span>
                                                </button>
                                            </form>
                                            <a href="{% url 'shops:delete' shop.pk %}" class="card-btn delete-card" title="删除商铺">
                                                <i class="fas fa-trash"></i>
                                                <span>删除</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">太好了！目前没有逾期的商铺</p>
                                        <a href="{% url 'shops:list' %}" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>查看所有商铺
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    

                    
                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1300px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1000px;
    }

    #body-table thead {
        margin: 0 !important;
        padding: 0 !important;
        height: 0 !important;
        line-height: 0 !important;
    }

    #body-table thead th {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        line-height: 0 !important;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        font-size: 0.9rem;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    .table-body-scroll tr:hover {
        background-color: #f8f9fa;
    }

    .table-body-scroll tr:last-child td {
        border-bottom: none;
    }

    /* 列宽设置 - 逾期商铺表格 */
    .table-header-fixed th:nth-child(1),
    .table-body-scroll td:nth-child(1) { width: 80px; }
    .table-header-fixed th:nth-child(2),
    .table-body-scroll td:nth-child(2) { width: 80px; }
    .table-header-fixed th:nth-child(3),
    .table-body-scroll td:nth-child(3) { width: 120px; }
    .table-header-fixed th:nth-child(4),
    .table-body-scroll td:nth-child(4) { width: 150px; }
    .table-header-fixed th:nth-child(5),
    .table-body-scroll td:nth-child(5) { width: 120px; }
    .table-header-fixed th:nth-child(6),
    .table-body-scroll td:nth-child(6) { width: 80px; }
    .table-header-fixed th:nth-child(7),
    .table-body-scroll td:nth-child(7) { width: 140px; }
    .table-header-fixed th:nth-child(8),
    .table-body-scroll td:nth-child(8) { width: 100px; }
    .table-header-fixed th:nth-child(9),
    .table-body-scroll td:nth-child(9) { width: 130px; }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }
    }

    /* 商铺管理专用立体卡片风格按钮 */
    .shop-action-buttons {
        display: flex;
        gap: 6px;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
        padding: 6px;
    }

    .card-btn {
        position: relative;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        border-radius: 14px;
        text-decoration: none;
        font-size: 11px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        min-width: 65px;
        min-height: 38px;
        overflow: hidden;
        background: linear-gradient(145deg, #ffffff, #f0f0f0);
        box-shadow:
            8px 8px 16px rgba(0, 0, 0, 0.1),
            -8px -8px 16px rgba(255, 255, 255, 0.8),
            inset 2px 2px 4px rgba(255, 255, 255, 0.9),
            inset -2px -2px 4px rgba(0, 0, 0, 0.05);
    }

    .card-btn i {
        font-size: 14px;
        margin-right: 6px;
        transition: all 0.3s ease;
        filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1));
    }

    .card-btn span {
        font-size: 11px;
        font-weight: 700;
        letter-spacing: 0.3px;
        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    }

    .card-btn:hover {
        transform: translateY(-4px) scale(1.05);
        text-decoration: none;
        box-shadow:
            12px 12px 24px rgba(0, 0, 0, 0.15),
            -12px -12px 24px rgba(255, 255, 255, 0.9),
            inset 3px 3px 6px rgba(255, 255, 255, 0.95),
            inset -3px -3px 6px rgba(0, 0, 0, 0.08);
    }

    .card-btn:hover i {
        transform: scale(1.2) rotate(5deg);
    }

    .card-btn:active {
        transform: translateY(-2px) scale(1.02);
        box-shadow:
            4px 4px 8px rgba(0, 0, 0, 0.2),
            -4px -4px 8px rgba(255, 255, 255, 0.7),
            inset 4px 4px 8px rgba(0, 0, 0, 0.1),
            inset -4px -4px 8px rgba(255, 255, 255, 0.9);
    }

    /* 续费按钮 - 绿色主题 */
    .renew-card {
        color: #059669;
        background: linear-gradient(145deg, #d1fae5, #a7f3d0);
    }

    .renew-card:hover {
        color: #047857;
        background: linear-gradient(145deg, #a7f3d0, #6ee7b7);
    }

    .renew-card i {
        color: #059669;
    }

    .renew-card:hover i {
        color: #047857;
        filter: drop-shadow(2px 2px 4px rgba(5, 150, 105, 0.3));
    }

    /* 退房按钮 - 橙色主题 */
    .checkout-card {
        color: #ea580c;
        background: linear-gradient(145deg, #fed7aa, #fdba74);
    }

    .checkout-card:hover {
        color: #c2410c;
        background: linear-gradient(145deg, #fdba74, #fb923c);
    }

    .checkout-card i {
        color: #ea580c;
    }

    .checkout-card:hover i {
        color: #c2410c;
        filter: drop-shadow(2px 2px 4px rgba(234, 88, 12, 0.3));
    }

    /* 删除按钮 - 红色主题 */
    .delete-card {
        color: #dc2626;
        background: linear-gradient(145deg, #fecaca, #fca5a5);
    }

    .delete-card:hover {
        color: #b91c1c;
        background: linear-gradient(145deg, #fca5a5, #f87171);
    }

    .delete-card i {
        color: #dc2626;
    }

    .delete-card:hover i {
        color: #b91c1c;
        filter: drop-shadow(2px 2px 4px rgba(220, 38, 38, 0.3));
    }

    /* 按钮弹跳动画 */
    @keyframes cardBounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(-4px) scale(1.05);
        }
        40% {
            transform: translateY(-8px) scale(1.08);
        }
        60% {
            transform: translateY(-6px) scale(1.06);
        }
    }

    .card-btn:hover {
        animation: cardBounce 0.6s ease-in-out;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .shop-action-buttons {
            gap: 6px;
            flex-wrap: wrap;
        }

        .card-btn {
            padding: 8px 12px;
            min-width: 65px;
            min-height: 40px;
        }

        .card-btn i {
            font-size: 14px;
            margin-right: 6px;
        }

        .card-btn span {
            font-size: 11px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头表体列宽
        syncTableColumns();

        // 同步表头表体列宽函数
        function syncTableColumns() {
            const headerTable = document.getElementById('header-table');
            const bodyTable = document.getElementById('body-table');

            if (!headerTable || !bodyTable) {
                console.log('表头或表体表格未找到');
                return;
            }

            const headerCells = headerTable.querySelectorAll('th');
            const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

            // 定义列宽 - 逾期商铺表格的列宽配置
            const columnWidths = [
                '80px',   // 商铺号
                '120px',  // 门牌号
                '140px',  // 租户或业主姓名
                '180px',  // 身份证号 - 增加宽度
                '120px',  // 电话
                '80px',   // 平米数
                '100px',  // 业主姓名
                '120px',  // 业主电话
                '120px',  // 物业费到期时间 - 减少宽度
                '80px',   // 已逾期天数 - 减少宽度
                '260px'   // 操作
            ];

            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 设置表体隐藏表头列宽（用于对齐）
            bodyHeaderCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 确保两个表格宽度一致
            headerTable.style.width = '100%';
            bodyTable.style.width = '100%';
            headerTable.style.minWidth = '1300px';
            bodyTable.style.minWidth = '1300px';

            console.log('✅ 逾期商铺表头表体列宽同步完成');
        }

        // 窗口大小改变时重新同步列宽
        window.addEventListener('resize', function() {
            setTimeout(syncTableColumns, 50);
        });

        // 高亮显示严重逾期的行
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach(function(row) {
            const overdueCell = row.querySelector('.badge');
            if (overdueCell) {
                const days = parseInt(overdueCell.textContent);
                if (days > 30) {
                    row.classList.add('table-danger');
                } else if (days > 7) {
                    row.classList.add('table-warning');
                }
            }
        });
    });
</script>
{% endblock %}
