{% extends "base/base.html" %}

{% block title %}租户售水机水卡挂失{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
            租户售水机水卡挂失
        </h2>
        <a href="{% url 'water_machine:tenant_water_card_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- 租户信息 -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-1"></i>租户信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>姓名：</strong>{{ tenant_card.name }}</p>
                            <p><strong>住址：</strong>{{ tenant_card.address }}</p>
                            <p><strong>电话：</strong>{{ tenant_card.phone }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>水桶数量：</strong>{{ tenant_card.water_bucket }}桶</p>
                            <p><strong>正常卡号：</strong>
                                {% if tenant_card.normal_card_number %}
                                    <span class="badge bg-success">{{ tenant_card.normal_card_number }}</span>
                                {% else %}
                                    <span class="text-muted">无</span>
                                {% endif %}
                            </p>
                            <p><strong>丢失卡号：</strong>
                                {% if tenant_card.lost_card_numbers %}
                                    <span class="badge bg-warning text-dark">{{ tenant_card.get_lost_card_display }}</span>
                                {% else %}
                                    <span class="text-muted">无</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 挂失表单 -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-1"></i>水卡挂失
                    </h5>
                </div>
                <div class="card-body">
                    {% if not tenant_card.normal_card_number %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            该租户当前没有正常使用的水卡，无法进行挂失操作。
                        </div>
                        <div class="text-center">
                            <a href="{% url 'water_machine:tenant_water_card_edit' tenant_card.pk %}" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i>编辑记录
                            </a>
                        </div>
                    {% else %}
                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="alert alert-warning">
                                <h6><strong>挂失说明：</strong></h6>
                                <ul class="mb-0">
                                    <li>挂失后，原卡号将移至丢失卡号列表</li>
                                    <li>如果提供新卡号，将设置为新的正常卡号</li>
                                    <li>如果不提供新卡号，该租户将暂时没有正常卡号</li>
                                    <li>挂失操作不可撤销，请谨慎操作</li>
                                </ul>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="{{ form.lost_card_number.id_for_label }}" class="form-label">
                                        {{ form.lost_card_number.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.lost_card_number }}
                                    {% if form.lost_card_number.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.lost_card_number.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">{{ form.lost_card_number.help_text }}</div>
                                    
                                    <!-- 当前正常卡号提示 -->
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            当前正常卡号：<span class="badge bg-success">{{ tenant_card.normal_card_number }}</span>
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="{{ form.new_card_number.id_for_label }}" class="form-label">
                                        {{ form.new_card_number.label }}
                                    </label>
                                    {{ form.new_card_number }}
                                    {% if form.new_card_number.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.new_card_number.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">{{ form.new_card_number.help_text }}</div>
                                </div>
                            </div>

                            <!-- 表单错误 -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors.0 }}
                                </div>
                            {% endif %}

                            <!-- 提交按钮 -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'water_machine:tenant_water_card_list' %}" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="submit" class="btn btn-warning" onclick="return confirm('确定要挂失这张水卡吗？此操作不可撤销！')">
                                    <i class="fas fa-exclamation-triangle me-1"></i>确认挂失
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 自动填充当前正常卡号到挂失卡号字段
document.addEventListener('DOMContentLoaded', function() {
    const lostCardInput = document.getElementById('{{ form.lost_card_number.id_for_label }}');
    const currentCardNumber = '{{ tenant_card.normal_card_number }}';
    
    if (lostCardInput && currentCardNumber && !lostCardInput.value) {
        lostCardInput.value = currentCardNumber;
    }
});
</script>
{% endblock %}
