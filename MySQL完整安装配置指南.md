# MySQL 完整安装配置指南

## 目录
1. [MySQL 下载与安装](#mysql-下载与安装)
2. [MySQL 服务配置](#mysql-服务配置)
3. [初始化配置](#初始化配置)
4. [创建数据库](#创建数据库)
5. [用户管理](#用户管理)
6. [常用工具安装](#常用工具安装)
7. [备份配置](#备份配置)
8. [故障排除](#故障排除)

---

## MySQL 下载与安装

### 1. 下载 MySQL

#### 方法一：官方网站下载
1. 访问 [MySQL 官方下载页面](https://dev.mysql.com/downloads/mysql/)
2. 选择 **Windows (x86, 64-bit), ZIP Archive**
3. 下载最新版本（如：mysql-8.4.5-winx64.zip）

#### 方法二：直接下载链接
```
https://dev.mysql.com/get/Downloads/MySQL-8.4/mysql-8.4.5-winx64.zip
```

### 2. 解压安装

```powershell
# 创建 MySQL 目录
New-Item -ItemType Directory -Path "E:\mysql" -Force

# 解压到指定目录
# 解压后路径应为：E:\mysql\mysql-8.4.5-winx64\
```

**目录结构：**
```
E:\mysql\mysql-8.4.5-winx64\
├── bin\          # 可执行文件
├── docs\         # 文档
├── include\      # 头文件
├── lib\          # 库文件
├── share\        # 共享文件
└── LICENSE       # 许可证
```

### 3. 环境变量配置

1. **打开系统环境变量**
   - 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"

2. **添加 PATH 环境变量**
   - 在"系统变量"中找到 `Path`
   - 点击"编辑" → "新建"
   - 添加：`E:\mysql\mysql-8.4.5-winx64\bin`

3. **验证环境变量**
   ```cmd
   # 重新打开命令提示符
   mysql --version
   ```

---

## MySQL 服务配置

### 1. 创建配置文件

在 MySQL 根目录创建 `my.ini` 配置文件：

```ini
[mysqld]
# 设置3306端口
port=3306

# 设置mysql的安装目录
basedir=E:\mysql\mysql-8.4.5-winx64

# 设置mysql数据库的数据的存放目录
datadir=E:\mysql\mysql-8.4.5-winx64\data

# 允许最大连接数
max_connections=200

# 允许连接失败的次数
max_connect_errors=10

# 服务端使用的字符集默认为UTF8
character-set-server=utf8mb4

# 创建新表时将使用的默认存储引擎
default-storage-engine=INNODB

# 默认使用"mysql_native_password"插件认证
default_authentication_plugin=mysql_native_password

[mysql]
# 设置mysql客户端默认字符集
default-character-set=utf8mb4

[client]
# 设置mysql客户端连接服务端时默认使用的端口
port=3306
default-character-set=utf8mb4
```

### 2. 初始化 MySQL

```powershell
# 以管理员身份运行 PowerShell
cd "E:\mysql\mysql-8.4.5-winx64\bin"

# 初始化 MySQL（会生成临时密码）
.\mysqld --initialize --console
```

**重要：** 记录显示的临时密码，格式如：
```
[Note] A temporary password is generated for root@localhost: kof>9Xp#lQ7r
```

### 3. 安装 MySQL 服务

```powershell
# 安装 MySQL 服务
.\mysqld install MySQL84

# 启动 MySQL 服务
net start MySQL84

# 或使用服务管理器启动
services.msc
```

---

## 初始化配置

### 1. 首次登录

```powershell
# 使用临时密码登录
mysql -u root -p
# 输入临时密码：kof>9Xp#lQ7r
```

### 2. 修改 root 密码

```sql
-- 修改 root 密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'hcd147258';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 3. 验证新密码

```powershell
# 使用新密码登录
mysql -u root -p
# 输入新密码：hcd147258
```

### 4. 基本安全配置

```sql
-- 查看当前用户
SELECT USER();

-- 查看所有数据库
SHOW DATABASES;

-- 删除匿名用户
DELETE FROM mysql.user WHERE User='';

-- 删除测试数据库
DROP DATABASE IF EXISTS test;

-- 刷新权限
FLUSH PRIVILEGES;
```

---

## 创建数据库

### 1. 基本语法

```sql
-- 创建数据库
CREATE DATABASE database_name;

-- 创建数据库并指定字符集
CREATE DATABASE database_name 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 查看所有数据库
SHOW DATABASES;

-- 使用数据库
USE database_name;

-- 查看当前数据库
SELECT DATABASE();
```

### 2. 实际示例

```sql
-- 创建物业管理系统数据库
CREATE DATABASE property_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 创建电商系统数据库
CREATE DATABASE ecommerce 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 创建博客系统数据库
CREATE DATABASE blog_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 验证创建结果
SHOW DATABASES;
```

### 3. 创建表示例

```sql
-- 使用数据库
USE property_management;

-- 创建用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(11),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 查看表结构
DESCRIBE users;

-- 插入测试数据
INSERT INTO users (username, email, password, phone) VALUES
('admin', '<EMAIL>', 'hashed_password', '13800138000'),
('user1', '<EMAIL>', 'hashed_password', '13800138001');

-- 查询数据
SELECT * FROM users;
```

---

## 用户管理

### 1. 创建用户

```sql
-- 创建本地用户
CREATE USER 'app_user'@'localhost' IDENTIFIED BY 'strong_password';

-- 创建可远程连接的用户
CREATE USER 'app_user'@'%' IDENTIFIED BY 'strong_password';

-- 查看所有用户
SELECT User, Host FROM mysql.user;
```

### 2. 权限管理

```sql
-- 授予数据库所有权限
GRANT ALL PRIVILEGES ON property_management.* TO 'app_user'@'localhost';

-- 授予特定权限
GRANT SELECT, INSERT, UPDATE, DELETE ON property_management.* TO 'app_user'@'localhost';

-- 授予所有数据库权限（谨慎使用）
GRANT ALL PRIVILEGES ON *.* TO 'app_user'@'localhost';

-- 查看用户权限
SHOW GRANTS FOR 'app_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 3. 删除用户

```sql
-- 删除用户
DROP USER 'app_user'@'localhost';

-- 撤销权限
REVOKE ALL PRIVILEGES ON property_management.* FROM 'app_user'@'localhost';
```

---

## 常用工具安装

### 1. MySQL Workbench（图形化管理工具）

1. **下载安装**
   - 访问：https://dev.mysql.com/downloads/workbench/
   - 下载 Windows 版本
   - 安装到默认位置

2. **连接配置**
   - 打开 MySQL Workbench
   - 点击 "+" 创建新连接
   - 连接名：Local MySQL
   - 主机名：localhost
   - 端口：3306
   - 用户名：root
   - 密码：hcd147258

### 2. Navicat（商业工具，可选）

1. **下载试用版**
   - 访问：https://www.navicat.com.cn/
   - 下载 Navicat for MySQL

2. **连接配置**
   - 新建连接
   - 连接名：本地MySQL
   - 主机：localhost
   - 端口：3306
   - 用户名：root
   - 密码：hcd147258

---

## 备份配置

### 1. 手动备份

```powershell
# 备份单个数据库
mysqldump -u root -p property_management > backup_20241204.sql

# 备份所有数据库
mysqldump -u root -p --all-databases > all_databases_backup.sql

# 备份数据库结构（不含数据）
mysqldump -u root -p --no-data property_management > structure_only.sql
```

### 2. 自动备份脚本

参考已创建的 `MySQLBackup_ScheduledTask.ps1` 脚本，配置计划任务实现自动备份。

---

## 故障排除

### 1. 常见问题

#### 问题1：服务无法启动
```powershell
# 检查服务状态
Get-Service MySQL84

# 查看错误日志
Get-Content "E:\mysql\mysql-8.4.5-winx64\data\*.err"

# 重新初始化
.\mysqld --initialize --console
```

#### 问题2：忘记密码
```powershell
# 停止服务
net stop MySQL84

# 跳过权限启动
.\mysqld --skip-grant-tables

# 另开窗口连接（无需密码）
mysql -u root

# 重置密码
USE mysql;
UPDATE user SET authentication_string=PASSWORD('new_password') WHERE User='root';
FLUSH PRIVILEGES;
EXIT;

# 重启正常服务
```

#### 问题3：端口被占用
```powershell
# 查看端口占用
netstat -ano | findstr :3306

# 修改配置文件端口
# 编辑 my.ini，修改 port=3307
```

### 2. 性能优化

```sql
-- 查看当前配置
SHOW VARIABLES LIKE '%buffer%';

-- 查看连接数
SHOW STATUS LIKE 'Connections';

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query%';
```

---

## 验证安装

### 完整验证脚本

```sql
-- 1. 检查版本
SELECT VERSION();

-- 2. 检查字符集
SHOW VARIABLES LIKE 'character%';

-- 3. 检查存储引擎
SHOW ENGINES;

-- 4. 检查用户
SELECT User, Host FROM mysql.user;

-- 5. 检查数据库
SHOW DATABASES;

-- 6. 测试创建删除
CREATE DATABASE test_db;
USE test_db;
CREATE TABLE test_table (id INT, name VARCHAR(50));
INSERT INTO test_table VALUES (1, 'test');
SELECT * FROM test_table;
DROP TABLE test_table;
DROP DATABASE test_db;
```

---

## 自动化脚本

### 1. MySQL 安装检查脚本

创建 `CheckMySQLInstallation.ps1` 脚本来验证安装：

```powershell
# MySQL 安装检查脚本
$MYSQL_PATH = "E:\mysql\mysql-8.4.5-winx64\bin\mysql.exe"
$SERVICE_NAME = "MySQL84"

Write-Host "MySQL 安装检查" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green

# 检查文件是否存在
if (Test-Path $MYSQL_PATH) {
    Write-Host "✅ MySQL 文件存在: $MYSQL_PATH" -ForegroundColor Green
} else {
    Write-Host "❌ MySQL 文件不存在: $MYSQL_PATH" -ForegroundColor Red
}

# 检查服务状态
$service = Get-Service -Name $SERVICE_NAME -ErrorAction SilentlyContinue
if ($service) {
    Write-Host "✅ MySQL 服务存在: $($service.Status)" -ForegroundColor Green
} else {
    Write-Host "❌ MySQL 服务不存在" -ForegroundColor Red
}

# 检查端口
$port = netstat -ano | findstr :3306
if ($port) {
    Write-Host "✅ MySQL 端口 3306 正在监听" -ForegroundColor Green
} else {
    Write-Host "❌ MySQL 端口 3306 未监听" -ForegroundColor Red
}
```

### 2. 数据库创建脚本

参考已创建的 `CreateMySQLDatabase.ps1` 脚本。

### 3. 备份配置脚本

参考已创建的 `MySQLBackup_ScheduledTask.ps1` 脚本。

---

## 配置文件模板

### my.ini 完整配置

```ini
[mysqld]
# 基本设置
port=3306
basedir=E:\mysql\mysql-8.4.5-winx64
datadir=E:\mysql\mysql-8.4.5-winx64\data

# 字符集设置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 连接设置
max_connections=200
max_connect_errors=10
max_allowed_packet=64M

# 存储引擎
default-storage-engine=INNODB
default_authentication_plugin=mysql_native_password

# InnoDB 设置
innodb_buffer_pool_size=128M
innodb_log_file_size=64M
innodb_file_per_table=1
innodb_flush_log_at_trx_commit=2

# 日志设置
log-error=E:\mysql\mysql-8.4.5-winx64\data\mysql-error.log
slow_query_log=1
slow_query_log_file=E:\mysql\mysql-8.4.5-winx64\data\mysql-slow.log
long_query_time=2

# 安全设置
skip-name-resolve
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set=utf8mb4

[client]
port=3306
default-character-set=utf8mb4
```

---

## 快速命令参考

### 常用 MySQL 命令

```sql
-- 数据库操作
SHOW DATABASES;                          -- 显示所有数据库
CREATE DATABASE db_name;                 -- 创建数据库
USE db_name;                            -- 使用数据库
DROP DATABASE db_name;                  -- 删除数据库

-- 表操作
SHOW TABLES;                            -- 显示所有表
DESCRIBE table_name;                    -- 显示表结构
CREATE TABLE table_name (...);          -- 创建表
DROP TABLE table_name;                  -- 删除表

-- 用户管理
CREATE USER 'user'@'host' IDENTIFIED BY 'password';  -- 创建用户
GRANT privileges ON db.table TO 'user'@'host';       -- 授权
SHOW GRANTS FOR 'user'@'host';                       -- 查看权限
DROP USER 'user'@'host';                             -- 删除用户

-- 系统信息
SELECT VERSION();                       -- 查看版本
SHOW STATUS;                           -- 查看状态
SHOW VARIABLES;                        -- 查看变量
SHOW PROCESSLIST;                      -- 查看进程
```

### 常用 PowerShell 命令

```powershell
# 服务管理
Get-Service MySQL84                     # 查看服务状态
Start-Service MySQL84                   # 启动服务
Stop-Service MySQL84                    # 停止服务
Restart-Service MySQL84                 # 重启服务

# 连接测试
mysql -u root -p                       # 连接 MySQL
mysql -u root -p -e "SHOW DATABASES;"  # 执行命令

# 备份恢复
mysqldump -u root -p db_name > backup.sql     # 备份
mysql -u root -p db_name < backup.sql         # 恢复
```

---

## 总结

完成以上步骤后，您将拥有：
- ✅ 完整的 MySQL 8.4.5 安装
- ✅ 正确的服务配置
- ✅ 安全的用户权限设置
- ✅ 标准的数据库创建流程
- ✅ 自动备份解决方案
- ✅ 图形化管理工具
- ✅ 自动化脚本工具

**下一步建议：**
1. 根据项目需求创建具体的数据库和表
2. 配置定期备份计划任务
3. 设置监控和日志记录
4. 学习 SQL 查询优化
5. 配置主从复制（如需要）

**相关文件：**
- `MySQL完整安装配置指南.md` - 本文档
- `CreateMySQLDatabase.ps1` - 数据库创建脚本
- `MySQLBackup_ScheduledTask.ps1` - 自动备份脚本
- `Create_HiddenScheduledTask.ps1` - 计划任务创建脚本

如有问题，请参考故障排除部分或查看 MySQL 官方文档。
