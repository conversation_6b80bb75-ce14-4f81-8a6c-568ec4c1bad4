# Generated by Django 5.2.3 on 2025-06-19 01:57

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Shop',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shop_number', models.CharField(max_length=20, verbose_name='商铺号')),
                ('tenant_name', models.CharField(max_length=50, verbose_name='租户姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('id_card', models.CharField(max_length=18, verbose_name='身份证号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='平米数')),
                ('lease_start_date', models.DateField(verbose_name='起租时间')),
                ('property_fee_due_date', models.DateField(verbose_name='物业费到期时间')),
                ('property_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='物业费用')),
                ('status', models.CharField(choices=[('active', '正常'), ('overdue', '逾期'), ('checkout', '已退房')], default='active', max_length=10, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '商铺',
                'verbose_name_plural': '商铺',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ShopPaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='交费时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='缴费金额')),
                ('fee_start_date', models.DateField(verbose_name='物业费开始时间')),
                ('fee_end_date', models.DateField(verbose_name='物业费结束时间')),
                ('payment_method', models.CharField(default='现金', max_length=20, verbose_name='缴费方式')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('shop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shops.shop', verbose_name='商铺')),
            ],
            options={
                'verbose_name': '商铺缴费记录',
                'verbose_name_plural': '商铺缴费记录',
                'ordering': ['-payment_date'],
            },
        ),
    ]
