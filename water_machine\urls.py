from django.urls import path
from . import views

app_name = 'water_machine'

urlpatterns = [
    # 售水机管理首页
    path('', views.WaterMachineIndexView.as_view(), name='index'),

    # 村售水机充值系统
    path('village/', views.VillageCustomerListView.as_view(), name='village_customer_list'),
    path('village/create/', views.VillageCustomerCreateView.as_view(), name='village_customer_create'),
    path('village/<int:pk>/edit/', views.VillageCustomerUpdateView.as_view(), name='village_customer_edit'),
    path('village/<int:pk>/delete/', views.VillageCustomerDeleteView.as_view(), name='village_customer_delete'),
    path('village/<int:pk>/lost/', views.VillageCardLostView.as_view(), name='village_card_lost'),

    # 批量导入导出
    path('village/import/', views.VillageCustomerImportView.as_view(), name='village_customer_import'),
    path('village/export/', views.VillageCustomerExportView.as_view(), name='village_customer_export'),
    path('village/import-template/', views.VillageCustomerImportTemplateView.as_view(), name='village_customer_import_template'),

    # 村售水机充值记录
    path('village/recharge/', views.VillageRechargeListView.as_view(), name='village_recharge_list'),
    path('village/recharge/create/', views.VillageRechargeCreateView.as_view(), name='village_recharge_create'),
    path('village/recharge/<int:pk>/delete/', views.VillageRechargeDeleteView.as_view(), name='village_recharge_delete'),
    path('village/recharge/export/', views.VillageRechargeExportView.as_view(), name='village_recharge_export'),

    # AJAX接口
    path('api/calculate-amount/', views.CalculateAmountView.as_view(), name='calculate_amount'),
    path('api/customer-quarters/<int:customer_id>/', views.CustomerQuartersAPIView.as_view(), name='customer_quarters_api'),

    # 租户售水机卡号记录系统
    path('tenant/', views.TenantWaterCardListView.as_view(), name='tenant_water_card_list'),
    path('tenant/create/', views.TenantWaterCardCreateView.as_view(), name='tenant_water_card_create'),
    path('tenant/<int:pk>/edit/', views.TenantWaterCardUpdateView.as_view(), name='tenant_water_card_edit'),
    path('tenant/<int:pk>/delete/', views.TenantWaterCardDeleteView.as_view(), name='tenant_water_card_delete'),
    path('tenant/<int:pk>/lost/', views.TenantWaterCardLostView.as_view(), name='tenant_water_card_lost'),
    path('tenant/export/', views.TenantWaterCardExportView.as_view(), name='tenant_water_card_export'),
    path('tenant/import/', views.TenantWaterCardImportView.as_view(), name='tenant_water_card_import'),
    path('tenant/import/template/', views.TenantWaterCardImportTemplateView.as_view(), name='tenant_water_card_import_template'),
]
