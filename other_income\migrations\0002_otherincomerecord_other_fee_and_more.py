# Generated by Django 5.2.3 on 2025-06-30 00:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other_income', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='otherincomerecord',
            name='other_fee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='其它费用'),
        ),
        migrations.AlterField(
            model_name='otherincomerecord',
            name='income_source',
            field=models.CharField(choices=[('water_fee', '水费'), ('storage_electricity', '储藏间电费'), ('water_purifier_fee', '净水机费'), ('water_card', '净水卡'), ('water_bucket_fee', '水桶费'), ('shop_water_fee', '商铺水费'), ('water_card_supplement', '补自来水卡费'), ('water_purifier_card', '补净水机卡费'), ('parking_fee', '停车场收费'), ('elevator_card_fee', '电梯梯控卡费'), ('other', '其他')], default='other', max_length=50, verbose_name='收入来源'),
        ),
    ]
