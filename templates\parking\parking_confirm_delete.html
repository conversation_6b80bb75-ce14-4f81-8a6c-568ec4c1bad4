{% extends 'base/base.html' %}

{% block title %}删除车位确认 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>删除车位确认
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-warning me-2"></i>警告</h6>
                        <p class="mb-0">您即将删除以下车位信息，此操作不可撤销！</p>
                    </div>
                    
                    <!-- 车位信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">车位信息</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>车位号：</strong>{{ object.parking_number }}</p>
                                            <p><strong>房号：</strong>{{ object.room_number }}</p>
                                            <p><strong>租户姓名：</strong>{{ object.tenant_name }}</p>
                                            <p><strong>电话：</strong>{{ object.phone }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>车牌号：</strong>{{ object.license_plate|default:"-" }}</p>
                                            <p><strong>车位所有者：</strong>{{ object.owner_name|default:"-" }}</p>
                                            <p><strong>所有者电话：</strong>{{ object.owner_phone|default:"-" }}</p>
                                            <p><strong>状态：</strong>
                                                {% if object.status == 'active' %}
                                                    <span class="badge bg-success">正常</span>
                                                {% elif object.status == 'overdue' %}
                                                    <span class="badge bg-danger">逾期</span>
                                                {% elif object.status == 'checkout' %}
                                                    <span class="badge bg-secondary">已退车位</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>租车位时间：</strong>{{ object.lease_start_date }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>物业费到期时间：</strong>{{ object.property_fee_due_date }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 确认表单 -->
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'parking:list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>取消
                            </a>
                            <div>
                                {% if object.status == 'overdue' %}
                                    <a href="{% url 'parking:overdue' %}" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-arrow-left me-1"></i>返回逾期列表
                                    </a>
                                {% elif object.status == 'checkout' %}
                                    <a href="{% url 'parking:checkout' %}" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-arrow-left me-1"></i>返回退车位列表
                                    </a>
                                {% endif %}
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>确认删除
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .alert-danger {
        border-left: 4px solid #dc3545;
    }
    
    .bg-light {
        background-color: #f8f9fa !important;
    }
</style>
{% endblock %}

{% block extra_js %}
{% endblock %}
