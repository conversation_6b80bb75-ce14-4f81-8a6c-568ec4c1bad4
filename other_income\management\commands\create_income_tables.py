from django.core.management.base import BaseCommand
from django.db import connection


class Command(BaseCommand):
    help = '创建其它收入管理相关的数据库表'

    def handle(self, *args, **options):
        with connection.cursor() as cursor:
            # 创建收入来源表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `other_income_incomesource` (
                    `id` bigint NOT NULL AUTO_INCREMENT,
                    `name` varchar(50) NOT NULL UNIQUE,
                    `is_active` tinyint(1) NOT NULL DEFAULT 1,
                    `created_at` datetime(6) NOT NULL,
                    PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 创建其它收入记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS `other_income_otherincomerecord` (
                    `id` bigint NOT NULL AUTO_INCREMENT,
                    `income_source` varchar(50) NOT NULL,
                    `amount` decimal(10,2) NOT NULL,
                    `income_date` date NOT NULL,
                    `notes` longtext,
                    `water_fee` decimal(10,2) DEFAULT NULL,
                    `storage_electricity_fee` decimal(10,2) DEFAULT NULL,
                    `water_purifier_fee` decimal(10,2) DEFAULT NULL,
                    `water_card_fee` decimal(10,2) DEFAULT NULL,
                    `water_bucket_fee` decimal(10,2) DEFAULT NULL,
                    `shop_water_fee` decimal(10,2) DEFAULT NULL,
                    `water_card_supplement_fee` decimal(10,2) DEFAULT NULL,
                    `water_purifier_card_fee` decimal(10,2) DEFAULT NULL,
                    `parking_fee` decimal(10,2) DEFAULT NULL,
                    `elevator_card_fee` decimal(10,2) DEFAULT NULL,
                    `created_at` datetime(6) NOT NULL,
                    `updated_at` datetime(6) NOT NULL,
                    PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            # 检查迁移记录是否存在
            cursor.execute("""
                SELECT COUNT(*) FROM `django_migrations` 
                WHERE `app` = 'other_income' AND `name` = '0001_initial'
            """)
            
            if cursor.fetchone()[0] == 0:
                # 添加迁移记录
                cursor.execute("""
                    INSERT INTO `django_migrations` (`app`, `name`, `applied`) 
                    VALUES ('other_income', '0001_initial', NOW())
                """)
                self.stdout.write(
                    self.style.SUCCESS('成功创建迁移记录')
                )
            
        self.stdout.write(
            self.style.SUCCESS('成功创建其它收入管理相关数据库表')
        )
