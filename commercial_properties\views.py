from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.db.models import Q, Case, When, IntegerField, Value
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl import Workbook
from io import BytesIO

from .models import CommercialProperty, CommercialPropertyPaymentHistory
from .forms import (CommercialPropertyForm, CommercialPropertySearchForm,
                   CommercialPropertyRenewForm, CommercialPropertyPaymentHistorySearchForm,
                   CommercialPropertyImportForm)


class CommercialPropertyListView(LoginRequiredMixin, ListView):
    """商品房列表视图"""
    model = CommercialProperty
    template_name = 'commercial_properties/list.html'
    context_object_name = 'properties'
    paginate_by = 20

    def get_queryset(self):
        # 自动更新逾期状态
        today = timezone.now().date()
        CommercialProperty.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态
        CommercialProperty.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = CommercialProperty.objects.filter(status='active')

        # 搜索功能
        search = self.request.GET.get('search')
        search_type = self.request.GET.get('search_type', 'room')  # 默认按房号搜索

        if search:
            if search_type == 'room':
                # 按房号搜索（楼号-单元-房号格式）
                queryset = queryset.filter(
                    Q(room_number__icontains=search) |
                    Q(building_number__icontains=search)
                )
            elif search_type == 'owner':
                # 按业主姓名搜索
                queryset = queryset.filter(owner_name__icontains=search)
            elif search_type == 'phone':
                # 按电话搜索
                queryset = queryset.filter(owner_phone__icontains=search)
            elif search_type == 'parking':
                # 按车位搜索
                queryset = queryset.filter(parking_number__icontains=search)

        # 楼号筛选
        building_number = self.request.GET.get('building_number')
        if building_number:
            queryset = queryset.filter(building_number__icontains=building_number)

        # 单元号筛选
        unit_number = self.request.GET.get('unit_number')
        if unit_number:
            queryset = queryset.filter(unit_number__icontains=unit_number)

        # 房号筛选
        room_number = self.request.GET.get('room_number')
        if room_number:
            queryset = queryset.filter(room_number__icontains=room_number)

        # 车位筛选
        has_parking = self.request.GET.get('has_parking')
        if has_parking == 'true':
            queryset = queryset.filter(has_parking=True)
        elif has_parking == 'false':
            queryset = queryset.filter(has_parking=False)

        # 自定义排序：将字符串转换为数字进行排序
        # 使用Python排序来处理数字字符串的正确排序
        properties_list = list(queryset)

        def sort_key(prop):
            # 将楼号、单元号、房号转换为数字进行排序
            try:
                building_num = int(prop.building_number) if prop.building_number.isdigit() else 0
            except:
                building_num = 0

            try:
                unit_num = int(prop.unit_number) if prop.unit_number and prop.unit_number.isdigit() else 0
            except:
                unit_num = 0

            try:
                room_num = int(prop.room_number) if prop.room_number.isdigit() else 0
            except:
                room_num = 0

            # 返回元组用于排序（升序）
            return (building_num, unit_num, room_num)

        properties_list.sort(key=sort_key)

        # 将排序后的列表转换回QuerySet
        # 由于我们需要保持QuerySet的特性（分页等），我们使用id列表重新查询
        if properties_list:
            ordered_ids = [prop.id for prop in properties_list]
            # 使用Case/When来保持排序顺序
            preserved_order = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(ordered_ids)])
            return queryset.filter(id__in=ordered_ids).order_by(preserved_order)
        else:
            return queryset.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CommercialPropertySearchForm(self.request.GET)

        queryset = self.get_queryset()
        total_count = queryset.count()
        context['total_count'] = total_count

        # 计算配套车位数量
        parking_count = queryset.filter(has_parking=True).count()
        context['parking_count'] = parking_count

        # 计算配套地下室数量
        basement_count = queryset.filter(has_basement=True).count()
        context['basement_count'] = basement_count

        return context


class CommercialPropertyCreateView(LoginRequiredMixin, CreateView):
    """商品房创建视图"""
    model = CommercialProperty
    form_class = CommercialPropertyForm
    template_name = 'commercial_properties/form.html'
    success_url = reverse_lazy('commercial_properties:list')

    def form_valid(self, form):
        # 保存前计算物业费总额
        property = form.save(commit=False)
        property.property_fee_total = property.calculate_property_fee()
        property.save()

        messages.success(self.request, '商品房信息添加成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增商品房'
        return context


class CommercialPropertyUpdateView(LoginRequiredMixin, UpdateView):
    """商品房编辑视图"""
    model = CommercialProperty
    form_class = CommercialPropertyForm
    template_name = 'commercial_properties/form.html'
    success_url = reverse_lazy('commercial_properties:list')

    def form_valid(self, form):
        # 保存前重新计算物业费总额
        property = form.save(commit=False)
        property.property_fee_total = property.calculate_property_fee()
        property.save()

        messages.success(self.request, '商品房信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑商品房'
        return context


class CommercialPropertyDeleteView(LoginRequiredMixin, DeleteView):
    """商品房删除视图"""
    model = CommercialProperty
    success_url = reverse_lazy('commercial_properties:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '商品房信息删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueCommercialPropertyListView(LoginRequiredMixin, ListView):
    """逾期商品房列表视图"""
    model = CommercialProperty
    template_name = 'commercial_properties/overdue_list.html'
    context_object_name = 'properties'
    paginate_by = 20

    def get_queryset(self):
        # 更新逾期状态
        today = timezone.now().date()
        CommercialProperty.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态（将已续费的逾期商品房状态改回正常）
        CommercialProperty.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = CommercialProperty.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        search_type = self.request.GET.get('search_type', 'room')  # 默认按房号搜索

        if search:
            if search_type == 'room':
                # 按房号搜索（楼号-单元-房号格式）
                queryset = queryset.filter(
                    Q(room_number__icontains=search) |
                    Q(building_number__icontains=search)
                )
            elif search_type == 'owner':
                # 按业主姓名搜索
                queryset = queryset.filter(owner_name__icontains=search)
            elif search_type == 'phone':
                # 按电话搜索
                queryset = queryset.filter(owner_phone__icontains=search)
            elif search_type == 'parking':
                # 按车位搜索
                queryset = queryset.filter(parking_number__icontains=search)

        # 自定义排序：将字符串转换为数字进行排序
        properties_list = list(queryset)

        def sort_key(prop):
            try:
                building_num = int(prop.building_number) if prop.building_number.isdigit() else 0
            except:
                building_num = 0

            try:
                unit_num = int(prop.unit_number) if prop.unit_number and prop.unit_number.isdigit() else 0
            except:
                unit_num = 0

            try:
                room_num = int(prop.room_number) if prop.room_number.isdigit() else 0
            except:
                room_num = 0

            return (building_num, unit_num, room_num)

        properties_list.sort(key=sort_key)

        if properties_list:
            ordered_ids = [prop.id for prop in properties_list]
            preserved_order = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(ordered_ids)])
            return queryset.filter(id__in=ordered_ids).order_by(preserved_order)
        else:
            return queryset.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CommercialPropertySearchForm(self.request.GET)

        # 计算逾期统计数据 - 基于所有逾期记录，不只是当前页面
        all_overdue_properties = CommercialProperty.objects.filter(status='overdue')
        if all_overdue_properties.exists():
            short_overdue = sum(1 for property in all_overdue_properties if property.overdue_days <= 7)
            long_overdue = sum(1 for property in all_overdue_properties if property.overdue_days > 30)
        else:
            short_overdue = 0
            long_overdue = 0

        context['short_overdue'] = short_overdue
        context['long_overdue'] = long_overdue

        return context


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """商品房缴费流水视图"""
    model = CommercialPropertyPaymentHistory
    template_name = 'commercial_properties/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = CommercialPropertyPaymentHistory.objects.select_related('property')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(property__owner_name__icontains=search) |
                Q(property__room_number__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                from datetime import datetime
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                from datetime import datetime, timedelta
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CommercialPropertyPaymentHistorySearchForm(self.request.GET)

        # 计算统计数据
        from decimal import Decimal
        from django.db.models import Sum, Count
        from datetime import timedelta

        queryset = self.get_queryset()

        if queryset.exists():
            # 总统计
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0

            # 本月统计
            now = timezone.now()
            local_now = timezone.localtime(now)

            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month_start = this_month_start.replace(month=local_now.month + 1) if local_now.month < 12 else this_month_start.replace(year=local_now.year + 1, month=1)

            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 上月统计
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context


class RenewCommercialPropertyView(LoginRequiredMixin, View):
    """商品房续费视图"""
    template_name = 'commercial_properties/renew.html'

    def get(self, request, pk):
        property = get_object_or_404(CommercialProperty, pk=pk)
        form = CommercialPropertyRenewForm(property=property)
        return render(request, self.template_name, {
            'property': property,
            'form': form
        })

    def post(self, request, pk):
        property = get_object_or_404(CommercialProperty, pk=pk)
        form = CommercialPropertyRenewForm(property=property, data=request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data['notes']

            # 计算续费金额（使用原始月费用，避免精度损失）
            raw_monthly_fee = property._calculate_raw_monthly_fee()
            total_amount = (raw_monthly_fee * months).quantize(Decimal('0.01'))

            # 计算新的到期日期（按月计算）
            current_due_date = property.property_fee_due_date

            # 使用dateutil的relativedelta来正确计算月份
            from dateutil.relativedelta import relativedelta

            # 续费开始日期：从当前到期日期的下一天开始
            fee_start_date = current_due_date + timedelta(days=1)

            # 计算新的到期日期：开始日期 + 月数 - 1天
            new_due_date = fee_start_date + relativedelta(months=months) - timedelta(days=1)

            # 更新商品房信息
            property.property_fee_due_date = new_due_date
            property.status = 'active'
            property.save()

            # 创建缴费记录
            CommercialPropertyPaymentHistory.objects.create(
                property=property,
                amount=total_amount,
                fee_start_date=fee_start_date,
                fee_end_date=new_due_date,
                payment_method=payment_method,
                notes=notes
            )

            messages.success(request, f'商品房 {property.owner_name} 续费成功！续费 {months} 个月，金额 ¥{total_amount}')
            return redirect('commercial_properties:list')

        return render(request, self.template_name, {
            'property': property,
            'form': form
        })


class ExportCommercialPropertyView(LoginRequiredMixin, View):
    """导出商品房数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商品房列表"

        # 设置表头
        headers = [
            '楼号', '单元号', '房号', '平米数', '业主姓名', '地下室', '车位',
            '业主电话', '物业费总额', '物业费到期日期', '楼层', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据 - 只导出活跃状态的商品房，不包括逾期的
        properties = list(CommercialProperty.objects.filter(status='active'))

        # 自定义排序
        def sort_key(prop):
            try:
                building_num = int(prop.building_number) if prop.building_number.isdigit() else 0
            except:
                building_num = 0

            try:
                unit_num = int(prop.unit_number) if prop.unit_number and prop.unit_number.isdigit() else 0
            except:
                unit_num = 0

            try:
                room_num = int(prop.room_number) if prop.room_number.isdigit() else 0
            except:
                room_num = 0

            return (building_num, unit_num, room_num)

        properties.sort(key=sort_key)

        # 写入数据
        for row, property in enumerate(properties, 2):
            ws.cell(row=row, column=1, value=property.building_number)
            ws.cell(row=row, column=2, value=property.unit_number)
            ws.cell(row=row, column=3, value=property.room_number)
            ws.cell(row=row, column=4, value=float(property.area))
            ws.cell(row=row, column=5, value=property.owner_name)
            ws.cell(row=row, column=6, value='是' if property.has_basement else '否')
            ws.cell(row=row, column=7, value='是' if property.has_parking else '否')
            ws.cell(row=row, column=8, value=property.owner_phone)
            ws.cell(row=row, column=9, value=float(property.property_fee_total))
            ws.cell(row=row, column=10, value=property.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=11, value=property.floor)
            ws.cell(row=row, column=12, value=property.get_status_display())
            # 将时区感知的datetime转换为本地时间
            local_created_at = timezone.localtime(property.created_at)
            ws.cell(row=row, column=13, value=local_created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="商品房列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ImportCommercialPropertyView(LoginRequiredMixin, View):
    """导入商品房数据"""
    template_name = 'commercial_properties/import.html'

    def get(self, request):
        form = CommercialPropertyImportForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = CommercialPropertyImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']
            skip_duplicates = request.POST.get('skip_duplicates') == 'on'
            update_existing = request.POST.get('update_existing') == 'on'

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                skip_count = 0
                update_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:5]):  # 如果前5列都为空，跳过这行
                            continue

                        # 解析数据
                        building_number = str(row[0]).strip() if row[0] else ''
                        unit_number = str(row[1]).strip() if row[1] else ''
                        room_number = str(row[2]).strip() if row[2] else ''

                        # 验证必填字段
                        if not building_number:
                            errors.append(f'第{row_num}行: 楼号不能为空')
                            error_count += 1
                            continue

                        if not room_number:
                            errors.append(f'第{row_num}行: 房号不能为空')
                            error_count += 1
                            continue

                        # 解析数值字段
                        try:
                            area = Decimal(str(row[3])) if row[3] else Decimal('0')
                        except (ValueError, TypeError):
                            errors.append(f'第{row_num}行: 平米数格式错误')
                            error_count += 1
                            continue

                        owner_name = str(row[4]).strip() if row[4] else ''
                        if not owner_name:
                            errors.append(f'第{row_num}行: 业主姓名不能为空')
                            error_count += 1
                            continue

                        # 解析可选字段
                        has_basement = str(row[5]).lower() in ['是', 'true', '1', 'yes'] if row[5] else False
                        basement_number = str(row[6]).strip() if row[6] and has_basement else ''
                        has_parking = str(row[7]).lower() in ['是', 'true', '1', 'yes'] if row[7] else False
                        parking_number = str(row[8]).strip() if row[8] and has_parking else ''
                        owner_phone = str(row[9]).strip() if row[9] else ''

                        # 解析日期 - 使用统一的日期解析函数
                        def parse_date(date_value, default_date=None):
                            """统一的日期解析函数"""
                            if not date_value:
                                return default_date or timezone.now().date()

                            if isinstance(date_value, str):
                                try:
                                    return datetime.strptime(date_value, '%Y-%m-%d').date()
                                except ValueError:
                                    try:
                                        # 尝试其他日期格式
                                        return datetime.strptime(date_value, '%Y/%m/%d').date()
                                    except ValueError:
                                        return default_date or timezone.now().date()
                            elif hasattr(date_value, 'date'):
                                # 如果是datetime对象，转换为date
                                return date_value.date()
                            elif hasattr(date_value, 'year'):
                                # 如果已经是date对象
                                return date_value
                            else:
                                return default_date or timezone.now().date()

                        property_fee_due_date = parse_date(row[10], timezone.now().date() + timedelta(days=365))

                        # 解析楼层
                        try:
                            floor = int(row[11]) if row[11] else 1
                        except (ValueError, TypeError):
                            floor = 1

                        # 检查是否存在重复记录（楼号、单元号、房号都相同才算重复）
                        existing = CommercialProperty.objects.filter(
                            building_number=building_number,
                            unit_number=unit_number,
                            room_number=room_number
                        ).first()

                        if existing:
                            if skip_duplicates and not update_existing:
                                skip_count += 1
                                continue
                            elif update_existing:
                                # 更新现有记录
                                existing.unit_number = unit_number
                                existing.area = area
                                existing.owner_name = owner_name
                                existing.has_basement = has_basement
                                existing.basement_number = basement_number
                                existing.has_parking = has_parking
                                existing.parking_number = parking_number
                                existing.owner_phone = owner_phone
                                existing.property_fee_due_date = property_fee_due_date
                                existing.floor = floor
                                existing.save()
                                update_count += 1
                                continue

                        # 创建新的商品房记录
                        property = CommercialProperty(
                            building_number=building_number,
                            unit_number=unit_number,
                            room_number=room_number,
                            area=area,
                            owner_name=owner_name,
                            has_basement=has_basement,
                            basement_number=basement_number,
                            has_parking=has_parking,
                            parking_number=parking_number,
                            owner_phone=owner_phone,
                            property_fee_due_date=property_fee_due_date,
                            floor=floor
                        )
                        property.save()
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                # 显示导入结果
                result_messages = []
                if success_count > 0:
                    result_messages.append(f'成功导入 {success_count} 条新记录')
                if update_count > 0:
                    result_messages.append(f'更新 {update_count} 条现有记录')
                if skip_count > 0:
                    result_messages.append(f'跳过 {skip_count} 条重复记录')

                if result_messages:
                    messages.success(request, '；'.join(result_messages) + '！')

                if error_count > 0:
                    error_msg = f'导入失败 {error_count} 条记录：\n' + '\n'.join(errors[:10])
                    if len(errors) > 10:
                        error_msg += f'\n... 还有 {len(errors) - 10} 个错误'
                    messages.error(request, error_msg)

                return redirect('commercial_properties:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})


class DownloadTemplateView(LoginRequiredMixin, View):
    """下载Excel导入模板"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商品房导入模板"

        # 设置表头
        headers = [
            '楼号*', '单元号', '房号*', '平米数*', '业主姓名*',
            '地下室(是/否)', '地下室号', '车位(是/否)', '车位号',
            '业主电话*', '物业费到期日期', '楼层'
        ]

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

        # 添加示例数据
        sample_data = [
            ['A1', '1', '101', '120.5', '张三', '是', 'B101', '是', 'P101', '13800138001', '2025-12-31', '5'],
            ['A1', '1', '102', '95.0', '李四', '否', '', '是', 'P102', '13800138002', '2025-12-31', '5'],
            ['A2', '2', '201', '110.0', '王五', '是', 'B201', '否', '', '13800138003,13900139003', '2025-12-31', '8'],
        ]

        for row_num, row_data in enumerate(sample_data, 2):
            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col, value=value)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                if col in [3, 4]:  # 平米数和业主姓名列
                    cell.alignment = Alignment(horizontal='center')

        # 设置列宽
        column_widths = [8, 8, 8, 10, 12, 12, 10, 12, 10, 15, 15, 8]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 添加说明工作表
        ws_info = wb.create_sheet("导入说明")
        info_data = [
            ['字段名称', '是否必填', '说明', '示例'],
            ['楼号', '是', '建筑楼号', 'A1, B2, C3'],
            ['单元号', '否', '单元号', '1, 2, 3'],
            ['房号', '是', '房间号', '101, 102, 201'],
            ['平米数', '是', '房屋面积，支持小数', '120.5, 95.0'],
            ['业主姓名', '是', '业主姓名', '张三, 李四'],
            ['地下室', '否', '是否有地下室，填写：是/否', '是, 否'],
            ['地下室号', '否', '地下室编号', 'B101, B102'],
            ['车位', '否', '是否有车位，填写：是/否', '是, 否'],
            ['车位号', '否', '车位编号', 'P101, P102'],
            ['业主电话', '是', '联系电话，多个用逗号分隔', '13800138001, 13800138001,13900139001'],
            ['物业费到期日期', '否', '格式：YYYY-MM-DD，默认为一年后', '2025-12-31'],
            ['楼层', '否', '所在楼层，默认为1', '5, 8, 12'],
        ]

        for row_num, row_data in enumerate(info_data, 1):
            for col, value in enumerate(row_data, 1):
                cell = ws_info.cell(row=row_num, column=col, value=value)
                if row_num == 1:  # 表头
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color='E7E6E6', end_color='E7E6E6', fill_type='solid')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

        # 设置说明工作表列宽
        info_widths = [15, 10, 30, 25]
        for col, width in enumerate(info_widths, 1):
            ws_info.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="商品房导入模板_{timezone.now().strftime("%Y%m%d")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class BatchDeleteCommercialPropertyView(LoginRequiredMixin, View):
    """批量删除商品房"""

    def post(self, request):
        selected_items = request.POST.getlist('selected_items')

        if not selected_items:
            messages.error(request, '请选择要删除的商品房')
            return redirect('commercial_properties:list')

        try:
            # 获取要删除的商品房
            properties = CommercialProperty.objects.filter(id__in=selected_items)
            count = properties.count()

            if count == 0:
                messages.error(request, '没有找到要删除的商品房')
                return redirect('commercial_properties:list')

            # 执行删除
            properties.delete()

            messages.success(request, f'成功删除 {count} 个商品房')

        except Exception as e:
            messages.error(request, f'删除失败：{str(e)}')

        return redirect('commercial_properties:list')


class BatchDeletePaymentHistoryView(LoginRequiredMixin, View):
    """批量删除缴费记录"""

    def post(self, request):
        payment_ids = request.POST.getlist('payment_ids')

        if not payment_ids:
            messages.error(request, '请选择要删除的缴费记录')
            return redirect('commercial_properties:payment_history')

        try:
            # 获取要删除的缴费记录
            payments = CommercialPropertyPaymentHistory.objects.filter(id__in=payment_ids)
            count = payments.count()

            if count == 0:
                messages.error(request, '没有找到要删除的缴费记录')
                return redirect('commercial_properties:payment_history')

            # 执行删除
            payments.delete()

            messages.success(request, f'成功删除 {count} 条缴费记录')

        except Exception as e:
            messages.error(request, f'删除失败：{str(e)}')

        return redirect('commercial_properties:payment_history')


class ExportPaymentHistoryView(LoginRequiredMixin, View):
    """导出缴费流水数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商品房缴费流水"

        # 设置表头
        headers = [
            '缴费时间', '业主姓名', '楼号', '单元号', '房号', '缴费金额',
            '费用开始日期', '费用结束日期', '缴费方式', '备注'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        payments = CommercialPropertyPaymentHistory.objects.select_related('property')

        # 应用与列表页面相同的筛选条件
        # 搜索功能
        search = request.GET.get('search')
        if search:
            payments = payments.filter(
                Q(property__owner_name__icontains=search) |
                Q(property__building_number__icontains=search) |
                Q(property__unit_number__icontains=search) |
                Q(property__room_number__icontains=search) |
                Q(payment_method__icontains=search) |
                Q(notes__icontains=search)
            )

        # 日期筛选
        start_date = request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                # 设置为当天的23:59:59
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        payments = payments.order_by('-payment_date')

        # 写入数据
        for row, payment in enumerate(payments, 2):
            # 将时区感知的datetime转换为本地时间
            local_payment_date = timezone.localtime(payment.payment_date)

            ws.cell(row=row, column=1, value=local_payment_date.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=2, value=payment.property.owner_name)
            ws.cell(row=row, column=3, value=payment.property.building_number)
            ws.cell(row=row, column=4, value=payment.property.unit_number or '')
            ws.cell(row=row, column=5, value=payment.property.room_number)
            ws.cell(row=row, column=6, value=float(payment.amount))
            ws.cell(row=row, column=7, value=payment.fee_start_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=8, value=payment.fee_end_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=9, value=payment.payment_method)
            ws.cell(row=row, column=10, value=payment.notes or '')

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # 生成文件名，包含时间范围信息
        filename = "商品房缴费流水"
        if start_date and end_date:
            filename += f"_{start_date}至{end_date}"
        elif start_date:
            filename += f"_{start_date}起"
        elif end_date:
            filename += f"_至{end_date}"
        filename += f"_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # 保存到响应
        wb.save(response)
        return response


class ExportOverdueCommercialPropertiesView(LoginRequiredMixin, View):
    """导出逾期商品房"""

    def get(self, request):
        try:
            # 获取所有逾期商品房
            today = timezone.now().date()
            properties = list(CommercialProperty.objects.filter(
                property_fee_due_date__lt=today
            ))

            # 自定义排序
            def sort_key(prop):
                try:
                    building_num = int(prop.building_number) if prop.building_number.isdigit() else 0
                except:
                    building_num = 0

                try:
                    unit_num = int(prop.unit_number) if prop.unit_number and prop.unit_number.isdigit() else 0
                except:
                    unit_num = 0

                try:
                    room_num = int(prop.room_number) if prop.room_number.isdigit() else 0
                except:
                    room_num = 0

                return (building_num, unit_num, room_num)

            properties.sort(key=sort_key)

            # 应用搜索筛选
            search = request.GET.get('search')
            if search:
                properties = properties.filter(
                    Q(owner_name__icontains=search) |
                    Q(building_number__icontains=search) |
                    Q(unit_number__icontains=search) |
                    Q(room_number__icontains=search) |
                    Q(owner_phone__icontains=search)
                )

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "逾期商品房列表"

            # 设置表头
            headers = [
                '楼号', '单元号', '房号', '平米数', '业主姓名', '地下室', '车位',
                '业主电话', '年物业费', '到期日期', '逾期天数'
            ]

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')

            # 写入数据
            for row, property in enumerate(properties, 2):
                # 计算逾期天数
                overdue_days = (timezone.now().date() - property.property_fee_due_date).days if property.property_fee_due_date else 0

                ws.cell(row=row, column=1, value=property.building_number)
                ws.cell(row=row, column=2, value=property.unit_number or '')
                ws.cell(row=row, column=3, value=property.room_number)
                ws.cell(row=row, column=4, value=float(property.area))
                ws.cell(row=row, column=5, value=property.owner_name)
                ws.cell(row=row, column=6, value='是' if property.has_basement else '否')
                ws.cell(row=row, column=7, value='是' if property.has_parking else '否')
                ws.cell(row=row, column=8, value=property.owner_phone)
                ws.cell(row=row, column=9, value=float(property.property_fee_total))
                ws.cell(row=row, column=10, value=property.property_fee_due_date.strftime('%Y-%m-%d') if property.property_fee_due_date else '')
                ws.cell(row=row, column=11, value=overdue_days)

            # 调整列宽
            column_widths = [8, 8, 8, 10, 12, 8, 8, 15, 12, 12, 10]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[get_column_letter(i)].width = width

            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            # 生成文件名
            filename = f"逾期商品房列表_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            # 保存工作簿
            wb.save(response)
            return response

        except Exception as e:
            messages.error(request, f'导出失败：{str(e)}')
            return redirect('commercial_properties:overdue')
