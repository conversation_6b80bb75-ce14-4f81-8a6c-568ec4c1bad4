from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from django.db.models import Sum, Count

from tenants.models import TenantPaymentHistory


class Command(BaseCommand):
    help = '测试物业费流水统计计算'

    def handle(self, *args, **options):
        self.stdout.write('测试物业费流水统计计算...')
        
        # 获取所有缴费记录
        queryset = TenantPaymentHistory.objects.all()
        
        if queryset.exists():
            # 总金额和总笔数
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )
            
            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0
            
            self.stdout.write(f'总收入金额: ¥{total_amount}')
            self.stdout.write(f'总缴费笔数: {total_count}')
            
            # 本月统计
            now = timezone.now()
            this_month_stats = queryset.filter(
                payment_date__year=now.year,
                payment_date__month=now.month
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )
            
            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')
            
            self.stdout.write(f'本月缴费笔数: {this_month_count}')
            self.stdout.write(f'本月缴费金额: ¥{this_month_amount}')
            
            # 上月统计
            last_month = now.replace(day=1) - timedelta(days=1)
            last_month_stats = queryset.filter(
                payment_date__year=last_month.year,
                payment_date__month=last_month.month
            ).aggregate(
                amount=Sum('amount')
            )
            
            last_month_amount = last_month_stats['amount'] or Decimal('0')
            
            self.stdout.write(f'上月缴费金额: ¥{last_month_amount}')
            
            # 显示一些具体记录
            self.stdout.write('\n最近5条缴费记录:')
            recent_payments = queryset.order_by('-payment_date')[:5]
            for payment in recent_payments:
                self.stdout.write(
                    f'{payment.payment_date.strftime("%Y-%m-%d")} - '
                    f'{payment.tenant.tenant_name} - ¥{payment.amount}'
                )
        else:
            self.stdout.write('没有找到缴费记录')
        
        self.stdout.write(
            self.style.SUCCESS('统计计算完成！')
        )
