from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal


class ParkingSpace(models.Model):
    """车位模型"""
    # 基本信息
    building_number = models.CharField('楼号', max_length=10)
    room_number = models.CharField('房号', max_length=10)
    tenant_name = models.CharField('租户姓名', max_length=50)
    phone = models.CharField('电话', max_length=20)
    parking_number = models.CharField('车位号', max_length=20)
    license_plate = models.CharField('车牌号', max_length=20)

    # 车位所有者信息
    owner_name = models.CharField('车位所有者', max_length=50, blank=True, null=True)
    owner_phone = models.CharField('所有者电话', max_length=20, blank=True, null=True)

    # 租赁信息
    lease_start_date = models.DateField('租车位时间')
    property_fee_due_date = models.DateField('物业费到期时间')
    property_fee = models.DecimalField('物业费用', max_digits=10, decimal_places=2, default=Decimal('20.00'))

    # 状态
    STATUS_CHOICES = [
        ('active', '正常'),
        ('overdue', '逾期'),
        ('checkout', '已退车位'),
    ]
    status = models.CharField('状态', max_length=10, choices=STATUS_CHOICES, default='active')

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '车位'
        verbose_name_plural = '车位'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.parking_number} {self.tenant_name} ({self.license_plate})"

    @property
    def days_until_due(self):
        """距离到期天数"""
        if self.property_fee_due_date:
            delta = self.property_fee_due_date - timezone.now().date()
            return delta.days
        return 0

    @property
    def is_overdue(self):
        """是否逾期"""
        return self.days_until_due < 0

    @property
    def overdue_days(self):
        """逾期天数"""
        if self.is_overdue:
            return abs(self.days_until_due)
        return 0

    def save(self, *args, **kwargs):
        # 车位费固定每月20元
        self.property_fee = Decimal('20.00')

        # 更新状态
        if self.property_fee_due_date:
            if self.is_overdue and self.status == 'active':
                self.status = 'overdue'
            elif not self.is_overdue and self.status == 'overdue':
                self.status = 'active'

        super().save(*args, **kwargs)


class ParkingPaymentHistory(models.Model):
    """车位缴费流水"""
    parking = models.ForeignKey(ParkingSpace, on_delete=models.CASCADE, verbose_name='车位')
    payment_date = models.DateTimeField('交费时间', default=timezone.now)
    amount = models.DecimalField('缴费金额', max_digits=10, decimal_places=2)
    fee_start_date = models.DateField('物业费开始时间')
    fee_end_date = models.DateField('物业费结束时间')
    payment_method = models.CharField('缴费方式', max_length=20, default='现金')
    notes = models.TextField('备注', blank=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '车位缴费记录'
        verbose_name_plural = '车位缴费记录'
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.parking} - ¥{self.amount} - {self.payment_date.strftime('%Y-%m-%d')}"
