# 东悦物业管理系统 - Python依赖包
# 更新时间: 2024-12-05
# Python版本要求: >=3.8

# ==========================================
# 核心Django框架
# ==========================================
Django==5.2

# ==========================================
# 数据库相关
# ==========================================
mysqlclient==2.2.0

# ==========================================
# Web服务器和中间件
# ==========================================
# WSGI服务器
waitress==2.1.2
gunicorn==21.2.0

# 静态文件服务
whitenoise[brotli]==6.5.0

# ==========================================
# 数据处理和文件操作
# ==========================================
# Excel文件处理
pandas==2.0.3
openpyxl==3.1.2

# 时间处理
python-dateutil==2.8.2

# 图像处理
Pillow==10.0.0

# ==========================================
# 配置和环境管理
# ==========================================
python-decouple==3.8

# ==========================================
# Windows服务支持
# ==========================================
pywin32==306

# ==========================================
# 压缩和性能优化
# ==========================================
Brotli==1.1.0

# ==========================================
# 开发和调试工具（可选）
# ==========================================
# 调试工具栏
# django-debug-toolbar==4.2.0

# ==========================================
# 其他依赖（根据实际安装情况）
# ==========================================
# 如果使用Redis缓存
# redis==4.6.0
# django-redis==5.3.0

# 如果使用Celery任务队列
# celery==5.3.0

# 如果需要API功能
# djangorestframework==3.14.0
