from django import forms
from django.utils import timezone


# 收入来源选择项（避免循环导入）
INCOME_SOURCE_CHOICES = [
    ('water_fee', '水费'),
    ('storage_electricity', '储藏间电费'),
    ('water_purifier_fee', '净水机费'),
    ('water_card', '净水卡'),
    ('water_bucket_fee', '水桶费'),
    ('shop_water_fee', '商铺水费'),
    ('water_card_supplement', '补自来水卡费'),
    ('water_purifier_card', '补净水机卡费'),
    ('parking_fee', '停车场收费'),
    ('elevator_card_fee', '电梯梯控卡费'),
    ('other', '其他'),
]


class OtherIncomeRecordForm(forms.ModelForm):
    """其它收入记录表单"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 设置日期字段的初始值和widget属性
        if self.instance and self.instance.pk:
            # 编辑时使用记录的实际日期
            date_str = self.instance.income_date.strftime('%Y-%m-%d')
            self.fields['income_date'].initial = self.instance.income_date
            self.fields['income_date'].widget.attrs['value'] = date_str
        else:
            # 新增时使用今天的日期
            today = timezone.now().date()
            self.fields['income_date'].initial = today
            self.fields['income_date'].widget.attrs['value'] = today.strftime('%Y-%m-%d')

    class Meta:
        from .models import OtherIncomeRecord
        model = OtherIncomeRecord
        fields = [
            'income_date', 'notes',
            'water_fee', 'storage_electricity_fee', 'water_purifier_fee',
            'water_card_fee', 'water_bucket_fee', 'shop_water_fee',
            'water_card_supplement_fee', 'water_purifier_card_fee',
            'parking_fee', 'elevator_card_fee', 'water_meter_battery_fee', 'other_fee'
        ]
        widgets = {
            'income_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '请输入备注信息...'
            }),
            'water_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'storage_electricity_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'water_purifier_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'water_card_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'water_bucket_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'shop_water_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'water_card_supplement_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'water_purifier_card_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'parking_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'elevator_card_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'water_meter_battery_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'other_fee': forms.NumberInput(attrs={
                'class': 'form-control fee-input',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
        }


class OtherIncomeSearchForm(forms.Form):
    """其它收入搜索表单"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索备注...'
        })
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
