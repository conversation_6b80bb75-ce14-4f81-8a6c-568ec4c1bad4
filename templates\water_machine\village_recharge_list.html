{% extends 'base/base.html' %}
{% load static %}

{% block title %}村售水机充值记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-money-bill-wave text-success me-2"></i>
                村售水机充值记录
            </h2>
            <div class="mt-2">
                <span class="badge bg-info me-2">
                    <i class="fas fa-receipt me-1"></i>
                    {{ stats.total_records }}条记录
                </span>
                <span class="badge bg-success me-2">
                    <i class="fas fa-money-bill-wave me-1"></i>
                    总金额 ¥{{ stats.total_amount|floatformat:2 }}
                </span>
            </div>
        </div>
        <div>
            <a href="{% url 'water_machine:village_customer_list' %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-users me-1"></i>客户列表
            </a>
            <a href="{% url 'water_machine:village_recharge_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>新增充值
            </a>
        </div>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <!-- 导出功能 -->
                <div class="col-md-3">
                    <button type="button" class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#exportModal">
                        <i class="fas fa-download me-1"></i>时间范围导出
                    </button>
                </div>

                <!-- 搜索表单 -->
                <div class="col-md-9">
                    <form method="get" class="row g-2">
                        <div class="col-md-8">
                            <div class="search-box">
                                <i class="fas fa-search search-icon"></i>
                                {{ search_form.search }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <a href="{% url 'water_machine:village_recharge_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-redo me-1"></i>重置
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值记录列表 -->
    {% if recharge_records %}
        <div class="card">
            <div class="card-body">
                <!-- 数据表格 - 分离表头表体结构 -->
                <div class="table-container">
                    <!-- 固定表头 -->
                    <div class="table-header-fixed">
                        <table class="table" id="header-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>客户姓名</th>
                                    <th>住址</th>
                                    <th>使用卡号</th>
                                    <th>使用人数</th>
                                    <th>充值金额</th>
                                    <th>充值季度</th>
                                    <th>充值时间</th>
                                    <th>备注</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    <!-- 可滚动表体 -->
                    <div class="table-body-scroll">
                        <table class="table table-hover" id="body-table">
                            <thead style="visibility: hidden;">
                                <tr>
                                    <th>客户姓名</th>
                                    <th>住址</th>
                                    <th>使用卡号</th>
                                    <th>使用人数</th>
                                    <th>充值金额</th>
                                    <th>充值季度</th>
                                    <th>充值时间</th>
                                    <th>备注</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in recharge_records %}
                                <tr>
                                    <td>{{ record.customer.name }}</td>
                                    <td>{{ record.customer.address }}</td>
                                    <td>
                                        <strong class="text-primary">{{ record.card_number }}</strong>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ record.people_count }}人</span>
                                    </td>
                                    <td class="text-end">
                                        <strong class="text-success">¥{{ record.amount|floatformat:2 }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ record.quarter }}</span>
                                    </td>
                                    <td>{{ record.recharge_date|date:"Y-m-d H:i:s" }}</td>
                                    <td>{{ record.notes|default:"-"|truncatechars:30 }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'water_machine:village_recharge_delete' record.pk %}" class="btn btn-outline-danger" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 美化的分页导航 -->
                {% if is_paginated %}
                <div class="pagination-container">
                    <!-- 分页统计信息 -->
                    <div class="pagination-info">
                        <div class="info-item">
                            <i class="fas fa-list-ol text-primary"></i>
                            <span class="info-text">
                                共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                            </span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-file-alt text-success"></i>
                            <span class="info-text">
                                每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                            </span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-bookmark text-info"></i>
                            <span class="info-text">
                                第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                            </span>
                        </div>
                    </div>

                    <!-- 分页按钮 -->
                    <nav aria-label="分页导航" class="pagination-nav">
                        <ul class="pagination pagination-modern">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.quarter %}&quarter={{ request.GET.quarter }}{% endif %}" title="首页">
                                        <i class="fas fa-angle-double-left"></i>
                                        <span class="d-none d-sm-inline">首页</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.quarter %}&quarter={{ request.GET.quarter }}{% endif %}" title="上一页">
                                        <i class="fas fa-angle-left"></i>
                                        <span class="d-none d-sm-inline">上一页</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link page-link-first">
                                        <i class="fas fa-angle-double-left"></i>
                                        <span class="d-none d-sm-inline">首页</span>
                                    </span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link page-link-prev">
                                        <i class="fas fa-angle-left"></i>
                                        <span class="d-none d-sm-inline">上一页</span>
                                    </span>
                                </li>
                            {% endif %}

                            <!-- 页码显示 -->
                            <li class="page-item active">
                                <span class="page-link page-link-current">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ page_obj.number }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.quarter %}&quarter={{ request.GET.quarter }}{% endif %}" title="下一页">
                                        <span class="d-none d-sm-inline">下一页</span>
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.quarter %}&quarter={{ request.GET.quarter }}{% endif %}" title="末页">
                                        <span class="d-none d-sm-inline">末页</span>
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link page-link-next">
                                        <span class="d-none d-sm-inline">下一页</span>
                                        <i class="fas fa-angle-right"></i>
                                    </span>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link page-link-last">
                                        <span class="d-none d-sm-inline">末页</span>
                                        <i class="fas fa-angle-double-right"></i>
                                    </span>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无充值记录</h5>
                <p class="text-muted">点击上方"新增充值"按钮添加第一条充值记录</p>
            </div>
        </div>
    {% endif %}
</div>

<!-- 导出模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">
                    <i class="fas fa-download text-success me-2"></i>
                    时间范围导出
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{% url 'water_machine:village_recharge_export' %}" method="get">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            将导出指定时间范围内的所有充值记录
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-download me-1"></i>导出
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    /* 表格容器间距最小化 */
    .table-container {
        margin-bottom: 0 !important;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin-bottom: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1110px;
        border-spacing: 0;
        border-collapse: separate;
    }

    #header-table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1110px;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin-bottom: 0 !important;
        margin-top: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1110px;
    }

    /* 表体隐藏表头 */
    #body-table thead {
        visibility: hidden;
        height: 0 !important;
        line-height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    #body-table thead th {
        padding: 0 !important;
        height: 0 !important;
        border: none !important;
        margin: 0 !important;
        line-height: 0 !important;
    }

    /* 表格内容样式 */
    #body-table td {
        padding: 0.6rem 0.5rem;
        text-align: center;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
        border-right: 1px solid #dee2e6;
        font-size: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }

    #body-table td:last-child {
        border-right: none;
        white-space: normal;
    }

    /* 鼠标悬停效果 */
    #body-table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* 设置列宽 - 确保表头和表体对齐 */
    #header-table th:nth-child(1), #body-table th:nth-child(1), #body-table td:nth-child(1) { width: 100px; min-width: 100px; } /* 客户姓名 */
    #header-table th:nth-child(2), #body-table th:nth-child(2), #body-table td:nth-child(2) { width: 120px; min-width: 120px; } /* 住址 */
    #header-table th:nth-child(3), #body-table th:nth-child(3), #body-table td:nth-child(3) { width: 120px; min-width: 120px; } /* 使用卡号 */
    #header-table th:nth-child(4), #body-table th:nth-child(4), #body-table td:nth-child(4) { width: 100px; min-width: 100px; } /* 使用人数 */
    #header-table th:nth-child(5), #body-table th:nth-child(5), #body-table td:nth-child(5) { width: 120px; min-width: 120px; } /* 充值金额 */
    #header-table th:nth-child(6), #body-table th:nth-child(6), #body-table td:nth-child(6) { width: 120px; min-width: 120px; } /* 充值季度 */
    #header-table th:nth-child(7), #body-table th:nth-child(7), #body-table td:nth-child(7) { width: 160px; min-width: 160px; } /* 充值时间 */
    #header-table th:nth-child(8), #body-table th:nth-child(8), #body-table td:nth-child(8) { width: 150px; min-width: 150px; } /* 备注 */
    #header-table th:nth-child(9), #body-table th:nth-child(9), #body-table td:nth-child(9) { width: 100px; min-width: 100px; } /* 操作 */

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-top: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: wrap;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: white;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-left: 4px solid;
        white-space: nowrap;
    }

    .info-item:nth-child(1) { border-left-color: #007bff; }
    .info-item:nth-child(2) { border-left-color: #28a745; }
    .info-item:nth-child(3) { border-left-color: #17a2b8; }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        background: white;
        border-radius: 0.5rem;
        padding: 0.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin: 0;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-first,
    .page-link-last,
    .page-link-current {
        font-weight: 600;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4) !important;
    }

    /* 响应式分页 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
        }

        .pagination-info {
            justify-content: center;
            gap: 0.5rem;
        }

        .info-item {
            padding: 0.375rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}
