# 东悦物业管理系统 - Windows 11 快速部署指南

## 🚀 5分钟快速部署

### 前置要求
- Windows 11 系统
- 管理员权限

### 第一步：下载和安装必要软件

#### 1. 安装 Python 3.11
- 访问：https://www.python.org/downloads/windows/
- 下载 Python 3.11.x
- **重要**：安装时勾选 "Add Python to PATH"

#### 2. 安装 MySQL 8.0
- 访问：https://dev.mysql.com/downloads/mysql/
- 下载 MySQL Community Server 8.0
- 安装时设置 root 密码（记住此密码）

#### 3. 安装 Git
- 访问：https://git-scm.com/download/win
- 下载并安装 Git for Windows

#### 4. 安装 Visual C++ Build Tools
- 访问：https://visualstudio.microsoft.com/visual-cpp-build-tools/
- 下载并安装 "Microsoft C++ Build Tools"

### 第二步：一键部署脚本

以管理员身份打开 PowerShell，复制并运行以下脚本：

```powershell
# 一键部署脚本
$ProjectPath = "C:\dywy"
$DatabaseName = "dywy_db"
$DatabaseUser = "root"  # 或创建新用户
$DatabasePassword = "your_mysql_password"  # 替换为您的MySQL密码

Write-Host "=== 开始部署东悦物业管理系统 ===" -ForegroundColor Green

# 创建项目目录
Write-Host "1. 创建项目目录..." -ForegroundColor Yellow
if (!(Test-Path $ProjectPath)) {
    New-Item -ItemType Directory -Path $ProjectPath -Force
}
Set-Location $ProjectPath

# 下载项目代码（如果有Git仓库）
# git clone <项目地址> .

# 创建虚拟环境
Write-Host "2. 创建Python虚拟环境..." -ForegroundColor Yellow
python -m venv venv

# 激活虚拟环境
Write-Host "3. 激活虚拟环境..." -ForegroundColor Yellow
& "$ProjectPath\venv\Scripts\activate.ps1"

# 升级pip
python -m pip install --upgrade pip

# 安装依赖
Write-Host "4. 安装项目依赖..." -ForegroundColor Yellow
pip install Django==5.2
pip install mysqlclient==2.2.0
pip install pandas==2.0.3
pip install openpyxl==3.1.2
pip install python-dateutil==2.8.2
pip install Pillow==10.0.0
pip install python-decouple==3.8
pip install waitress==2.1.2

# 创建数据库
Write-Host "5. 创建数据库..." -ForegroundColor Yellow
$mysqlCmd = @"
CREATE DATABASE IF NOT EXISTS $DatabaseName CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
"@

mysql -u $DatabaseUser -p$DatabasePassword -e $mysqlCmd

Write-Host "=== 部署完成！===" -ForegroundColor Green
Write-Host "请将项目文件复制到 $ProjectPath 目录，然后运行初始化脚本。" -ForegroundColor Cyan
```

### 第三步：项目初始化

将项目文件复制到 `C:\dywy` 目录后，运行以下命令：

```powershell
# 进入项目目录
cd C:\dywy

# 激活虚拟环境
.\venv\Scripts\activate

# 配置数据库（编辑 property_management/settings.py）
# 将数据库配置修改为：
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'dywy_db',
#         'USER': 'root',
#         'PASSWORD': 'your_mysql_password',
#         'HOST': 'localhost',
#         'PORT': '3306',
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#         },
#     }
# }

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建管理员用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput

# 启动服务器
python manage.py runserver 0.0.0.0:8000
```

### 第四步：访问系统

打开浏览器访问：http://localhost:8000

## 🔧 快速配置脚本

### 创建启动脚本
创建 `start.bat` 文件：
```batch
@echo off
title 东悦物业管理系统
cd /d C:\dywy
call venv\Scripts\activate.bat
echo 正在启动系统...
python manage.py runserver 0.0.0.0:8000
pause
```

### 创建生产环境启动脚本
创建 `start_production.py` 文件：
```python
from waitress import serve
from property_management.wsgi import application

if __name__ == '__main__':
    print("启动生产环境服务器...")
    serve(application, host='0.0.0.0', port=8000)
```

创建 `start_production.bat` 文件：
```batch
@echo off
title 东悦物业管理系统 - 生产环境
cd /d C:\dywy
call venv\Scripts\activate.bat
echo 正在启动生产环境服务器...
python start_production.py
pause
```

## 🛠️ 常用维护命令

```powershell
# 备份数据库
mysqldump -u root -p dywy_db > backup_$(Get-Date -Format 'yyyyMMdd').sql

# 查看运行状态
Get-Process python
netstat -an | Select-String ":8000"

# 重启服务（如果作为服务运行）
Restart-Service DywyPropertyManagement

# 查看日志
Get-Content C:\dywy\logs\django.log -Tail 50
```

## ⚠️ 常见问题

**问题1：Python 命令不识别**
- 解决：重新安装 Python 并确保勾选 "Add Python to PATH"

**问题2：MySQL 连接失败**
- 解决：检查 MySQL 服务是否启动，密码是否正确

**问题3：mysqlclient 安装失败**
- 解决：确保已安装 Visual C++ Build Tools

**问题4：端口被占用**
- 解决：更改端口或结束占用进程

## 📞 技术支持

如遇到问题，请检查：
1. 所有软件是否正确安装
2. 虚拟环境是否正确激活
3. 数据库配置是否正确
4. 防火墙是否阻止了端口访问

---

**快速部署完成后，系统将在 http://localhost:8000 运行**
