# Generated by Django 5.2.3 on 2025-07-01 02:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('water_machine', '0004_remove_villagecustomer_total_amount'),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantWaterCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('address', models.CharField(max_length=200, verbose_name='住址')),
                ('phone', models.CharField(help_text='11位手机号码', max_length=11, verbose_name='电话')),
                ('water_bucket', models.PositiveIntegerField(default=0, help_text='水桶数量', verbose_name='水桶数量')),
                ('normal_card_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='正常卡号')),
                ('lost_card_numbers', models.TextField(blank=True, help_text='多个卡号用逗号分隔', null=True, verbose_name='丢失卡号')),
                ('record_time', models.DateTimeField(auto_now_add=True, verbose_name='记录时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '租户售水机卡号记录',
                'verbose_name_plural': '租户售水机卡号记录',
                'ordering': ['-record_time'],
            },
        ),
    ]
