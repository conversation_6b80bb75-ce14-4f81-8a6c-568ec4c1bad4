# Generated by Django 5.2.3 on 2025-06-30 02:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('water_machine', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='VillageCustomer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('address', models.CharField(max_length=200, verbose_name='住址')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('water_bucket', models.BooleanField(default=False, help_text='是否有水桶', verbose_name='水桶')),
                ('normal_card_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='正常卡号')),
                ('lost_card_numbers', models.TextField(blank=True, help_text='多个卡号用逗号分隔', null=True, verbose_name='丢失卡号')),
                ('people_count', models.PositiveIntegerField(default=1, verbose_name='使用人数')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='总充值金额')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '村售水机客户',
                'verbose_name_plural': '村售水机客户',
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='watercard',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='rechargerecord',
            name='water_card',
        ),
        migrations.CreateModel(
            name='VillageRechargeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(max_length=20, verbose_name='使用卡号')),
                ('people_count', models.PositiveIntegerField(verbose_name='使用人数')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='充值金额')),
                ('quarter', models.CharField(help_text='格式：2025Q1', max_length=10, verbose_name='充值季度')),
                ('recharge_date', models.DateTimeField(auto_now_add=True, verbose_name='充值时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recharge_records', to='water_machine.villagecustomer', verbose_name='客户')),
            ],
            options={
                'verbose_name': '村售水机充值记录',
                'verbose_name_plural': '村售水机充值记录',
                'ordering': ['-recharge_date'],
            },
        ),
        migrations.DeleteModel(
            name='Customer',
        ),
        migrations.DeleteModel(
            name='RechargeRecord',
        ),
        migrations.DeleteModel(
            name='WaterCard',
        ),
    ]
