# MySQL Backup Script for Scheduled Task
# This script runs once and exits (no infinite loop)
# Designed to run silently in background without showing windows

# Hide PowerShell window when running as scheduled task
Add-Type -Name Window -Namespace Console -MemberDefinition '
[DllImport("Kernel32.dll")]
public static extern IntPtr GetConsoleWindow();
[DllImport("user32.dll")]
public static extern bool ShowWindow(IntPtr hWnd, Int32 nCmdShow);
'

# Hide console window if not running interactively
if (-not [Environment]::UserInteractive) {
    $consolePtr = [Console.Window]::GetConsoleWindow()
    [Console.Window]::ShowWindow($consolePtr, 0) | Out-Null
}

# Configuration
$DB_USER = "root"
$DB_PASS = "hcd147258"
$DB_NAME = "property_management"
$BACKUP_DIR = "D:\MySQL_Backup"
$MAX_DAYS = 7

# MySQL path
$MYSQL_PATH = "E:\wang\dywy\mysql-8.4.5-winx64\mysql-8.4.5-winx64\bin\mysqldump.exe"

# Log file
$LOG_FILE = "$BACKUP_DIR\backup.log"

# Function: Write log (silent mode for scheduled tasks)
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    # Only write to console if running interactively
    if ([Environment]::UserInteractive) {
        Write-Host $logEntry
    }

    # Ensure log directory exists
    if (!(Test-Path $BACKUP_DIR)) {
        New-Item -ItemType Directory -Path $BACKUP_DIR -Force | Out-Null
    }

    # Always write to log file
    $logEntry | Out-File $LOG_FILE -Append -Encoding UTF8
}

# Function: Execute single backup
function Start-SingleBackup {
    try {
        Write-Log "=== MySQL Backup Started ==="
        
        # Create backup directory if not exists
        if (!(Test-Path $BACKUP_DIR)) {
            New-Item -ItemType Directory -Path $BACKUP_DIR -Force | Out-Null
            Write-Log "Created backup directory: $BACKUP_DIR"
        }
        
        # Check if MySQL exists
        if (!(Test-Path $MYSQL_PATH)) {
            throw "MySQL mysqldump not found at: $MYSQL_PATH"
        }
        
        # Generate backup filename
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFile = "$BACKUP_DIR\${DB_NAME}_${timestamp}.sql"
        
        Write-Log "Starting backup for database: $DB_NAME"
        Write-Log "Backup file: $(Split-Path $backupFile -Leaf)"
        
        # Execute mysqldump with proper UTF-8 encoding
        $arguments = @(
            "--user=$DB_USER",
            "--password=$DB_PASS",
            "--single-transaction",
            "--routines",
            "--triggers",
            "--default-character-set=utf8mb4",
            "--set-charset",
            "--add-drop-database",
            "--add-drop-table",
            "--comments",
            "--dump-date",
            "--lock-tables=false",
            "--hex-blob",
            "--complete-insert",
            "--extended-insert",
            "--databases",
            $DB_NAME
        )

        # Set environment variables for proper encoding
        $env:MYSQL_PWD = $DB_PASS
        $originalOutputEncoding = [Console]::OutputEncoding
        $originalInputEncoding = [Console]::InputEncoding

        try {
            # Set UTF-8 encoding for console
            [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
            [Console]::InputEncoding = [System.Text.Encoding]::UTF8

            # Create temporary file for mysqldump output
            $tempFile = "$BACKUP_DIR\temp_backup_$(Get-Date -Format 'yyyyMMddHHmmss').sql"

            # Execute mysqldump and capture output
            $processInfo = New-Object System.Diagnostics.ProcessStartInfo
            $processInfo.FileName = $MYSQL_PATH
            $processInfo.Arguments = ($arguments -join " ")
            $processInfo.UseShellExecute = $false
            $processInfo.RedirectStandardOutput = $true
            $processInfo.RedirectStandardError = $true
            $processInfo.StandardOutputEncoding = [System.Text.Encoding]::UTF8
            $processInfo.StandardErrorEncoding = [System.Text.Encoding]::UTF8
            $processInfo.CreateNoWindow = $true

            $process = New-Object System.Diagnostics.Process
            $process.StartInfo = $processInfo

            # Start process and read output
            $process.Start() | Out-Null

            # Read output with UTF-8 encoding and write to file
            $output = $process.StandardOutput.ReadToEnd()
            $errorOutput = $process.StandardError.ReadToEnd()

            $process.WaitForExit()
            $exitCode = $process.ExitCode

            Write-Log "mysqldump exit code: $exitCode"
            if ($errorOutput) {
                Write-Log "mysqldump stderr: $errorOutput" "WARN"
            }

            # Write output to backup file with UTF-8 BOM for better VSCode compatibility
            $utf8WithBom = New-Object System.Text.UTF8Encoding($true)
            [System.IO.File]::WriteAllText($backupFile, $output, $utf8WithBom)

            # Clean up temp file if exists
            if (Test-Path $tempFile) {
                Remove-Item $tempFile -Force
            }

            # Check if backup file was created and has content
            if (!(Test-Path $backupFile)) {
                throw "Backup file was not created: $backupFile"
            }

            $fileInfo = Get-Item $backupFile
            if ($fileInfo.Length -eq 0) {
                throw "Backup file is empty: $backupFile"
            }

            Write-Log "Backup file created successfully: $($fileInfo.Length) bytes"

        } finally {
            # Restore original encoding
            [Console]::OutputEncoding = $originalOutputEncoding
            [Console]::InputEncoding = $originalInputEncoding

            # Clean up environment variable
            Remove-Item Env:MYSQL_PWD -ErrorAction SilentlyContinue
        }

        # File existence and content already verified above
            $fileSize = [math]::Round((Get-Item $backupFile).Length / 1MB, 2)
            Write-Log "SQL backup completed successfully: $fileSize MB"

            # Verify backup file encoding and content for VSCode compatibility
            try {
                $sampleContent = Get-Content $backupFile -Head 50 -Encoding UTF8
                $hasUtf8Declaration = $sampleContent | Where-Object { $_ -match "utf8mb4" }
                $hasChineseChars = $sampleContent | Where-Object { $_ -match "[\u4e00-\u9fff]" }

                if ($hasUtf8Declaration) {
                    Write-Log " ?Backup contains UTF-8 character set declaration"
                } else {
                    Write-Log " ?Warning: No UTF-8 character set declaration found"
                }

                if ($hasChineseChars) {
                    Write-Log " ?Chinese characters detected (encoding correct)"
                }

                # Check if file has UTF-8 BOM for VSCode compatibility
                $bytes = [System.IO.File]::ReadAllBytes($backupFile)
                if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
                    Write-Log " ?Backup saved with UTF-8 BOM (VSCode compatible)"
                } else {
                    Write-Log " ?Backup saved without BOM"
                }

            } catch {
                Write-Log "Warning: Could not verify backup encoding: $($_.Exception.Message)" "WARN"
            }
            
            # Compress the backup file
            try {
                $zipFile = "$BACKUP_DIR\${DB_NAME}_${timestamp}.zip"
                Compress-Archive -Path $backupFile -DestinationPath $zipFile -Force
                Remove-Item $backupFile -Force
                
                $compressedSize = [math]::Round((Get-Item $zipFile).Length / 1MB, 2)
                $compressionRatio = [math]::Round((1 - $compressedSize / $fileSize) * 100, 1)
                Write-Log "Backup compressed successfully: $compressedSize MB (saved $compressionRatio%)"
                
                return $true
            } catch {
                Write-Log "Compression failed: $($_.Exception.Message)" "WARN"
                Write-Log "Keeping uncompressed SQL file" "WARN"
                return $true
            }

    } catch {
        Write-Log "Backup failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function: Clean old backups
function Remove-OldBackups {
    try {
        Write-Log "Starting cleanup of old backups..."
        $cutoffDate = (Get-Date).AddDays(-$MAX_DAYS)
        
        # Find old files
        $oldSqlFiles = Get-ChildItem -Path $BACKUP_DIR -Filter "*.sql" -ErrorAction SilentlyContinue | Where-Object { $_.LastWriteTime -lt $cutoffDate }
        $oldZipFiles = Get-ChildItem -Path $BACKUP_DIR -Filter "*.zip" -ErrorAction SilentlyContinue | Where-Object { $_.LastWriteTime -lt $cutoffDate }
        
        $allOldFiles = @()
        if ($oldSqlFiles) { $allOldFiles += $oldSqlFiles }
        if ($oldZipFiles) { $allOldFiles += $oldZipFiles }
        
        if ($allOldFiles.Count -gt 0) {
            foreach ($file in $allOldFiles) {
                Remove-Item $file.FullName -Force
                Write-Log "Deleted old backup: $($file.Name)"
            }
            Write-Log "Cleanup completed: deleted $($allOldFiles.Count) old files"
        } else {
            Write-Log "No old backup files to clean"
        }
        
    } catch {
        Write-Log "Cleanup error: $($_.Exception.Message)" "ERROR"
    }
}

# Function: Show backup statistics
function Show-BackupStats {
    try {
        $sqlFiles = Get-ChildItem -Path $BACKUP_DIR -Filter "*.sql" -ErrorAction SilentlyContinue
        $zipFiles = Get-ChildItem -Path $BACKUP_DIR -Filter "*.zip" -ErrorAction SilentlyContinue
        
        $allFiles = @()
        if ($sqlFiles) { $allFiles += $sqlFiles }
        if ($zipFiles) { $allFiles += $zipFiles }
        
        if ($allFiles.Count -gt 0) {
            $totalSize = ($allFiles | Measure-Object Length -Sum).Sum / 1MB
            $latestFile = $allFiles | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            
            Write-Log "=== Backup Statistics ==="
            Write-Log "Total backup files: $($allFiles.Count)"
            Write-Log "Total space used: $([math]::Round($totalSize, 2)) MB"
            Write-Log "Latest backup: $($latestFile.Name) ($($latestFile.LastWriteTime))"
            Write-Log "========================="
        } else {
            Write-Log "No backup files found in directory"
        }
        
    } catch {
        Write-Log "Error getting statistics: $($_.Exception.Message)" "ERROR"
    }
}

# Main execution (single run, no loop)
try {
    Write-Log "MySQL Scheduled Backup Script Started"
    Write-Log "Database: $DB_NAME"
    Write-Log "Backup Directory: $BACKUP_DIR"
    Write-Log "Retention Days: $MAX_DAYS"
    
    # Execute single backup
    $backupSuccess = Start-SingleBackup
    
    if ($backupSuccess) {
        Write-Log "Backup completed successfully"
        
        # Clean old backups
        Remove-OldBackups
        
        # Show statistics
        Show-BackupStats
        
        Write-Log "=== Backup Process Completed Successfully ==="
        exit 0
        
    } else {
        Write-Log "Backup process failed" "ERROR"
        Write-Log "=== Backup Process Failed ==="
        exit 1
    }
    
} catch {
    Write-Log "Unhandled error: $($_.Exception.Message)" "ERROR"
    Write-Log "=== Backup Process Failed with Error ==="
    exit 1
}
