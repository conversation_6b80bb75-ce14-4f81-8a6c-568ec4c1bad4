from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random

from tenants.models import Tenant


class Command(BaseCommand):
    help = '创建一些逾期租客用于测试'

    def handle(self, *args, **options):
        self.stdout.write('开始创建逾期租客...')
        
        # 创建一些逾期租客
        overdue_tenants = [
            {
                'building_number': '2',
                'room_number': '201',
                'tenant_name': '逾期张三',
                'landlord_name': '张房东',
                'landlord_phone': '13800000001',
                'overdue_days': 15
            },
            {
                'building_number': '2',
                'room_number': '202',
                'tenant_name': '逾期李四',
                'landlord_name': '李房东',
                'landlord_phone': '13800000002',
                'overdue_days': 30
            },
            {
                'building_number': '3',
                'room_number': '301',
                'tenant_name': '逾期王五',
                'landlord_name': '王房东',
                'landlord_phone': '13800000003',
                'overdue_days': 5
            }
        ]
        
        for tenant_data in overdue_tenants:
            # 检查是否已存在
            existing = Tenant.objects.filter(
                building_number=tenant_data['building_number'],
                room_number=tenant_data['room_number']
            ).first()
            
            if existing:
                # 更新为逾期状态
                existing.property_fee_due_date = timezone.now().date() - timedelta(days=tenant_data['overdue_days'])
                existing.status = 'overdue'
                existing.save()
                self.stdout.write(f'更新租客 {existing.tenant_name} 为逾期状态')
            else:
                # 创建新的逾期租客
                room_number = tenant_data['room_number']
                last_two = room_number[-2:] if len(room_number) >= 2 else ''
                area = Decimal('130.00') if last_two in ['01', '04'] else Decimal('90.00')
                
                tenant = Tenant.objects.create(
                    building_number=tenant_data['building_number'],
                    room_number=room_number,
                    area=area,
                    tenant_name=tenant_data['tenant_name'],
                    id_card=f"11010119{random.randint(80, 99)}0101{random.randint(1000, 9999)}",
                    resident_count=random.randint(1, 3),
                    landlord_name=tenant_data['landlord_name'],
                    landlord_phone=tenant_data['landlord_phone'],
                    move_in_date=timezone.now().date() - timedelta(days=random.randint(100, 300)),
                    property_fee_due_date=timezone.now().date() - timedelta(days=tenant_data['overdue_days']),
                    floor=int(room_number[0]) if room_number else 1,
                    status='overdue'
                )
                self.stdout.write(f'创建逾期租客 {tenant.tenant_name}')
        
        self.stdout.write(
            self.style.SUCCESS('逾期租客创建完成！')
        )
