{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid px-0 w-100">
    <!-- 页面标题 -->

    <div class="row w-100 mx-0">
        <div class="col-12 px-0">
            <div class="card w-100">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>租客列表
                        <span class="badge bg-primary ms-2">{{ total_count }}户</span>
                        <span class="badge bg-success ms-1">{{ total_residents }}人</span>
                    </h4>
                </div>

              

                <div class="card-body" style="padding-bottom: 0.5rem;">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <!-- 所有按钮放在同一行 -->
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{% url 'tenants:add' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>新增租客
                                    </a>
                                    <a href="{% url 'tenants:import' %}" class="btn btn-success">
                                        <i class="fas fa-upload me-1"></i>批量导入
                                    </a>
                                    <a href="{% url 'tenants:export' %}" class="btn btn-info" id="export-btn">
                                        <i class="fas fa-download me-1"></i>批量导出
                                    </a>
                                    <a href="{% url 'tenants:overdue' %}" class="btn btn-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>物业费逾期列表
                                    </a>
                                    <a href="{% url 'tenants:checkout' %}" class="btn btn-secondary">
                                        <i class="fas fa-sign-out-alt me-1"></i>退房列表
                                    </a>
                                    <a href="{% url 'tenants:payment_history' %}" class="btn btn-success">
                                        <i class="fas fa-history me-1"></i>物业费流水
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex justify-content-end gap-2">
                                    <div class="search-box" style="width: 200px;">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    {{ search_form.search_type }}
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                    <a href="{% url 'tenants:list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo me-1"></i>重置
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th>楼号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>租客姓名</th>
                                        <th>身份证号</th>
                                        <th>租客电话</th>
                                        <th>租住人数</th>
                                        <th>房东姓名</th>
                                        <th>房东电话</th>
                                        <th>入住时间</th>
                                        <th>物业费到期时间</th>
                                        <th>剩余天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th>楼号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>租客姓名</th>
                                        <th>身份证号</th>
                                        <th>租客电话</th>
                                        <th>租住人数</th>
                                        <th>房东姓名</th>
                                        <th>房东电话</th>
                                        <th>入住时间</th>
                                        <th>物业费到期时间</th>
                                        <th>剩余天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for tenant in tenants %}
                                    <tr>
                                        <td>{{ tenant.building_number }}</td>
                                        <td>{{ tenant.room_number }}</td>
                                        <td>{{ tenant.area }}㎡</td>
                                        <td class="name-cell">{{ tenant.tenant_name }}</td>
                                        <td>{{ tenant.id_card }}</td>
                                        <td class="phone-cell">{{ tenant.get_formatted_phone|safe }}</td>
                                        <td>{{ tenant.resident_count }}人</td>
                                        <td class="name-cell">{{ tenant.landlord_name }}</td>
                                        <td>{{ tenant.landlord_phone }}</td>
                                        <td>{{ tenant.move_in_date }}</td>
                                        <td>{{ tenant.property_fee_due_date }}</td>
                                        <td>
                                            {% if tenant.days_until_due <= 7 %}
                                                <span class="badge bg-warning">{{ tenant.days_until_due }}天</span>
                                            {% else %}
                                                <span class="badge bg-success">{{ tenant.days_until_due }}天</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="action-buttons-container">
                                                <a href="{% url 'tenants:edit' tenant.pk %}" class="action-btn edit-btn" title="编辑租客信息">
                                                    <i class="fas fa-edit"></i>
                                                    <span>编辑</span>
                                                </a>
                                                <a href="{% url 'tenants:renew' tenant.pk %}" class="action-btn renew-btn" title="续费缴费">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                    <span>续费</span>
                                                </a>
                                                <form method="post" action="{% url 'tenants:checkout_action' tenant.pk %}?next=tenants:list" style="display: inline;">
                                                    {% csrf_token %}
                                                    <button type="submit" class="action-btn checkout-btn" title="办理退房"
                                                            onclick="return confirm('确定要将此租客标记为退房吗？')">
                                                        <i class="fas fa-sign-out-alt"></i>
                                                        <span>退房</span>
                                                    </button>
                                                </form>
                                                <a href="{% url 'tenants:delete' tenant.pk %}" class="action-btn delete-btn" title="删除租客">
                                                    <i class="fas fa-trash"></i>
                                                    <span>删除</span>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="13" class="text-center py-4">
                                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">暂无租客数据</p>
                                            <a href="{% url 'tenants:add' %}" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i>添加第一个租客
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    /* 电话号码显示样式 */
    .phone-cell {
        line-height: 1.2;
        vertical-align: top;
        padding: 8px 12px;
        max-width: 150px;
    }

    .phone-number {
        margin: 1px 0;
        word-break: break-all;
    }

    .phone-number.primary {
        font-weight: 500;
        color: #333;
    }

    .phone-number.secondary {
        font-size: 0.9em;
        color: #666;
        margin-top: 2px;
    }

    /* 确保表格行高度自适应 */
    .table td {
        vertical-align: top;
    }

    /* 租客管理专用渐变流光按钮风格 */
    .action-buttons-container {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        padding: 12px;
        position: relative;
        z-index: 200;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
    }

    .action-btn {
        position: relative;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 10px 16px;
        border-radius: 25px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        min-width: 80px;
        min-height: 40px;
        overflow: hidden;
        white-space: nowrap;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 300% 300%;
        color: white;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        box-shadow:
            0 6px 20px rgba(102, 126, 234, 0.3),
            0 3px 10px rgba(118, 75, 162, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        animation: gradientFlow 4s ease infinite;
        transform: translateZ(0);
        z-index: 201;
    }

    @keyframes gradientFlow {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .action-btn i {
        font-size: 14px;
        margin-right: 6px;
        transition: all 0.4s ease;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        opacity: 0.95;
    }

    .action-btn span {
        font-size: 12px;
        font-weight: 700;
        position: relative;
        z-index: 2;
        transition: all 0.4s ease;
        text-transform: none;
        letter-spacing: 0.5px;
        white-space: nowrap;
    }

    .action-btn:hover {
        transform: translateY(-3px) scale(1.05);
        text-decoration: none;
        box-shadow:
            0 12px 35px rgba(102, 126, 234, 0.4),
            0 6px 20px rgba(118, 75, 162, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        animation: gradientFlow 2s ease infinite, streamingLight 1.5s ease infinite;
    }

    @keyframes streamingLight {
        0% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.4),
                0 6px 20px rgba(118, 75, 162, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
        50% {
            box-shadow:
                0 12px 35px rgba(245, 87, 108, 0.4),
                0 6px 20px rgba(240, 147, 251, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }
        100% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.4),
                0 6px 20px rgba(118, 75, 162, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }
    }

    .action-btn:hover i {
        transform: scale(1.15) rotate(8deg);
        filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.3));
        opacity: 1;
    }

    .action-btn:hover span {
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        transform: translateY(-1px);
    }

    .action-btn:active {
        transform: translateY(-1px) scale(1.02);
        transition: all 0.1s ease;
        box-shadow:
            0 6px 20px rgba(102, 126, 234, 0.3),
            0 3px 10px rgba(118, 75, 162, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* 编辑按钮 - 蓝紫流光渐变 */
    .edit-btn {
        background: linear-gradient(45deg, #4f46e5, #7c3aed, #06b6d4, #3b82f6);
        background-size: 300% 300%;
        animation: gradientFlow 3s ease infinite;
    }

    .edit-btn:hover {
        background: linear-gradient(45deg, #4f46e5, #7c3aed, #06b6d4, #3b82f6);
        background-size: 300% 300%;
        animation: gradientFlow 1.5s ease infinite, editGlow 2s ease-in-out infinite alternate;
    }

    @keyframes editGlow {
        from {
            box-shadow:
                0 12px 35px rgba(79, 70, 229, 0.4),
                0 6px 20px rgba(124, 58, 237, 0.3),
                0 0 20px rgba(6, 182, 212, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(79, 70, 229, 0.6),
                0 6px 20px rgba(124, 58, 237, 0.5),
                0 0 30px rgba(59, 130, 246, 0.7);
        }
    }

    /* 续费按钮 - 绿青流光渐变 */
    .renew-btn {
        background: linear-gradient(45deg, #10b981, #06d6a0, #34d399, #6ee7b7);
        background-size: 300% 300%;
        animation: gradientFlow 3.5s ease infinite;
    }

    .renew-btn:hover {
        background: linear-gradient(45deg, #10b981, #06d6a0, #34d399, #6ee7b7);
        background-size: 300% 300%;
        animation: gradientFlow 1.8s ease infinite, renewGlow 2s ease-in-out infinite alternate;
    }

    @keyframes renewGlow {
        from {
            box-shadow:
                0 12px 35px rgba(16, 185, 129, 0.4),
                0 6px 20px rgba(6, 214, 160, 0.3),
                0 0 20px rgba(52, 211, 153, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(16, 185, 129, 0.6),
                0 6px 20px rgba(6, 214, 160, 0.5),
                0 0 30px rgba(110, 231, 183, 0.7);
        }
    }

    /* 退房按钮 - 橙红流光渐变 */
    .checkout-btn {
        background: linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899);
        background-size: 300% 300%;
        animation: gradientFlow 4s ease infinite;
    }

    .checkout-btn:hover {
        background: linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899);
        background-size: 300% 300%;
        animation: gradientFlow 2s ease infinite, checkoutGlow 2s ease-in-out infinite alternate;
    }

    @keyframes checkoutGlow {
        from {
            box-shadow:
                0 12px 35px rgba(245, 158, 11, 0.4),
                0 6px 20px rgba(249, 115, 22, 0.3),
                0 0 20px rgba(239, 68, 68, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(245, 158, 11, 0.6),
                0 6px 20px rgba(249, 115, 22, 0.5),
                0 0 30px rgba(236, 72, 153, 0.7);
        }
    }

    /* 恢复按钮 - 青蓝流光渐变 */
    .restore-btn {
        background: linear-gradient(45deg, #06b6d4, #0ea5e9, #3b82f6, #8b5cf6);
        background-size: 300% 300%;
        animation: gradientFlow 3.2s ease infinite;
    }

    .restore-btn:hover {
        background: linear-gradient(45deg, #06b6d4, #0ea5e9, #3b82f6, #8b5cf6);
        background-size: 300% 300%;
        animation: gradientFlow 1.6s ease infinite, restoreGlow 2s ease-in-out infinite alternate;
    }

    @keyframes restoreGlow {
        from {
            box-shadow:
                0 12px 35px rgba(6, 182, 212, 0.4),
                0 6px 20px rgba(14, 165, 233, 0.3),
                0 0 20px rgba(59, 130, 246, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(6, 182, 212, 0.6),
                0 6px 20px rgba(14, 165, 233, 0.5),
                0 0 30px rgba(139, 92, 246, 0.7);
        }
    }

    /* 删除按钮 - 红粉流光渐变 */
    .delete-btn {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientFlow 2.8s ease infinite;
    }

    .delete-btn:hover {
        background: linear-gradient(45deg, #ef4444, #f43f5e, #ec4899, #d946ef);
        background-size: 300% 300%;
        animation: gradientFlow 1.4s ease infinite, deleteGlow 2s ease-in-out infinite alternate;
    }

    @keyframes deleteGlow {
        from {
            box-shadow:
                0 12px 35px rgba(239, 68, 68, 0.4),
                0 6px 20px rgba(244, 63, 94, 0.3),
                0 0 20px rgba(236, 72, 153, 0.5);
        }
        to {
            box-shadow:
                0 12px 35px rgba(239, 68, 68, 0.6),
                0 6px 20px rgba(244, 63, 94, 0.5),
                0 0 30px rgba(217, 70, 239, 0.7);
        }
    }

    /* 流光扫过效果 */
    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
        border-radius: 25px;
        z-index: 1;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    /* 内部高光效果 */
    .action-btn::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        height: 50%;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
        border-radius: 23px 23px 0 0;
        z-index: 2;
    }

    /* 流光脉冲效果 */
    .action-btn:active {
        animation: lightPulse 0.3s ease-out;
    }

    @keyframes lightPulse {
        0% {
            box-shadow:
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 3px 10px rgba(118, 75, 162, 0.2);
        }
        50% {
            box-shadow:
                0 12px 35px rgba(102, 126, 234, 0.6),
                0 6px 20px rgba(118, 75, 162, 0.4),
                0 0 30px rgba(240, 147, 251, 0.5);
        }
        100% {
            box-shadow:
                0 6px 20px rgba(102, 126, 234, 0.3),
                0 3px 10px rgba(118, 75, 162, 0.2);
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .action-buttons-container {
            gap: 6px;
        }

        .action-btn {
            padding: 6px 12px;
            min-width: 60px;
            font-size: 11px;
        }

        .action-btn i {
            font-size: 13px;
            margin-right: 4px;
        }

        .action-btn span {
            font-size: 10px;
        }
    }
    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin-bottom: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1280px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
    
    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin-bottom: 0 !important;
        margin-top: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1280px;
    }

    /* 表格容器间距最小化 */
    .table-container {
        margin-bottom: 0 !important;
    }

    #header-table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1280px;
    }

    /* 表体隐藏表头 */
    #body-table thead {
        visibility: hidden;
        height: 0 !important;
        line-height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    #body-table thead th {
        padding: 0 !important;
        height: 0 !important;
        border: none !important;
        margin: 0 !important;
        line-height: 0 !important;
    }
    
    /* 表格内容样式 */
    #body-table td {
        padding: 0.6rem 0.5rem;
        text-align: center;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
        border-right: 1px solid #dee2e6;
        font-size: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }
    
    #tenant-table td:last-child {
        border-right: none;
        white-space: normal;
    }
    
    /* 身份证号列不截断 */
    #tenant-table td:nth-child(5) {
        white-space: normal;
        word-break: break-all;
    }
    
    /* 鼠标悬停效果 */
    #tenant-table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }
    
    /* 设置列宽 */
    #tenant-table th:nth-child(1), #tenant-table td:nth-child(1) { width: 4%; min-width: 50px; } /* 楼号 */
    #tenant-table th:nth-child(2), #tenant-table td:nth-child(2) { width: 4%; min-width: 50px; } /* 房号 */
    #tenant-table th:nth-child(3), #tenant-table td:nth-child(3) { width: 5%; min-width: 60px; } /* 平米数 */
    #tenant-table th:nth-child(4), #tenant-table td:nth-child(4) { width: 7%; min-width: 85px; } /* 租客姓名 */
    #tenant-table th:nth-child(5), #tenant-table td:nth-child(5) { width: 13%; min-width: 165px; } /* 身份证号 */
    #tenant-table th:nth-child(6), #tenant-table td:nth-child(6) { width: 9%; min-width: 105px; } /* 租客电话 */
    #tenant-table th:nth-child(7), #tenant-table td:nth-child(7) { width: 5%; min-width: 60px; } /* 租住人数 */
    #tenant-table th:nth-child(8), #tenant-table td:nth-child(8) { width: 6%; min-width: 75px; } /* 房东姓名 */
    #tenant-table th:nth-child(9), #tenant-table td:nth-child(9) { width: 9%; min-width: 105px; } /* 房东电话 */
    #tenant-table th:nth-child(10), #tenant-table td:nth-child(10) { width: 8%; min-width: 95px; } /* 入住时间 */
    #tenant-table th:nth-child(11), #tenant-table td:nth-child(11) { width: 9%; min-width: 105px; } /* 物业费到期时间 */
    #tenant-table th:nth-child(12), #tenant-table td:nth-child(12) { width: 6%; min-width: 70px; } /* 剩余天数 */
    #tenant-table th:nth-child(13), #tenant-table td:nth-child(13) { width: 21%; min-width: 290px; } /* 操作 */

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }

        .table-wrapper {
            max-height: 60vh;
        }

        #tenant-table th,
        #tenant-table td {
            font-size: 14px;
            padding: 0.5rem 0.4rem;
        }
    }
    
    /* 浏览器兼容性修复 */
    @supports (-webkit-overflow-scrolling: touch) {
        /* iOS Safari 特定修复 */
        .table-wrapper {
            -webkit-overflow-scrolling: touch;
        }
        
        #tenant-table thead {
            position: -webkit-sticky;
            position: sticky;
        }
    }
    
    @supports (position: -webkit-sticky) or (position: sticky) {
        /* 现代浏览器支持 sticky */
        #tenant-table th {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
        }
    }

    /* 强制姓名单元格内容完全显示 */
    .table td.name-cell {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4 !important;
    }

    /* 确保姓名单元格内的div也支持换行 */
    .table td.name-cell div {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4 !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头和表体列宽
        function syncColumnWidths() {
            const headerTable = document.getElementById('header-table');
            const bodyTable = document.getElementById('body-table');

            if (!headerTable || !bodyTable) {
                console.log('表头或表体表格未找到');
                return;
            }

            const headerCells = headerTable.querySelectorAll('th');
            const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

            // 定义列宽 - 调整入住时间和物业费到期时间列宽
            const columnWidths = [
                '4%',   // 楼号
                '4%',   // 房号
                '5%',   // 平米数
                '7%',   // 租客姓名
                '13%',  // 身份证号
                '9%',   // 租客电话
                '5%',   // 租住人数
                '6%',   // 房东姓名
                '9%',   // 房东电话
                '8%',   // 入住时间
                '9%',   // 物业费到期时间
                '6%',   // 剩余天数
                '21%'   // 操作 - 适中宽度容纳3个按钮
            ];

            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 设置表体隐藏表头列宽（用于对齐）
            bodyHeaderCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 确保两个表格宽度一致
            headerTable.style.width = '100%';
            bodyTable.style.width = '100%';
            headerTable.style.minWidth = '1280px';
            bodyTable.style.minWidth = '1280px';

            console.log('✅ 表头表体列宽同步完成');
            
            // 监听滚动事件，增强视觉效果
            tableWrapper.addEventListener('scroll', function() {
                if (tableWrapper.scrollTop > 0) {
                    thead.classList.add('scrolled');
                    // 增强阴影效果
                    headerCells.forEach(th => {
                        th.style.boxShadow = 'inset 0 -1px 0 #dee2e6, 0 2px 4px rgba(0,0,0,0.15)';
                    });
                } else {
                    thead.classList.remove('scrolled');
                    // 恢复默认阴影
                    headerCells.forEach(th => {
                        th.style.boxShadow = 'inset 0 -1px 0 #dee2e6, 0 1px 2px rgba(0,0,0,0.1)';
                    });
                }
            });
        }
        
        // 设置列宽
        function setColumnWidths() {
            const table = document.getElementById('tenant-table');
            if (!table) return;
            
            // 列宽配置（百分比和最小宽度）
            const columnConfig = [
                { width: '4%', minWidth: '50px' },  // 楼号
                { width: '4%', minWidth: '50px' },  // 房号
                { width: '5%', minWidth: '60px' },  // 平米数
                { width: '7%', minWidth: '85px' },  // 租客姓名
                { width: '13%', minWidth: '165px' }, // 身份证号
                { width: '9%', minWidth: '105px' }, // 租客电话
                { width: '5%', minWidth: '60px' },  // 租住人数
                { width: '6%', minWidth: '75px' },  // 房东姓名
                { width: '9%', minWidth: '105px' }, // 房东电话
                { width: '8%', minWidth: '95px' },  // 入住时间
                { width: '9%', minWidth: '105px' }, // 物业费到期时间
                { width: '6%', minWidth: '70px' },  // 剩余天数
                { width: '21%', minWidth: '290px' } // 操作
            ];
            
            // 计算表格最小总宽度
            const totalMinWidth = columnConfig.reduce((sum, col) => {
                return sum + parseInt(col.minWidth, 10);
            }, 0);
            
            // 设置表格最小宽度
            table.style.minWidth = totalMinWidth + 'px';
            
            // 获取所有表头单元格和表格行
            const headerCells = table.querySelectorAll('thead th');
            const rows = table.querySelectorAll('tbody tr');
            
            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnConfig.length) {
                    th.style.width = columnConfig[index].width;
                    th.style.minWidth = columnConfig[index].minWidth;
                }
            });
            
            // 设置表体列宽
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach((td, index) => {
                    if (index < columnConfig.length) {
                        td.style.width = columnConfig[index].width;
                        td.style.minWidth = columnConfig[index].minWidth;
                    }
                });
            });
            
            console.log('表格列宽设置完成，最小总宽度：' + totalMinWidth + 'px');
        }
        
        // 浏览器兼容性检测和修复
        function applyBrowserFixes() {
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            const tableWrapper = document.querySelector('.table-wrapper');
            
            if (isIOS || isSafari) {
                // 对iOS和Safari应用特定修复
                tableWrapper.style.WebkitOverflowScrolling = 'touch';
                
                // Safari特定的表头固定修复
                const headerCells = document.querySelectorAll('#tenant-table th');
                headerCells.forEach(th => {
                    th.style.position = '-webkit-sticky';
                    th.style.transform = 'translateZ(0)';
                });
            }
            
            // 检测是否支持sticky定位
            const supportsSticky = CSS.supports('position', 'sticky') || 
                                 CSS.supports('position', '-webkit-sticky');
            
            if (!supportsSticky) {
                // 对不支持sticky的浏览器使用备用方案
                console.log('浏览器不支持sticky定位，应用备用方案');
                
                // 使用JavaScript滚动事件模拟固定表头
                tableWrapper.addEventListener('scroll', function() {
                    const thead = document.querySelector('#tenant-table thead');
                    if (thead) {
                        thead.style.transform = `translateY(${this.scrollTop}px)`;
                    }
                });
            }
        }
        
        // 初始化函数
        function initTable() {
            syncColumnWidths();

            // 窗口大小改变时重新设置
            window.addEventListener('resize', function() {
                syncColumnWidths();
                console.log('窗口大小改变，表格样式已重新应用');
            });
        }

        // 立即初始化
        initTable();

        // 确保在页面完全加载后再次应用
        window.addEventListener('load', function() {
            setTimeout(initTable, 200);
        });

        // 退房确认增强
        document.querySelectorAll('form[action*="checkout_action"]').forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('确定要将此租客标记为退房吗？退房后租客将转入退房列表。')) {
                    this.submit();
                }
            });
        });

        // 导出按钮处理 - 传递当前搜索参数
        document.getElementById('export-btn').addEventListener('click', function(e) {
            e.preventDefault();

            // 获取当前搜索参数
            const urlParams = new URLSearchParams(window.location.search);
            const search = urlParams.get('search');

            // 构建导出URL
            let exportUrl = '{% url "tenants:export" %}';
            if (search) {
                exportUrl += '?search=' + encodeURIComponent(search);
            }

            // 跳转到导出URL
            window.location.href = exportUrl;
        });
    });
</script>
{% endblock %}
