{% extends 'base/base.html' %}
{% load static %}

{% block title %}车位续费 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2 text-success"></i>车位续费
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 车位信息 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-car me-2"></i>车位信息
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>车位号：</strong>{{ parking.parking_number }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>租户姓名：</strong>{{ parking.tenant_name }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>车牌号：</strong>{{ parking.license_plate }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>当前到期：</strong>
                                        <span class="text-danger">{{ parking.property_fee_due_date }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" id="renewForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.months.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>续费月数 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.months }}
                                    {% if form.months.errors %}
                                        <div class="text-danger small">{{ form.months.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">请输入1-24之间的月数</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.payment_method.id_for_label }}" class="form-label">
                                        <i class="fas fa-credit-card me-1"></i>缴费方式 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.payment_method }}
                                    {% if form.payment_method.errors %}
                                        <div class="text-danger small">{{ form.payment_method.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>备注
                                    </label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 费用计算 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-calculator me-2"></i>费用计算
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>月物业费：</strong>¥20.00
                                        </div>
                                        <div class="col-md-3">
                                            <strong>续费月数：</strong><span id="displayMonths">1</span>个月
                                        </div>
                                        <div class="col-md-3">
                                            <strong>应缴金额：</strong><span id="totalAmount" class="text-success fw-bold">¥20.00</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>新到期日期：</strong><span id="newDueDate" class="text-primary fw-bold">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'parking:list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-money-bill-wave me-1"></i>确认续费
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .alert-info {
        border-left: 4px solid #17a2b8;
    }
    
    .alert-success {
        border-left: 4px solid #28a745;
    }
    
    .form-label {
        font-weight: 500;
        color: #495057;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const monthsInput = document.getElementById('{{ form.months.id_for_label }}');
        const displayMonths = document.getElementById('displayMonths');
        const totalAmount = document.getElementById('totalAmount');
        const newDueDate = document.getElementById('newDueDate');
        
        // 当前到期日期
        const currentDueDate = new Date('{{ parking.property_fee_due_date|date:"Y-m-d" }}');
        
        function updateCalculation() {
            const months = parseInt(monthsInput.value) || 1;
            const monthlyFee = 20.00;
            const total = months * monthlyFee;
            
            displayMonths.textContent = months;
            totalAmount.textContent = '¥' + total.toFixed(2);
            
            // 计算新到期日期 - 按照新的计费逻辑
            // 费用期间从当前到期日期的下一天开始
            const feeStartDate = new Date(currentDueDate);
            feeStartDate.setDate(feeStartDate.getDate() + 1);
            
            // 计算新的到期日期
            const newDate = new Date(feeStartDate);
            newDate.setMonth(newDate.getMonth() + months);
            newDate.setDate(newDate.getDate() - 1);
            
            newDueDate.textContent = newDate.toISOString().split('T')[0];
        }
        
        // 监听月数变化
        monthsInput.addEventListener('input', updateCalculation);
        
        // 初始计算
        updateCalculation();
        
        // 表单提交确认
        document.getElementById('renewForm').addEventListener('submit', function(e) {
            const months = parseInt(monthsInput.value) || 0;
            const total = months * 20;
            
            if (months < 1 || months > 24) {
                alert('续费月数必须在1-24之间');
                e.preventDefault();
                return;
            }
            
            if (!confirm(`确定要为车位 {{ parking.parking_number }} 续费 ${months} 个月吗？\n应缴金额：¥${total.toFixed(2)}`)) {
                e.preventDefault();
            }
        });
    });
</script>
{% endblock %}
