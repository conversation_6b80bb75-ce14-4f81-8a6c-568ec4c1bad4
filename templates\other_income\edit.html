{% extends 'base/base.html' %}
{% load static %}

{% block title %}编辑收入记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>编辑收入记录
                    </h4>
                </div>

                <div class="card-body">
                    <form method="post" id="incomeForm">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.income_date.id_for_label }}" class="form-label">收入日期 <span class="text-danger">*</span></label>
                                <input type="date" name="income_date" class="form-control" value="{{ object.income_date|date:'Y-m-d' }}" required>
                                {% if form.income_date.errors %}
                                    <div class="text-danger">{{ form.income_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <h6 class="text-primary">收入明细（填写有金额的项目）</h6>
                                <small class="text-muted">总金额将自动计算各项费用之和</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="{{ form.water_fee.id_for_label }}" class="form-label">水费</label>
                                {{ form.water_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.storage_electricity_fee.id_for_label }}" class="form-label">储藏间电费</label>
                                {{ form.storage_electricity_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.water_purifier_fee.id_for_label }}" class="form-label">净水机费</label>
                                {{ form.water_purifier_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.water_card_fee.id_for_label }}" class="form-label">净水卡费</label>
                                {{ form.water_card_fee }}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="{{ form.water_bucket_fee.id_for_label }}" class="form-label">水桶费</label>
                                {{ form.water_bucket_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.shop_water_fee.id_for_label }}" class="form-label">商铺水费</label>
                                {{ form.shop_water_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.water_card_supplement_fee.id_for_label }}" class="form-label">补自来水卡费</label>
                                {{ form.water_card_supplement_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.water_purifier_card_fee.id_for_label }}" class="form-label">补净水机卡费</label>
                                {{ form.water_purifier_card_fee }}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="{{ form.parking_fee.id_for_label }}" class="form-label">停车场收费</label>
                                {{ form.parking_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.elevator_card_fee.id_for_label }}" class="form-label">电梯梯控卡费</label>
                                {{ form.elevator_card_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.water_meter_battery_fee.id_for_label }}" class="form-label">自来水表电池费</label>
                                {{ form.water_meter_battery_fee }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ form.other_fee.id_for_label }}" class="form-label">其它费用</label>
                                {{ form.other_fee }}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">总金额预览</label>
                                <div class="form-control bg-light" id="totalAmount">¥{{ object.amount|floatformat:2 }}</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">备注</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">{{ form.notes.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存
                                </button>
                                <a href="{% url 'other_income:list' %}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i>返回
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 计算总金额
    function calculateTotal() {
        let total = 0;
        const feeInputs = document.querySelectorAll('.fee-input');
        
        feeInputs.forEach(input => {
            const value = parseFloat(input.value) || 0;
            total += value;
        });
        
        document.getElementById('totalAmount').textContent = '¥' + total.toFixed(2);
    }
    
    // 监听所有费用输入框的变化
    document.querySelectorAll('.fee-input').forEach(input => {
        input.addEventListener('input', calculateTotal);
    });
    
    // 初始计算
    calculateTotal();
});
</script>
{% endblock %}
