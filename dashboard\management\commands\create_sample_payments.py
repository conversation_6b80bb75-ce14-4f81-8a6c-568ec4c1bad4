from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
import random

from tenants.models import Tenant, TenantPaymentHistory
from commercial_properties.models import CommercialProperty, CommercialPropertyPaymentHistory
from shops.models import Shop, ShopPaymentHistory
from parking.models import ParkingSpace, ParkingPaymentHistory


class Command(BaseCommand):
    help = '创建示例支付记录数据'

    def handle(self, *args, **options):
        self.stdout.write('开始创建示例支付记录...')
        
        # 创建租客支付记录
        self.create_tenant_payments()
        
        # 创建商品房支付记录
        self.create_commercial_payments()
        
        # 创建商铺支付记录
        self.create_shop_payments()
        
        # 创建车位支付记录
        self.create_parking_payments()
        
        self.stdout.write(self.style.SUCCESS('示例支付记录创建完成！'))

    def create_tenant_payments(self):
        """创建租客支付记录"""
        tenants = Tenant.objects.filter(status='active')[:5]
        if not tenants:
            self.stdout.write('没有找到活跃的租客记录')
            return
            
        for i, tenant in enumerate(tenants):
            # 创建最近几天的支付记录
            for days_ago in range(1, 8):
                payment_date = timezone.now() - timedelta(days=days_ago)
                amount = Decimal(str(random.uniform(300, 600)))
                
                TenantPaymentHistory.objects.get_or_create(
                    tenant=tenant,
                    payment_date=payment_date.date(),
                    defaults={
                        'amount': amount,
                        'payment_period_start': payment_date.date(),
                        'payment_period_end': payment_date.date() + timedelta(days=30),
                        'notes': f'物业费支付 - {payment_date.strftime("%Y年%m月")}',
                    }
                )
        
        self.stdout.write('租客支付记录创建完成')

    def create_commercial_payments(self):
        """创建商品房支付记录"""
        properties = CommercialProperty.objects.filter(status='active')[:3]
        if not properties:
            self.stdout.write('没有找到活跃的商品房记录')
            return
            
        for property in properties:
            # 创建最近几天的支付记录
            for days_ago in range(2, 10, 2):
                payment_date = timezone.now() - timedelta(days=days_ago)
                amount = Decimal(str(random.uniform(400, 800)))
                
                CommercialPropertyPaymentHistory.objects.get_or_create(
                    property=property,
                    payment_date=payment_date.date(),
                    defaults={
                        'amount': amount,
                        'payment_period_start': payment_date.date(),
                        'payment_period_end': payment_date.date() + timedelta(days=30),
                        'notes': f'物业费支付 - {payment_date.strftime("%Y年%m月")}',
                    }
                )
        
        self.stdout.write('商品房支付记录创建完成')

    def create_shop_payments(self):
        """创建商铺支付记录"""
        shops = Shop.objects.filter(status='active')[:3]
        if not shops:
            self.stdout.write('没有找到活跃的商铺记录')
            return
            
        for shop in shops:
            # 创建最近几天的支付记录
            for days_ago in range(3, 12, 3):
                payment_date = timezone.now() - timedelta(days=days_ago)
                amount = Decimal(str(random.uniform(600, 1200)))
                
                ShopPaymentHistory.objects.get_or_create(
                    shop=shop,
                    payment_date=payment_date.date(),
                    defaults={
                        'amount': amount,
                        'payment_period_start': payment_date.date(),
                        'payment_period_end': payment_date.date() + timedelta(days=30),
                        'notes': f'物业费支付 - {payment_date.strftime("%Y年%m月")}',
                    }
                )
        
        self.stdout.write('商铺支付记录创建完成')

    def create_parking_payments(self):
        """创建车位支付记录"""
        parking_spaces = ParkingSpace.objects.filter(status='active')[:3]
        if not parking_spaces:
            self.stdout.write('没有找到活跃的车位记录')
            return
            
        for parking in parking_spaces:
            # 创建最近几天的支付记录
            for days_ago in range(1, 15, 4):
                payment_date = timezone.now() - timedelta(days=days_ago)
                amount = Decimal(str(random.uniform(150, 300)))
                
                ParkingPaymentHistory.objects.get_or_create(
                    parking=parking,
                    payment_date=payment_date.date(),
                    defaults={
                        'amount': amount,
                        'payment_period_start': payment_date.date(),
                        'payment_period_end': payment_date.date() + timedelta(days=30),
                        'notes': f'停车费支付 - {payment_date.strftime("%Y年%m月")}',
                    }
                )
        
        self.stdout.write('车位支付记录创建完成')
