from django.urls import path
from . import views

app_name = 'parking'

urlpatterns = [
    path('', views.ParkingListView.as_view(), name='list'),
    path('add/', views.ParkingCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', views.ParkingUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.ParkingDeleteView.as_view(), name='delete'),
    path('overdue/', views.OverdueParkingListView.as_view(), name='overdue'),
    path('checkout/', views.CheckoutParkingListView.as_view(), name='checkout'),
    path('payment-history/', views.PaymentHistoryListView.as_view(), name='payment_history'),
    path('<int:pk>/renew/', views.RenewParkingView.as_view(), name='renew'),
    path('<int:pk>/checkout-action/', views.CheckoutParkingView.as_view(), name='checkout_action'),
    path('<int:pk>/restore/', views.RestoreParkingView.as_view(), name='restore'),
    path('export/', views.ExportParkingView.as_view(), name='export'),
    path('export-overdue/', views.ExportOverdueParkingView.as_view(), name='export_overdue'),
    path('export-checkout/', views.ExportCheckoutParkingView.as_view(), name='export_checkout'),
    path('export-payment-history/', views.ExportParkingPaymentHistoryView.as_view(), name='export_payment_history'),
    path('batch-delete-payment/', views.BatchDeleteParkingPaymentView.as_view(), name='batch_delete_payment'),
    path('import/', views.ImportParkingView.as_view(), name='import'),
    path('download-template/', views.DownloadParkingTemplateView.as_view(), name='download_template'),
]
