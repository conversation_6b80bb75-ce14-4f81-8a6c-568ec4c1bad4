{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-car me-2"></i>{{ title }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.parking_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-parking me-1"></i>车位号 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.parking_number }}
                                    {% if form.parking_number.errors %}
                                        <div class="text-danger small">{{ form.parking_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.room_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-door-open me-1"></i>房号
                                    </label>
                                    {{ form.room_number }}
                                    {% if form.room_number.errors %}
                                        <div class="text-danger small">{{ form.room_number.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">格式示例：3-1001 或 9-1-1001</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.tenant_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>租户姓名 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.tenant_name }}
                                    {% if form.tenant_name.errors %}
                                        <div class="text-danger small">{{ form.tenant_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>电话 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-car me-1"></i>车牌号
                                    </label>
                                    <div class="input-group">
                                        {{ form.license_plate_province }}
                                        {{ form.license_plate_number }}
                                    </div>
                                    {% if form.license_plate_province.errors %}
                                        <div class="text-danger small">{{ form.license_plate_province.errors.0 }}</div>
                                    {% endif %}
                                    {% if form.license_plate_number.errors %}
                                        <div class="text-danger small">{{ form.license_plate_number.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">选择省份简称 + 输入号码（如：京A12345）</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.owner_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>车位所有者
                                    </label>
                                    {{ form.owner_name }}
                                    {% if form.owner_name.errors %}
                                        <div class="text-danger small">{{ form.owner_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.owner_phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone-alt me-1"></i>所有者电话
                                    </label>
                                    {{ form.owner_phone }}
                                    {% if form.owner_phone.errors %}
                                        <div class="text-danger small">{{ form.owner_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.lease_start_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>租车位时间 <span class="text-danger">*</span>
                                    </label>
                                    {% if form.instance.pk and form.instance.lease_start_date %}
                                        <input type="date" class="form-control" name="lease_start_date"
                                               id="{{ form.lease_start_date.id_for_label }}"
                                               value="{{ form.instance.lease_start_date|date:'Y-m-d' }}" required>
                                    {% else %}
                                        {{ form.lease_start_date }}
                                    {% endif %}
                                    {% if form.lease_start_date.errors %}
                                        <div class="text-danger small">{{ form.lease_start_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.property_fee_due_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-times me-1"></i>物业费到期时间 <span class="text-danger">*</span>
                                    </label>
                                    {% if form.instance.pk and form.instance.property_fee_due_date %}
                                        <input type="date" class="form-control" name="property_fee_due_date"
                                               id="{{ form.property_fee_due_date.id_for_label }}"
                                               value="{{ form.instance.property_fee_due_date|date:'Y-m-d' }}" required>
                                    {% else %}
                                        {{ form.property_fee_due_date }}
                                    {% endif %}
                                    {% if form.property_fee_due_date.errors %}
                                        <div class="text-danger small">{{ form.property_fee_due_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong>车位物业费固定为每月20元
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'parking:list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 500;
        color: #495057;
    }
    
    .text-danger {
        font-size: 0.875rem;
    }
    
    .alert-info {
        border-left: 4px solid #17a2b8;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 车位号自动转大写
        const parkingNumberInput = document.getElementById('{{ form.parking_number.id_for_label }}');
        if (parkingNumberInput) {
            parkingNumberInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase();
            });
        }

        // 车牌号码自动转大写
        const licensePlateNumberInput = document.getElementById('license_plate_number');
        if (licensePlateNumberInput) {
            licensePlateNumberInput.addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase();
                updateLicensePlatePreview();
            });
        }

        // 省份选择变化时更新预览
        const licensePlateProvinceSelect = document.getElementById('license_plate_province');
        if (licensePlateProvinceSelect) {
            licensePlateProvinceSelect.addEventListener('change', function(e) {
                updateLicensePlatePreview();
            });
        }

        // 更新车牌号预览
        function updateLicensePlatePreview() {
            const province = licensePlateProvinceSelect ? licensePlateProvinceSelect.value : '';
            const number = licensePlateNumberInput ? licensePlateNumberInput.value : '';

            // 可以在这里添加实时预览功能
            if (province && number) {
                const fullLicensePlate = province + number;
                console.log('完整车牌号：', fullLicensePlate);
            }
        }

        // 表单验证
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const parkingNumber = document.getElementById('{{ form.parking_number.id_for_label }}').value.trim();
            const tenantName = document.getElementById('{{ form.tenant_name.id_for_label }}').value.trim();
            const phone = document.getElementById('{{ form.phone.id_for_label }}').value.trim();

            if (!parkingNumber) {
                alert('请输入车位号');
                e.preventDefault();
                return;
            }

            if (!tenantName) {
                alert('请输入租户姓名');
                e.preventDefault();
                return;
            }

            if (!phone) {
                alert('请输入电话号码');
                e.preventDefault();
                return;
            }

            // 检查车牌号
            const province = licensePlateProvinceSelect ? licensePlateProvinceSelect.value : '';
            const number = licensePlateNumberInput ? licensePlateNumberInput.value.trim() : '';

            if ((province && !number) || (!province && number)) {
                alert('请完整填写车牌号（省份和号码都要填写）');
                e.preventDefault();
                return;
            }
        });

        // 初始化预览
        updateLicensePlatePreview();

        // 强制设置日期字段的值（用于编辑模式）
        const leaseStartDateInput = document.getElementById('{{ form.lease_start_date.id_for_label }}');
        const propertyFeeDueDateInput = document.getElementById('{{ form.property_fee_due_date.id_for_label }}');

        // 检查是否有data-date属性，如果有则设置值
        if (leaseStartDateInput && leaseStartDateInput.getAttribute('data-date')) {
            leaseStartDateInput.value = leaseStartDateInput.getAttribute('data-date');
        }

        if (propertyFeeDueDateInput && propertyFeeDueDateInput.getAttribute('data-date')) {
            propertyFeeDueDateInput.value = propertyFeeDueDateInput.getAttribute('data-date');
        }
    });
</script>
{% endblock %}
