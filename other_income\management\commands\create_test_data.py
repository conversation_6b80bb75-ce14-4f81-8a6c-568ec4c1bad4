from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random

from other_income.models import OtherIncomeRecord


class Command(BaseCommand):
    help = '创建其它收入管理的测试数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=50,
            help='要创建的测试记录数量'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        # 清除现有数据
        OtherIncomeRecord.objects.all().delete()
        self.stdout.write('已清除现有数据')
        
        # 费用项目配置
        fee_configs = [
            {'field': 'water_fee', 'name': '水费', 'min': 50, 'max': 500},
            {'field': 'storage_electricity_fee', 'name': '储藏间电费', 'min': 20, 'max': 200},
            {'field': 'water_purifier_fee', 'name': '净水机费', 'min': 30, 'max': 150},
            {'field': 'water_card_fee', 'name': '净水卡费', 'min': 10, 'max': 100},
            {'field': 'water_bucket_fee', 'name': '水桶费', 'min': 5, 'max': 50},
            {'field': 'shop_water_fee', 'name': '商铺水费', 'min': 100, 'max': 800},
            {'field': 'water_card_supplement_fee', 'name': '补自来水卡费', 'min': 15, 'max': 80},
            {'field': 'water_purifier_card_fee', 'name': '补净水机卡费', 'min': 20, 'max': 100},
            {'field': 'parking_fee', 'name': '停车场收费', 'min': 200, 'max': 1000},
            {'field': 'elevator_card_fee', 'name': '电梯梯控卡费', 'min': 25, 'max': 120},
            {'field': 'other_fee', 'name': '其它费用', 'min': 10, 'max': 300},
        ]
        
        # 备注模板
        notes_templates = [
            '{}收费',
            '{}月度收费',
            '{}补缴费用',
            '{}维护费',
            '{}服务费',
            '{}管理费',
            '{}年度费用',
            '{}季度收费',
            '{}临时收费',
            '{}设备费用',
            '{}收费，包含设备维护和日常管理费用',
            '{}月度收费，涵盖基础服务和额外维护项目',
            '{}年度费用缴纳，包括设备更新和系统维护成本',
            '{}临时收费项目，用于应急维修和特殊服务需求',
        ]
        
        created_count = 0
        
        for i in range(count):
            # 随机生成日期（最近3个月内）
            days_ago = random.randint(0, 90)
            income_date = timezone.now().date() - timedelta(days=days_ago)
            
            # 随机选择1-3个费用项目
            selected_fees = random.sample(fee_configs, random.randint(1, 3))
            
            # 创建记录
            record_data = {
                'income_source': 'other',
                'income_date': income_date,
                'notes': '',
            }
            
            total_amount = Decimal('0.00')
            note_parts = []
            
            # 为选中的费用项目生成金额
            for fee_config in selected_fees:
                amount = Decimal(str(random.uniform(fee_config['min'], fee_config['max']))).quantize(Decimal('0.01'))
                record_data[fee_config['field']] = amount
                total_amount += amount
                note_parts.append(fee_config['name'])
            
            # 设置总金额
            record_data['amount'] = total_amount
            
            # 生成备注
            if note_parts:
                template = random.choice(notes_templates)
                record_data['notes'] = template.format('、'.join(note_parts))
            
            # 创建记录
            try:
                OtherIncomeRecord.objects.create(**record_data)
                created_count += 1
                
                if created_count % 10 == 0:
                    self.stdout.write(f'已创建 {created_count} 条记录...')
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'创建记录失败: {e}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'成功创建 {created_count} 条其它收入测试数据')
        )
        
        # 显示统计信息
        from django.db.models import Sum
        total_amount = OtherIncomeRecord.objects.aggregate(
            total=Sum('amount')
        )['total'] or Decimal('0.00')
        
        self.stdout.write(f'总收入金额: ¥{total_amount}')
        
        # 按月统计
        from django.db.models.functions import TruncMonth
        
        monthly_stats = OtherIncomeRecord.objects.annotate(
            month=TruncMonth('income_date')
        ).values('month').annotate(
            total=Sum('amount')
        ).order_by('-month')[:3]
        
        self.stdout.write('\n最近3个月统计:')
        for stat in monthly_stats:
            month_str = stat['month'].strftime('%Y年%m月')
            amount = stat['total']
            self.stdout.write(f'  {month_str}: ¥{amount}')
