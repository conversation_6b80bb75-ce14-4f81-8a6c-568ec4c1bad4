from django.db import models
from django.utils import timezone
from decimal import Decimal


class IncomeSource(models.Model):
    """收入来源模型"""
    name = models.CharField('收入来源', max_length=50, unique=True)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '收入来源'
        verbose_name_plural = '收入来源'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class OtherIncomeRecord(models.Model):
    """物业其它收入记录模型"""
    
    # 收入来源选择
    INCOME_SOURCE_CHOICES = [
        ('water_fee', '水费'),
        ('storage_electricity', '储藏间电费'),
        ('water_purifier_fee', '净水机费'),
        ('water_card', '净水卡'),
        ('water_bucket_fee', '水桶费'),
        ('shop_water_fee', '商铺水费'),
        ('water_card_supplement', '补自来水卡费'),
        ('water_purifier_card', '补净水机卡费'),
        ('parking_fee', '停车场收费'),
        ('elevator_card_fee', '电梯梯控卡费'),
        ('other', '其他'),
    ]
    
    income_source = models.CharField('收入来源', max_length=50, choices=INCOME_SOURCE_CHOICES, default='other')
    amount = models.DecimalField('金额', max_digits=10, decimal_places=2)
    income_date = models.DateField('收入日期', default=timezone.now)
    notes = models.TextField('备注', blank=True, null=True)
    
    # 各项费用明细（可选）
    water_fee = models.DecimalField('水费', max_digits=10, decimal_places=2, blank=True, null=True)
    storage_electricity_fee = models.DecimalField('储藏间电费', max_digits=10, decimal_places=2, blank=True, null=True)
    water_purifier_fee = models.DecimalField('净水机费', max_digits=10, decimal_places=2, blank=True, null=True)
    water_card_fee = models.DecimalField('净水卡费', max_digits=10, decimal_places=2, blank=True, null=True)
    water_bucket_fee = models.DecimalField('水桶费', max_digits=10, decimal_places=2, blank=True, null=True)
    shop_water_fee = models.DecimalField('商铺水费', max_digits=10, decimal_places=2, blank=True, null=True)
    water_card_supplement_fee = models.DecimalField('补自来水卡费', max_digits=10, decimal_places=2, blank=True, null=True)
    water_purifier_card_fee = models.DecimalField('补净水机卡费', max_digits=10, decimal_places=2, blank=True, null=True)
    parking_fee = models.DecimalField('停车场收费', max_digits=10, decimal_places=2, blank=True, null=True)
    elevator_card_fee = models.DecimalField('电梯梯控卡费', max_digits=10, decimal_places=2, blank=True, null=True)
    water_meter_battery_fee = models.DecimalField('自来水表电池费', max_digits=10, decimal_places=2, blank=True, null=True)
    other_fee = models.DecimalField('其它费用', max_digits=10, decimal_places=2, blank=True, null=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '其它收入记录'
        verbose_name_plural = '其它收入记录'
        ordering = ['-income_date', '-created_at']
    
    def __str__(self):
        return f"{self.get_income_source_display()} - {self.amount}元 ({self.income_date})"
    
    def calculate_total_amount(self):
        """计算总金额（各项费用之和）"""
        total = Decimal('0.00')
        fee_fields = [
            'water_fee', 'storage_electricity_fee', 'water_purifier_fee',
            'water_card_fee', 'water_bucket_fee', 'shop_water_fee',
            'water_card_supplement_fee', 'water_purifier_card_fee',
            'parking_fee', 'elevator_card_fee', 'water_meter_battery_fee', 'other_fee'
        ]
        
        for field in fee_fields:
            value = getattr(self, field)
            if value:
                total += value
        
        return total
    
    def save(self, *args, **kwargs):
        """保存时自动计算总金额"""
        # 总是重新计算总金额（基于各项费用明细）
        self.amount = self.calculate_total_amount()
        super().save(*args, **kwargs)
