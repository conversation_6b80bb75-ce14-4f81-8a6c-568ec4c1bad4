{% extends 'base/base.html' %}
{% load static %}

{% block title %}商品房物业费流水统计 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-success"></i>商品房物业费流水统计
                        <span class="badge bg-success ms-2">{{ page_obj.paginator.count }} 条</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card bg-primary text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-number" id="total-amount">
                                    ¥{{ total_amount|floatformat:2 }}
                                </div>
                                <div class="stats-label">总收入金额</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-success text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <div class="stats-number">{{ this_month_count }}</div>
                                <div class="stats-label">本月缴费笔数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-info text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-calendar-month"></i>
                                </div>
                                <div class="stats-number" id="this-month-amount">
                                    ¥{{ this_month_amount|floatformat:2 }}
                                </div>
                                <div class="stats-label">本月缴费金额</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-warning text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="stats-number" id="last-month-amount">
                                    ¥{{ last_month_amount|floatformat:2 }}
                                </div>
                                <div class="stats-label">上月缴费金额</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索和筛选 -->
                    <div class="toolbar">
                        <!-- 导航和操作按钮 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{% url 'commercial_properties:list' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-1"></i>返回商品房物业费管理
                                    </a>
                                    <a href="{% url 'commercial_properties:export_payment_history' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-info">
                                        <i class="fas fa-download me-1"></i>导出流水数据
                                    </a>
                                    <button type="button" class="btn btn-danger" onclick="showBatchDeleteModal()">
                                        <i class="fas fa-trash me-1"></i>批量删除
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- 筛选区域 - 单行布局 -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="unified-filter-section">
                                    <form method="get" class="d-flex align-items-center flex-wrap gap-3">
                                        <!-- 快捷筛选标签 -->
                                        <div class="filter-label">
                                            <i class="fas fa-calendar-alt me-2"></i>快捷筛选：
                                        </div>

                                        <!-- 快捷筛选按钮 -->
                                        <div class="btn-group me-3" role="group">
                                            <button type="button" class="btn btn-outline-primary btn-sm date-filter" data-days="0">
                                                <i class="fas fa-calendar-day me-1"></i>今天
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm date-filter" data-days="1">
                                                <i class="fas fa-calendar-minus me-1"></i>昨天
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm date-filter" data-days="3">
                                                <i class="fas fa-calendar-week me-1"></i>近3天
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm date-filter" data-days="7">
                                                <i class="fas fa-calendar-week me-1"></i>近7天
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm date-filter" data-period="month">
                                                <i class="fas fa-calendar-alt me-1"></i>本月
                                            </button>
                                        </div>

                                        <a href="{% url 'commercial_properties:payment_history' %}" class="btn btn-outline-secondary btn-sm me-3">
                                            <i class="fas fa-list me-1"></i>全部
                                        </a>

                                        <!-- 分隔线 -->
                                        <div class="filter-divider"></div>

                                        <!-- 自定义日期筛选 -->
                                        <div class="custom-date-group d-flex align-items-center gap-2">
                                            <label class="form-label mb-0">
                                                <i class="fas fa-calendar-plus me-1"></i>开始日期
                                            </label>
                                            {{ search_form.start_date }}

                                            <label class="form-label mb-0 ms-2">
                                                <i class="fas fa-calendar-check me-1"></i>结束日期
                                            </label>
                                            {{ search_form.end_date }}

                                            <button type="submit" class="btn btn-success btn-sm ms-2">
                                                <i class="fas fa-filter me-1"></i>筛选
                                            </button>
                                            <a href="{% url 'commercial_properties:payment_history' %}" class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-redo me-1"></i>重置
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>缴费时间</th>
                                        <th>业主姓名</th>
                                        <th>房号</th>
                                        <th>缴费金额</th>
                                        <th>费用期间</th>
                                        <th>缴费方式</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input">
                                        </th>
                                        <th>缴费时间</th>
                                        <th>业主姓名</th>
                                        <th>房号</th>
                                        <th>缴费金额</th>
                                        <th>费用期间</th>
                                        <th>缴费方式</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="payment_ids" value="{{ payment.id }}" class="form-check-input payment-checkbox">
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ payment.payment_date|date:"Y-m-d H:i" }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator status-active"></div>
                                            {{ payment.property.owner_name }}
                                        </div>
                                    </td>
                                    <td>{{ payment.property.building_number }}{% if payment.property.unit_number %}-{{ payment.property.unit_number }}{% endif %}-{{ payment.property.room_number }}</td>
                                    <td class="text-success fw-bold">¥{{ payment.amount }}</td>
                                    <td>
                                        <small class="text-muted">
                                            {{ payment.fee_start_date }} 至 {{ payment.fee_end_date }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ payment.payment_method }}</span>
                                    </td>
                                    <td>
                                        {% if payment.notes %}
                                            <span class="text-muted">{{ payment.notes|truncatechars:30 }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无缴费记录</p>
                                        <a href="{% url 'commercial_properties:list' %}" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>查看商品房列表
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                 
 
                    
                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量删除确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="selectedCount">0</span> 条缴费记录吗？</p>
                <p class="text-danger">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="batchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-radius: 15px;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .stats-icon {
        position: absolute;
        right: 1rem;
        top: 1rem;
        font-size: 2rem;
        opacity: 0.3;
    }

    .stats-number {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-active {
        background-color: #28a745;
    }

    /* 统一筛选区域样式 */
    .unified-filter-section {
        padding: 1.25rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 1px solid #dee2e6;
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
    }

    .filter-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.95rem;
        white-space: nowrap;
        min-width: fit-content;
    }

    .filter-divider {
        width: 2px;
        height: 30px;
        background: linear-gradient(to bottom, #dee2e6, #adb5bd, #dee2e6);
        border-radius: 1px;
        margin: 0 0.5rem;
    }

    .custom-date-group {
        background: rgba(255, 255, 255, 0.8);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        border: 1px solid rgba(222, 226, 230, 0.8);
        backdrop-filter: blur(5px);
    }

    .custom-date-group .form-control {
        min-width: 150px;
        width: auto;
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        box-sizing: border-box;
        -webkit-appearance: none;
        -moz-appearance: textfield;
        appearance: none;
    }

    /* 确保日期输入框在不同浏览器中的一致性 */
    .custom-date-group input[type="date"] {
        min-width: 150px;
        width: auto;
        font-family: inherit;
        line-height: 1.5;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        background-color: #fff;
        background-clip: padding-box;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .custom-date-group input[type="date"]:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    /* 按钮组美化 */
    .btn-group .btn {
        border-radius: 0;
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        transition: all 0.2s ease;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
    }

    .btn-group .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 日期筛选按钮特殊样式 */
    .date-filter {
        position: relative;
        overflow: hidden;
    }

    .date-filter:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .date-filter:hover:before {
        left: 100%;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .unified-filter-section form {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .filter-divider {
            width: 100%;
            height: 2px;
            margin: 0;
        }

        .custom-date-group {
            width: 100%;
            justify-content: flex-start;
        }
    }

    @media (max-width: 768px) {
        .unified-filter-section {
            padding: 1rem;
        }

        .btn-group {
            flex-wrap: wrap;
            width: 100%;
        }

        .btn-group .btn {
            flex: 1;
            min-width: 70px;
            margin-bottom: 0.25rem;
        }

        .custom-date-group {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
            width: 100%;
        }

        .custom-date-group .form-control {
            width: 100%;
        }
    }

    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 900px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 900px;
    }

    #body-table thead {
        margin: 0 !important;
        padding: 0 !important;
        height: 0 !important;
        line-height: 0 !important;
    }

    #body-table thead th {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        line-height: 0 !important;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        font-size: 0.9rem;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    .table-body-scroll tr:hover {
        background-color: #f8f9fa;
    }

    .table-body-scroll tr:last-child td {
        border-bottom: none;
    }

    /* 列宽设置 - 商品房缴费流水表格 */
    .table-header-fixed th:nth-child(1),
    .table-body-scroll td:nth-child(1) { width: 60px; }
    .table-header-fixed th:nth-child(2),
    .table-body-scroll td:nth-child(2) { width: 150px; }
    .table-header-fixed th:nth-child(3),
    .table-body-scroll td:nth-child(3) { width: 100px; }
    .table-header-fixed th:nth-child(4),
    .table-body-scroll td:nth-child(4) { width: 120px; }
    .table-header-fixed th:nth-child(5),
    .table-body-scroll td:nth-child(5) { width: 100px; }
    .table-header-fixed th:nth-child(6),
    .table-body-scroll td:nth-child(6) { width: 200px; }
    .table-header-fixed th:nth-child(7),
    .table-body-scroll td:nth-child(7) { width: 100px; }
    .table-header-fixed th:nth-child(8),
    .table-body-scroll td:nth-child(8) { width: 150px; }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 响应式设计 - 分页 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头表体列宽
        syncTableColumns();

        // 同步表头表体列宽函数
        function syncTableColumns() {
            const headerTable = document.getElementById('header-table');
            const bodyTable = document.getElementById('body-table');

            if (!headerTable || !bodyTable) {
                console.log('表头或表体表格未找到');
                return;
            }

            const headerCells = headerTable.querySelectorAll('th');
            const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

            // 定义列宽 - 商品房缴费流水表格的列宽配置
            const columnWidths = [
                '60px',   // 复选框
                '150px',  // 缴费时间
                '100px',  // 业主姓名
                '120px',  // 房号
                '100px',  // 缴费金额
                '200px',  // 费用期间
                '100px',  // 缴费方式
                '150px'   // 备注
            ];

            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 设置表体隐藏表头列宽（用于对齐）
            bodyHeaderCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 确保两个表格宽度一致
            headerTable.style.width = '100%';
            bodyTable.style.width = '100%';
            headerTable.style.minWidth = '900px';
            bodyTable.style.minWidth = '900px';

            console.log('✅ 商品房缴费流水表头表体列宽同步完成');
        }

        // 窗口大小改变时重新同步列宽
        window.addEventListener('resize', function() {
            setTimeout(syncTableColumns, 50);
        });

        // 快捷日期筛选
        const dateFilterButtons = document.querySelectorAll('.date-filter');
        dateFilterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const days = this.getAttribute('data-days');
                const period = this.getAttribute('data-period');

                let url = new URL(window.location.href);
                url.searchParams.delete('start_date');
                url.searchParams.delete('end_date');

                // 获取当前日期，使用本地时间
                const today = new Date();
                const year = today.getFullYear();
                const month = today.getMonth();
                const date = today.getDate();

                // 格式化日期为 YYYY-MM-DD
                function formatDate(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }

                if (period === 'month') {
                    // 本月：从本月1号到本月最后一天
                    const firstDay = new Date(year, month, 1);
                    const lastDay = new Date(year, month + 1, 0);
                    url.searchParams.set('start_date', formatDate(firstDay));
                    url.searchParams.set('end_date', formatDate(lastDay));
                } else if (days) {
                    if (days === '0') {
                        // 今天
                        const todayStr = formatDate(today);
                        url.searchParams.set('start_date', todayStr);
                        url.searchParams.set('end_date', todayStr);
                    } else if (days === '1') {
                        // 昨天
                        const yesterday = new Date(year, month, date - 1);
                        const yesterdayStr = formatDate(yesterday);
                        url.searchParams.set('start_date', yesterdayStr);
                        url.searchParams.set('end_date', yesterdayStr);
                    } else {
                        // 近N天：从N天前到今天
                        const startDate = new Date(year, month, date - parseInt(days) + 1);
                        url.searchParams.set('start_date', formatDate(startDate));
                        url.searchParams.set('end_date', formatDate(today));
                    }
                }

                window.location.href = url.toString();
            });
        });

        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('selectAll');
        const paymentCheckboxes = document.querySelectorAll('.payment-checkbox');

        selectAllCheckbox.addEventListener('change', function() {
            paymentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });

        paymentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.payment-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = selectedCount;

            // 更新全选状态
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < paymentCheckboxes.length;
            selectAllCheckbox.checked = selectedCount === paymentCheckboxes.length;
        }
    });

    function showBatchDeleteModal() {
        const selectedCount = document.querySelectorAll('.payment-checkbox:checked').length;
        if (selectedCount === 0) {
            alert('请先选择要删除的记录');
            return;
        }

        document.getElementById('selectedCount').textContent = selectedCount;
        const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
        modal.show();
    }

    function batchDelete() {
        const selectedIds = Array.from(document.querySelectorAll('.payment-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedIds.length === 0) {
            alert('请先选择要删除的记录');
            return;
        }

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "commercial_properties:batch_delete_payment" %}';

        // 添加CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // 添加选中的ID
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'payment_ids';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }

    document.addEventListener('DOMContentLoaded', function() {
        // 月度收入图表
        const ctx = document.getElementById('monthlyChart');
        if (ctx) {
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '月度收入',
                        data: [12000, 15000, 13000, 18000, 16000, 20000],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '商品房物业费收入趋势'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>
{% endblock %}
