{% extends 'base/base.html' %}
{% load static %}

{% block title %}批量导入客户{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-upload text-success me-2"></i>
            批量导入客户
        </h2>
        <div>
            <a href="{% url 'water_machine:village_customer_import_template' %}" class="btn btn-outline-info me-2">
                <i class="fas fa-download me-1"></i>下载模板
            </a>
            <a href="{% url 'water_machine:village_customer_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>
    </div>

    <!-- 导入说明 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        导入说明
                    </h6>
                </div>
                <div class="card-body">
                    <h6>文件要求：</h6>
                    <ul class="mb-3">
                        <li>支持 <strong>.xlsx</strong> 和 <strong>.xls</strong> 格式</li>
                        <li>文件大小不超过 <strong>5MB</strong></li>
                        <li>建议先下载模板，按照模板格式填写数据</li>
                    </ul>
                    
                    <h6>必填字段：</h6>
                    <ul class="mb-3">
                        <li><strong>姓名</strong>：客户姓名（必填）</li>
                        <li><strong>住址</strong>：客户住址（不能重复）</li>
                        <li><strong>电话</strong>：11位手机号码（必填，以1开头）</li>
                        <li><strong>水桶数量</strong>：默认为1</li>
                        <li><strong>正常卡号</strong>：水卡号码（不能重复）</li>
                        <li><strong>使用人数</strong>：默认为1</li>
                    </ul>
                    
                    <h6>可选字段：</h6>
                    <ul class="mb-0">
                        <li><strong>丢失卡号</strong>：多个卡号用逗号分隔</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        注意事项
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>导入前请备份现有数据</li>
                        <li>住址不能与现有客户重复</li>
                        <li>正常卡号不能与现有客户重复</li>
                        <li>电话号码必须是11位数字，以1开头</li>
                        <li>导入失败的记录会显示具体错误信息</li>
                        <li>建议分批导入，每次不超过1000条</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入表单 -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="{{ form.file.id_for_label }}" class="form-label">
                                <i class="fas fa-file-excel text-success me-2"></i>
                                选择Excel文件 <span class="text-danger">*</span>
                            </label>
                            {{ form.file }}
                            {% if form.file.errors %}
                                <div class="text-danger mt-1">{{ form.file.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">{{ form.file.help_text }}</div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'water_machine:village_customer_list' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i>开始导入
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header h6 {
        margin-bottom: 0;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>
{% endblock %}
