/* 全局样式 */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f6fa;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    transform: scale(1.02);
}

.navbar-brand i {
    margin-right: 8px;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.navbar-nav .nav-link i {
    margin-right: 6px;
    font-size: 0.9rem;
}

/* 下拉菜单美化样式 */
.navbar-nav .dropdown-menu {
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    padding: 1rem 0;
    margin-top: 0.8rem;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    min-width: 240px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.navbar-nav .dropdown-menu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dropdown-item {
    padding: 0.8rem 1.5rem;
    font-weight: 500;
    color: #2c3e50;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    margin: 0.2rem 0.8rem;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    border: 1px solid transparent;
}

.dropdown-item i {
    margin-right: 12px;
    font-size: 1rem;
    width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    color: #667eea;
    transform: translateX(8px) translateY(-1px);
    border-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
}

.dropdown-item:hover i {
    color: #667eea;
    transform: scale(1.1);
}

.dropdown-item:active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
    color: #667eea;
    transform: translateX(6px) translateY(0);
}

.dropdown-divider {
    height: 1px;
    margin: 0.8rem 1rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    border: none;
    opacity: 0.6;
}

/* 下拉菜单动画 */
@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 下拉菜单项进入动画 */
.dropdown-item {
    animation: dropdownItemFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateX(-10px);
}

.dropdown-item:nth-child(1) { animation-delay: 0.05s; }
.dropdown-item:nth-child(2) { animation-delay: 0.1s; }
.dropdown-item:nth-child(3) { animation-delay: 0.15s; }
.dropdown-item:nth-child(4) { animation-delay: 0.2s; }
.dropdown-item:nth-child(5) { animation-delay: 0.25s; }

@keyframes dropdownItemFadeIn {
    0% {
        opacity: 0;
        transform: translateX(-10px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 用户下拉菜单特殊样式 */
.navbar-nav .nav-item:last-child .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 200px;
}

.navbar-nav .nav-item:last-child .dropdown-item {
    justify-content: flex-start;
}

/* 响应式下拉菜单 */
@media (max-width: 991.98px) {
    .navbar-nav .dropdown-menu {
        position: static !important;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: rgba(255, 255, 255, 0.95);
        border: none;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        border-radius: 0;
        animation: none;
    }

    .dropdown-item {
        margin: 0;
        border-radius: 0;
        padding-left: 2rem;
    }

    .dropdown-item:hover {
        transform: none;
        background-color: rgba(102, 126, 234, 0.1);
    }
}

/* 主内容区域样式 */
.main-content {
    padding-top: 80px; /* 为固定导航栏留出空间 */
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-bottom: 2rem;
    min-height: 100vh;
}

/* 移动端导航栏适配 */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 10px;
        margin-top: 1rem;
        padding: 1rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .navbar-nav .nav-link {
        color: #4e73df !important;
        padding: 0.75rem 1rem !important;
        margin: 0.2rem 0;
        border-radius: 8px;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df !important;
        transform: none;
    }

    .main-content {
        padding-top: 70px; /* 移动端稍小的顶部间距 */
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 表格样式 */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* 租客姓名和房东姓名单元格支持自动换行 */
.table td.name-cell {
    word-wrap: break-word !important;
    word-break: break-all !important;
    white-space: normal !important;
    line-height: 1.4;
    overflow: visible !important;
    text-overflow: unset !important;
    max-width: none !important;
    min-height: auto !important;
}

/* 表单样式 */
.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* 分页样式 */
.pagination {
    justify-content: center;
}

.page-link {
    border: none;
    color: var(--primary-color);
    border-radius: var(--border-radius);
    margin: 0 2px;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

/* 徽章样式 */
.badge {
    border-radius: 20px;
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* 工具栏样式 */
.toolbar {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.toolbar .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 5;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-active {
    background-color: var(--success-color);
}

.status-overdue {
    background-color: var(--danger-color);
}

.status-warning {
    background-color: var(--warning-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .toolbar {
        text-align: center;
    }
    
    .toolbar .btn {
        margin: 0.25rem;
        width: 100%;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 数据统计卡片 */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

.stats-card .stats-icon {
    font-size: 3rem;
    opacity: 0.3;
    position: absolute;
    right: 1rem;
    top: 1rem;
}

/* 固定表头样式 */
.table-fixed-header {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.table-fixed-header table {
    margin-bottom: 0;
}

.table-fixed-header thead th {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: 2px solid #dee2e6;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-fixed-header thead th:first-child {
    border-top-left-radius: 0.375rem;
}

.table-fixed-header thead th:last-child {
    border-top-right-radius: 0.375rem;
}

/* 移动端固定表头适配 */
@media (max-width: 768px) {
    .table-fixed-header {
        max-height: 400px;
    }
}
