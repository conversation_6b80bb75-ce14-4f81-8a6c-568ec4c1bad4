@echo off
chcp 65001 >nul
title 静态文件优化工具

echo.
echo ========================================
echo    静态文件优化工具
echo ========================================
echo.
echo [功能] 优化CSS/JS文件，提高局域网访问速度
echo [包含] 文件压缩、缓存优化、Gzip压缩
echo.

:: 切换到项目目录
cd /d "E:\hh\dywy"

:: 检查项目文件
if not exist "manage.py" (
    echo [错误] 未找到 manage.py 文件
    echo 请确认当前目录是项目根目录
    pause
    exit /b 1
)

:: 激活虚拟环境
echo [信息] 激活Python虚拟环境...
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo [成功] 虚拟环境已激活
) else (
    echo [错误] 未找到虚拟环境
    pause
    exit /b 1
)

echo.
echo [1/4] 清理旧的静态文件...
if exist "staticfiles" (
    rmdir /s /q "staticfiles" 2>nul
    echo ✓ 旧静态文件已清理
) else (
    echo ✓ 无需清理
)

echo.
echo [2/4] 运行静态文件优化脚本...
python optimize_static_files.py

echo.
echo [3/4] 检查静态文件目录...
if exist "staticfiles" (
    echo ✓ 静态文件目录已创建
    
    :: 统计文件数量
    for /f %%i in ('dir /s /b "staticfiles\*.*" 2^>nul ^| find /c /v ""') do set file_count=%%i
    echo [信息] 静态文件数量: !file_count!
    
    :: 统计目录大小
    for /f "tokens=3" %%i in ('dir /s "staticfiles" 2^>nul ^| find "个文件"') do set total_size=%%i
    echo [信息] 静态文件大小: !total_size! 字节
) else (
    echo ✗ 静态文件目录创建失败
)

echo.
echo [4/4] 验证优化效果...

:: 检查是否有压缩文件
set gzip_count=0
for /f %%i in ('dir /s /b "staticfiles\*.gz" 2^>nul ^| find /c /v ""') do set gzip_count=%%i
if !gzip_count! gtr 0 (
    echo ✓ 找到 !gzip_count! 个Gzip压缩文件
) else (
    echo ⚠ 未找到Gzip压缩文件
)

:: 检查是否有压缩的CSS/JS文件
set min_count=0
for /f %%i in ('dir /s /b "staticfiles\*.min.*" 2^>nul ^| find /c /v ""') do set min_count=%%i
if !min_count! gtr 0 (
    echo ✓ 找到 !min_count! 个压缩的CSS/JS文件
) else (
    echo ⚠ 未找到压缩的CSS/JS文件
)

echo.
echo ========================================
echo    优化建议
echo ========================================
echo.
echo [浏览器优化]
echo 1. 清除浏览器缓存 (Ctrl+Shift+Delete)
echo 2. 禁用浏览器扩展进行测试
echo 3. 使用开发者工具查看网络请求时间
echo.
echo [服务器优化]
echo 1. 重启Django服务器应用新的静态文件
echo 2. 确保防火墙规则正确配置
echo 3. 使用有线网络连接而不是WiFi
echo.
echo [网络优化]
echo 1. 运行 optimize_lan_connection.bat
echo 2. 使用IP地址访问而不是域名
echo 3. 检查网络设备性能
echo.

echo ========================================
echo    测试静态文件加载
echo ========================================
echo.

:: 启动服务器进行测试
set /p start_server="是否现在启动优化后的服务器进行测试? (y/N): "
if /i "%start_server%"=="y" (
    echo.
    echo [信息] 启动优化版Waitress服务器...
    echo [提示] 服务器启动后，请在浏览器中测试页面加载速度
    echo [提示] 按 Ctrl+C 停止服务器
    echo.
    python run_waitress.py
) else (
    echo.
    echo [信息] 优化完成！
    echo [下一步] 手动启动服务器: python run_waitress.py
    echo [测试] 在浏览器中访问系统并观察加载速度
)

echo.
echo ========================================
echo    性能测试命令
echo ========================================
echo.
echo 使用以下命令测试静态文件加载速度:
echo.

:: 获取本机IP
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    set "LOCAL_IP=%%a"
    set "LOCAL_IP=!LOCAL_IP: =!"
    if not "!LOCAL_IP!"=="" goto :show_test_commands
)
set "LOCAL_IP=127.0.0.1"

:show_test_commands
echo 1. 测试主页加载:
echo    curl -w "总时间: %%{time_total}s\n" http://!LOCAL_IP!:8000/
echo.
echo 2. 测试CSS文件加载:
echo    curl -w "总时间: %%{time_total}s\n" http://!LOCAL_IP!:8000/static/css/style.css
echo.
echo 3. 测试JS文件加载:
echo    curl -w "总时间: %%{time_total}s\n" http://!LOCAL_IP!:8000/static/js/main.js
echo.
echo 4. 浏览器开发者工具:
echo    按F12 → Network标签 → 刷新页面 → 查看加载时间
echo.

echo ========================================
echo.
echo [完成] 静态文件优化完成！
echo [建议] 重启服务器并清除浏览器缓存后测试
echo.
pause
