{% extends 'base/base.html' %}
{% load static %}

{% block title %}新增充值记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-money-bill-wave text-success me-2"></i>
            新增充值记录
        </h2>
        <div>
            <a href="{% url 'water_machine:village_recharge_list' %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>返回充值记录
            </a>
            <a href="{% url 'water_machine:village_customer_list' %}" class="btn btn-outline-info">
                <i class="fas fa-users me-1"></i>客户列表
            </a>
        </div>
    </div>

    <!-- 表单 -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="post" id="rechargeForm">
                        {% csrf_token %}

                        <!-- 显示表单错误 -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="{{ form.customer.id_for_label }}" class="form-label">选择客户 <span class="text-danger">*</span></label>
                            {{ form.customer }}
                            {% if form.customer.errors %}
                                <div class="text-danger">{{ form.customer.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- 客户充值状态显示 -->
                        <div class="mb-3" id="customerStatusDiv" style="display: none;">
                            <label class="form-label">客户当前季度充值状态</label>
                            <div id="customerQuarterStatus" class="d-flex gap-1 flex-wrap">
                                <!-- 动态生成季度状态 -->
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.people_count.id_for_label }}" class="form-label">使用人数 <span class="text-danger">*</span></label>
                                {{ form.people_count }}
                                {% if form.people_count.errors %}
                                    <div class="text-danger">{{ form.people_count.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">充值金额（季度）</label>
                                <div class="form-control bg-light" id="calculatedAmount">
                                    <strong class="text-success">¥0.00</strong>
                                </div>
                                <small class="text-muted">季度金额 = 使用人数 × 15元 × 3个月</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.quarter.id_for_label }}" class="form-label">充值季度 <span class="text-danger">*</span></label>
                            {{ form.quarter }}
                            {% if form.quarter.errors %}
                                <div class="text-danger">{{ form.quarter.errors.0 }}</div>
                            {% endif %}
                            <!-- 无可充值季度提示 -->
                            <div id="noQuarterTip" class="alert alert-warning mt-2" style="display: none;">
                                <i class="fas fa-info-circle me-2"></i>
                                该客户当年所有季度都已充值完成，无需再次充值。
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">备注</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'water_machine:village_recharge_list' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="fas fa-save me-1"></i>创建充值记录
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const peopleCountInput = document.querySelector('input[name="people_count"]');
    const calculatedAmountDiv = document.getElementById('calculatedAmount');
    
    function calculateAmount() {
        const peopleCount = parseInt(peopleCountInput.value) || 0;
        const amount = peopleCount * 15 * 3; // 乘以3个月（一个季度）
        calculatedAmountDiv.innerHTML = `<strong class="text-success">¥${amount.toFixed(2)}</strong>`;
    }
    
    // 初始计算
    calculateAmount();
    
    // 监听人数变化
    peopleCountInput.addEventListener('input', calculateAmount);
    
    // 处理URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const customerId = urlParams.get('customer');
    const quarter = urlParams.get('quarter');
    const peopleCount = urlParams.get('people_count');
    const notes = urlParams.get('notes');

    // 自动选择客户
    if (customerId) {
        const customerSelect = document.querySelector('select[name="customer"]');
        if (customerSelect) {
            customerSelect.value = customerId;
        }
    }

    // 自动填入人数
    if (peopleCount) {
        peopleCountInput.value = peopleCount;
        calculateAmount();
    }

    // 自动选择季度
    if (quarter) {
        const quarterSelect = document.querySelector('select[name="quarter"]');
        if (quarterSelect) {
            quarterSelect.value = quarter;
        }
    }

    // 更新客户充值状态显示
    function updateCustomerStatus(data) {
        const statusDiv = document.getElementById('customerStatusDiv');
        const statusContainer = document.getElementById('customerQuarterStatus');

        if (data && data.customer_name) {
            const currentYear = new Date().getFullYear();
            statusContainer.innerHTML = '';

            for (let q = 1; q <= 4; q++) {
                const quarterCode = `${currentYear}Q${q}`;
                const isPaid = data.paid_quarters.includes(quarterCode);

                const badge = document.createElement('span');
                badge.className = `badge ${isPaid ? 'bg-success' : 'bg-light text-dark'} me-1 mb-1`;
                badge.style.minWidth = '50px';
                badge.title = `Q${q}${isPaid ? '已充值' : '未充值'}`;
                badge.innerHTML = `Q${q} ${isPaid ? '<i class="fas fa-check ms-1"></i>' : '<i class="fas fa-times ms-1"></i>'}`;

                statusContainer.appendChild(badge);
            }

            statusDiv.style.display = 'block';
        } else {
            statusDiv.style.display = 'none';
        }
    }

    // 监听客户选择变化，动态更新可用季度
    const customerSelect = document.querySelector('select[name="customer"]');
    const quarterSelect = document.querySelector('select[name="quarter"]');

    if (customerSelect && quarterSelect) {
        customerSelect.addEventListener('change', function() {
            const customerId = this.value;
            if (customerId) {
                // 获取客户已充值的季度
                fetch(`/water-machine/api/customer-quarters/${customerId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // 显示客户充值状态
                        updateCustomerStatus(data);

                        // 重新生成季度选项，排除已充值的季度
                        const currentYear = new Date().getFullYear();
                        quarterSelect.innerHTML = '<option value="">请选择充值季度</option>';

                        let availableQuarters = 0;
                        for (let q = 1; q <= 4; q++) {
                            const quarterCode = `${currentYear}Q${q}`;
                            const quarterName = `${currentYear}年第${q}季度`;

                            // 只显示未充值的季度
                            if (!data.paid_quarters.includes(quarterCode)) {
                                const option = document.createElement('option');
                                option.value = quarterCode;
                                option.textContent = quarterName;
                                quarterSelect.appendChild(option);
                                availableQuarters++;
                            }
                        }

                        // 显示或隐藏无可充值季度提示
                        const noQuarterTip = document.getElementById('noQuarterTip');
                        const submitBtn = document.getElementById('submitBtn');
                        if (availableQuarters === 0) {
                            noQuarterTip.style.display = 'block';
                            quarterSelect.disabled = true;
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>该客户已完成全年充值';
                        } else {
                            noQuarterTip.style.display = 'none';
                            quarterSelect.disabled = false;
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>创建充值记录';
                        }

                        // 如果URL中有预选季度，重新设置
                        if (quarter && !data.paid_quarters.includes(quarter)) {
                            quarterSelect.value = quarter;
                        }
                    })
                    .catch(error => {
                        console.error('获取客户季度信息失败:', error);
                    });
            } else {
                // 隐藏客户状态显示
                document.getElementById('customerStatusDiv').style.display = 'none';

                // 隐藏无季度提示
                document.getElementById('noQuarterTip').style.display = 'none';

                // 重置提交按钮
                const submitBtn = document.getElementById('submitBtn');
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>创建充值记录';

                // 重置为所有季度
                const currentYear = new Date().getFullYear();
                quarterSelect.innerHTML = '<option value="">请选择充值季度</option>';
                quarterSelect.disabled = false;
                for (let q = 1; q <= 4; q++) {
                    const quarterCode = `${currentYear}Q${q}`;
                    const quarterName = `${currentYear}年第${q}季度`;
                    const option = document.createElement('option');
                    option.value = quarterCode;
                    option.textContent = quarterName;
                    quarterSelect.appendChild(option);
                }
            }
        });
    }

    // 自动填入备注
    if (notes) {
        const notesTextarea = document.querySelector('textarea[name="notes"]');
        if (notesTextarea) {
            notesTextarea.value = decodeURIComponent(notes);
        }
    }

    // 由于已充值的季度不会显示在下拉框中，所以不需要额外的表单验证
    // 服务端的表单验证仍然保留作为最后的安全保障
});
</script>
{% endblock %}
