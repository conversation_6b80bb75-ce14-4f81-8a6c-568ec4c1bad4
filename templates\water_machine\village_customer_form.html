{% extends 'base/base.html' %}
{% load static %}

{% block title %}
    {% if object %}编辑客户{% else %}新增客户{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-user text-primary me-2"></i>
            {% if object %}编辑客户{% else %}新增客户{% endif %}
            {% if object and not object.normal_card_number %}
                <span class="badge bg-warning text-dark ms-2">
                    <i class="fas fa-exclamation-triangle me-1"></i>需要输入新卡号
                </span>
            {% endif %}
        </h2>
        <a href="{% url 'water_machine:village_customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>

    <!-- 挂失提示 -->
    {% if object and not object.normal_card_number %}
        <div class="alert alert-warning" role="alert">
            <h6 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                水卡挂失处理
            </h6>
            <p class="mb-2">该客户的水卡已被标记为丢失，请为客户输入新的水卡号。</p>
            <hr>
            <p class="mb-0">
                <small>
                    <strong>注意：</strong>新卡号不能与其他客户的卡号重复。
                </small>
            </p>
        </div>
    {% endif %}

    <!-- 表单 -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">姓名 <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">电话 <span class="text-danger">*</span></label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">住址 <span class="text-danger">*</span></label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="text-danger">{{ form.address.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="{{ form.water_bucket.id_for_label }}" class="form-label">水桶数量 <span class="text-danger">*</span></label>
                                {{ form.water_bucket }}
                                {% if form.water_bucket.errors %}
                                    <div class="text-danger">{{ form.water_bucket.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label for="{{ form.people_count.id_for_label }}" class="form-label">使用人数 <span class="text-danger">*</span></label>
                                {{ form.people_count }}
                                {% if form.people_count.errors %}
                                    <div class="text-danger">{{ form.people_count.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">固定金额（季度）</label>
                                <div class="form-control bg-light" id="calculatedAmount">
                                    <strong class="text-success">¥0.00</strong>
                                </div>
                                <small class="text-muted">季度金额 = 使用人数 × 15元 × 3个月</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.normal_card_number.id_for_label }}" class="form-label">
                                    正常卡号
                                    {% if not object.normal_card_number %}
                                        <span class="text-danger">*</span>
                                    {% endif %}
                                </label>
                                {{ form.normal_card_number }}
                                {% if form.normal_card_number.errors %}
                                    <div class="text-danger">{{ form.normal_card_number.errors.0 }}</div>
                                {% endif %}
                                {% if not object.normal_card_number %}
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        请输入新的水卡号
                                    </small>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.lost_card_numbers.id_for_label }}" class="form-label">丢失卡号</label>
                                {{ form.lost_card_numbers }}
                                {% if form.lost_card_numbers.errors %}
                                    <div class="text-danger">{{ form.lost_card_numbers.errors.0 }}</div>
                                {% endif %}
                                <small class="text-muted">多个卡号用逗号分隔</small>
                            </div>
                        </div>

                        {% if object and object.normal_card_number %}
                        <div class="mb-3">
                            <label class="form-label">当前水卡号</label>
                            <div class="form-control bg-light">
                                <strong class="text-primary">{{ object.normal_card_number }}</strong>
                            </div>
                        </div>
                        {% endif %}

                        {% if object and object.get_lost_card_list %}
                        <div class="mb-3">
                            <label class="form-label">丢失卡号历史</label>
                            <div class="form-control bg-light">
                                <span class="text-muted">{{ object.get_lost_card_display }}</span>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 充值功能区域 -->
                        <div class="card bg-light mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-money-bill-wave text-success me-2"></i>
                                    充值功能
                                </h6>
                            </div>
                            <div class="card-body">
                                {% if object %}
                                <!-- 显示当前季度充值状态 -->
                                <div class="mb-3">
                                    <label class="form-label">当年季度充值状态</label>
                                    <div>
                                        {% with quarter_status=object.get_quarter_status %}
                                            {% for quarter, is_paid in quarter_status.items %}
                                                <span class="badge {% if is_paid %}bg-success{% else %}bg-light text-dark{% endif %} me-1 mb-1"
                                                      style="min-width: 50px;">
                                                    {{ quarter }}
                                                    {% if is_paid %}
                                                        <i class="fas fa-check ms-1"></i>
                                                    {% else %}
                                                        <i class="fas fa-times ms-1"></i>
                                                    {% endif %}
                                                </span>
                                            {% endfor %}
                                        {% endwith %}
                                    </div>
                                </div>
                                {% endif %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">充值季度</label>
                                        <select class="form-select" id="rechargeQuarter">
                                            <option value="">选择充值季度</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">备注</label>
                                        <input type="text" class="form-control" id="rechargeNotes" placeholder="充值备注（可选）">
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-success" id="addRechargeBtn" {% if not object %}disabled{% endif %}>
                                        <i class="fas fa-plus me-1"></i>
                                        {% if object %}添加充值记录{% else %}保存客户后可充值{% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'water_machine:village_customer_list' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if object %}更新{% else %}创建{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const peopleCountInput = document.querySelector('input[name="people_count"]');
    const calculatedAmountDiv = document.getElementById('calculatedAmount');
    const rechargeQuarterSelect = document.getElementById('rechargeQuarter');
    const addRechargeBtn = document.getElementById('addRechargeBtn');
    const rechargeNotesInput = document.getElementById('rechargeNotes');

    function calculateAmount() {
        const peopleCount = parseInt(peopleCountInput.value) || 0;
        const amount = peopleCount * 15 * 3; // 乘以3个月
        calculatedAmountDiv.innerHTML = `<strong class="text-success">¥${amount.toFixed(2)}</strong>`;
    }

    // 生成季度选项
    function generateQuarterOptions() {
        const currentYear = new Date().getFullYear();
        const quarters = [];

        // 只生成当前年份的四个季度
        for (let quarter = 1; quarter <= 4; quarter++) {
            const quarterCode = `${currentYear}Q${quarter}`;
            const quarterName = `${currentYear}年第${quarter}季度`;
            quarters.push({code: quarterCode, name: quarterName});
        }

        // 清空并添加选项
        rechargeQuarterSelect.innerHTML = '<option value="">选择充值季度</option>';
        quarters.forEach(quarter => {
            const option = document.createElement('option');
            option.value = quarter.code;
            option.textContent = quarter.name;
            rechargeQuarterSelect.appendChild(option);
        });
    }

    // 添加充值记录
    function addRechargeRecord() {
        const customerId = {% if object %}{{ object.pk }}{% else %}null{% endif %};
        const quarter = rechargeQuarterSelect.value;
        const peopleCount = parseInt(peopleCountInput.value) || 0;
        const notes = rechargeNotesInput.value;

        if (!customerId) {
            alert('请先保存客户信息');
            return;
        }

        if (!quarter) {
            alert('请选择充值季度');
            return;
        }

        if (peopleCount <= 0) {
            alert('请输入正确的使用人数');
            return;
        }

        // 检查该季度是否已经充值
        {% if object %}
        const quarterStatus = JSON.parse('{{ object.get_quarter_status|escapejs }}');
        const quarterKey = quarter.substring(quarter.length - 2); // 获取Q1, Q2等
        if (quarterStatus[quarterKey]) {
            alert(`${quarter} 季度已经充值过了，不能重复充值！`);
            return;
        }
        {% endif %}

        // 构建充值URL
        const rechargeUrl = `/water-machine/village/recharge/create/?customer=${customerId}&quarter=${quarter}&people_count=${peopleCount}&notes=${encodeURIComponent(notes)}`;
        window.location.href = rechargeUrl;
    }

    // 初始化
    calculateAmount();
    generateQuarterOptions();

    // 监听人数变化
    peopleCountInput.addEventListener('input', calculateAmount);
    peopleCountInput.addEventListener('change', calculateAmount);

    // 监听充值按钮点击
    addRechargeBtn.addEventListener('click', addRechargeRecord);
});
</script>
{% endblock %}
