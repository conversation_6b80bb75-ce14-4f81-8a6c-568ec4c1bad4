# 设置工作目录
Set-Location "E:\dy"

# 创建日志目录
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
} else {
    # 如果logs文件夹存在且有内容，先清除所有文件
    if ((Get-ChildItem "logs" -File).Count -gt 0) {
        Write-Host "Clearing existing log files..."
        Remove-Item "logs\*" -Force
        Write-Host "Log files cleared."
    }
}

# 生成日志文件名
$logFile = "logs\server_$(Get-Date -Format 'yyyyMMdd').log"

# 写入启动信息
"[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Starting Django server..." | Out-File -FilePath $logFile

try {
    # 检查Python环境
    $pythonPath = "C:\ProgramData\miniforge3\envs\hcd\python.exe"
    if (!(Test-Path $pythonPath)) {
        throw "Python executable not found at: $pythonPath"
    }

    # 检查脚本文件
    if (!(Test-Path "run_waitress.py")) {
        throw "run_waitress.py not found in current directory"
    }

    # 启动进程并重定向输出
    $process = Start-Process -FilePath $pythonPath -ArgumentList "run_waitress.py" -WorkingDirectory "E:\dy" -WindowStyle Hidden -RedirectStandardOutput "$logFile.out" -RedirectStandardError "$logFile.err" -PassThru

    "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Process started with PID: $($process.Id)" | Out-File -FilePath $logFile -Append

    # 监控输出文件并合并到主日志
    while (!$process.HasExited) {
        Start-Sleep -Seconds 5
        
        # 读取标准输出
        if (Test-Path "$logFile.out") {
            $output = Get-Content "$logFile.out" -Tail 10
            if ($output) {
                $output | ForEach-Object { "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $_" } | Out-File -FilePath $logFile -Append
                Clear-Content "$logFile.out"
            }
        }
        
        # 读取错误输出
        if (Test-Path "$logFile.err") {
            $errors = Get-Content "$logFile.err" -Tail 10
            if ($errors) {
                $errors | ForEach-Object { "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ERROR: $_" } | Out-File -FilePath $logFile -Append
                Clear-Content "$logFile.err"
            }
        }
    }

} catch {
    "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] FATAL ERROR: $($_.Exception.Message)" | Out-File -FilePath $logFile -Append
    Write-Host "Error: $($_.Exception.Message)"
}