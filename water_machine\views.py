from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.db.models import Q, Sum, Count, Case, When, IntegerField, Value
from django.http import JsonResponse, HttpResponse
from decimal import Decimal
import pandas as pd
import io
from datetime import datetime
from .models import VillageCustomer, VillageRechargeRecord, TenantWaterCard
from .forms import (
    VillageCustomerForm, VillageRechargeForm, VillageCustomerSearchForm, VillageRechargeSearchForm, VillageCustomerImportForm,
    TenantWaterCardForm, TenantWaterCardSearchForm, TenantWaterCardImportForm, TenantWaterCardLostForm
)


class WaterMachineIndexView(LoginRequiredMixin, View):
    """售水机管理首页"""

    def get(self, request):
        try:
            # 村售水机统计数据
            village_customers = VillageCustomer.objects.count()
            village_normal_cards = VillageCustomer.objects.exclude(
                normal_card_number__isnull=True
            ).exclude(normal_card_number='').count()

            # 统计所有丢失卡的总数
            village_lost_cards = 0
            for customer in VillageCustomer.objects.exclude(
                lost_card_numbers__isnull=True
            ).exclude(lost_card_numbers=''):
                lost_cards = customer.get_lost_card_list()
                village_lost_cards += len(lost_cards)

            # 租户售水机统计数据
            tenant_records = TenantWaterCard.objects.count()
            tenant_normal_cards = TenantWaterCard.objects.exclude(
                normal_card_number__isnull=True
            ).exclude(normal_card_number='').count()

            # 统计所有丢失卡的总数
            tenant_lost_cards = 0
            for record in TenantWaterCard.objects.exclude(
                lost_card_numbers__isnull=True
            ).exclude(lost_card_numbers=''):
                lost_cards = record.get_lost_card_list()
                tenant_lost_cards += len(lost_cards)

            # 充值记录统计
            total_recharge_records = VillageRechargeRecord.objects.count()
            total_recharge_amount = VillageRechargeRecord.objects.aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')

            context = {
                'village_customers': village_customers,
                'village_normal_cards': village_normal_cards,
                'village_lost_cards': village_lost_cards,
                'tenant_records': tenant_records,
                'tenant_normal_cards': tenant_normal_cards,
                'tenant_lost_cards': tenant_lost_cards,
                'total_recharge_records': total_recharge_records,
                'total_recharge_amount': total_recharge_amount,
            }
            return render(request, 'water_machine/index.html', context)
        except Exception as e:
            messages.error(request, f'加载首页数据时出错：{str(e)}')
            # 返回基本的空数据
            context = {
                'village_customers': 0,
                'village_normal_cards': 0,
                'village_lost_cards': 0,
                'tenant_records': 0,
                'tenant_normal_cards': 0,
                'tenant_lost_cards': 0,
                'total_recharge_records': 0,
                'total_recharge_amount': Decimal('0.00'),
            }
            return render(request, 'water_machine/index.html', context)


# 村售水机充值系统视图
class VillageCustomerListView(LoginRequiredMixin, ListView):
    """村售水机客户列表视图"""
    model = VillageCustomer
    template_name = 'water_machine/village_customer_list.html'
    context_object_name = 'customers'
    paginate_by = 15

    def get_queryset(self):
        queryset = VillageCustomer.objects.all()
        form = VillageCustomerSearchForm(self.request.GET)

        if form.is_valid():
            search = form.cleaned_data.get('search')

            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(phone__icontains=search) |
                    Q(address__icontains=search) |
                    Q(normal_card_number__icontains=search) |
                    Q(lost_card_numbers__icontains=search)
                )

        # 自定义排序：将地址按数字逻辑排序
        # 使用Python排序来处理地址的正确数字排序
        customers_list = list(queryset)

        def sort_key(customer):
            address = customer.address
            # 尝试解析地址格式如 "1-101", "1-1001" 等
            try:
                if '-' in address:
                    parts = address.split('-')
                    if len(parts) >= 2:
                        # 提取楼号和房号的数字部分
                        building_num = int(''.join(filter(str.isdigit, parts[0]))) if parts[0] else 0
                        room_num = int(''.join(filter(str.isdigit, parts[1]))) if parts[1] else 0
                        return (building_num, room_num)
                else:
                    # 如果没有连字符，尝试提取数字
                    num = int(''.join(filter(str.isdigit, address))) if any(c.isdigit() for c in address) else 0
                    return (num, 0)
            except:
                # 如果解析失败，使用字符串排序
                return (999999, 999999, address)

            # 默认返回地址字符串排序
            return (999999, 999999, address)

        customers_list.sort(key=sort_key)

        # 将排序后的列表转换回QuerySet
        if customers_list:
            ordered_ids = [customer.id for customer in customers_list]
            # 使用Case/When来保持排序顺序
            preserved_order = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(ordered_ids)])
            return queryset.filter(id__in=ordered_ids).order_by(preserved_order)
        else:
            return queryset.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = VillageCustomerSearchForm(self.request.GET)

        # 添加统计数据
        queryset = self.get_queryset()
        total_customers = queryset.count()
        total_fixed_amount = Decimal('0.00')

        for customer in queryset:
            total_fixed_amount += customer.get_fixed_amount()

        context['stats'] = {
            'total_customers': total_customers,
            'total_fixed_amount': total_fixed_amount,
        }

        return context


class VillageCustomerCreateView(LoginRequiredMixin, CreateView):
    """创建村售水机客户视图"""
    model = VillageCustomer
    form_class = VillageCustomerForm
    template_name = 'water_machine/village_customer_form.html'
    success_url = reverse_lazy('water_machine:village_customer_list')

    def form_valid(self, form):
        messages.success(self.request, '客户信息创建成功！')
        return super().form_valid(form)


class VillageCustomerUpdateView(LoginRequiredMixin, UpdateView):
    """更新村售水机客户视图"""
    model = VillageCustomer
    form_class = VillageCustomerForm
    template_name = 'water_machine/village_customer_form.html'
    success_url = reverse_lazy('water_machine:village_customer_list')

    def form_valid(self, form):
        messages.success(self.request, '客户信息更新成功！')
        return super().form_valid(form)


class VillageCustomerDeleteView(LoginRequiredMixin, DeleteView):
    """删除村售水机客户视图"""
    model = VillageCustomer
    template_name = 'water_machine/village_customer_confirm_delete.html'
    success_url = reverse_lazy('water_machine:village_customer_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '客户信息删除成功！')
        return super().delete(request, *args, **kwargs)


class VillageCardLostView(LoginRequiredMixin, View):
    """村售水机水卡挂失视图"""

    def post(self, request, pk):
        customer = get_object_or_404(VillageCustomer, pk=pk)
        old_card = customer.normal_card_number

        # 将当前卡号标记为丢失，但不自动生成新卡号
        if old_card:
            lost_cards = customer.get_lost_card_list()
            lost_cards.append(old_card)
            customer.lost_card_numbers = ','.join(lost_cards)
            customer.normal_card_number = ''  # 清空正常卡号，等待手动输入
            customer.save()

            messages.warning(request, f'水卡 {old_card} 已标记为丢失，请为客户输入新的卡号！')
        else:
            messages.warning(request, '该客户没有正常卡号，请直接输入新的卡号！')

        return redirect('water_machine:village_customer_edit', pk=pk)


# 村售水机充值记录视图
class VillageRechargeListView(LoginRequiredMixin, ListView):
    """村售水机充值记录列表视图"""
    model = VillageRechargeRecord
    template_name = 'water_machine/village_recharge_list.html'
    context_object_name = 'recharge_records'
    paginate_by = 15

    def get_queryset(self):
        queryset = VillageRechargeRecord.objects.select_related('customer').all()
        form = VillageRechargeSearchForm(self.request.GET)

        if form.is_valid():
            search = form.cleaned_data.get('search')

            if search:
                queryset = queryset.filter(
                    Q(customer__name__icontains=search) |
                    Q(customer__address__icontains=search) |
                    Q(card_number__icontains=search)
                )

        return queryset.order_by('-recharge_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = VillageRechargeSearchForm(self.request.GET)

        # 添加统计数据
        queryset = self.get_queryset()
        total_records = queryset.count()
        total_amount = queryset.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        context['stats'] = {
            'total_records': total_records,
            'total_amount': total_amount,
        }

        return context


class VillageRechargeCreateView(LoginRequiredMixin, CreateView):
    """创建村售水机充值记录视图"""
    model = VillageRechargeRecord
    form_class = VillageRechargeForm
    template_name = 'water_machine/village_recharge_form.html'
    success_url = reverse_lazy('water_machine:village_recharge_list')

    def get_initial(self):
        initial = super().get_initial()
        # 如果URL中有参数，预填表单
        customer_id = self.request.GET.get('customer')
        quarter = self.request.GET.get('quarter')
        people_count = self.request.GET.get('people_count')
        notes = self.request.GET.get('notes')

        if customer_id:
            try:
                customer = VillageCustomer.objects.get(pk=customer_id)
                initial['customer'] = customer
                initial['people_count'] = people_count or customer.people_count
            except VillageCustomer.DoesNotExist:
                pass

        if quarter:
            initial['quarter'] = quarter

        if notes:
            initial['notes'] = notes

        return initial

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, '充值记录创建成功！')
            return response
        except Exception as e:
            # 处理数据库唯一性约束错误
            if 'unique constraint' in str(e).lower() or 'duplicate' in str(e).lower():
                form.add_error(None, '该客户在此季度已经充值过了，不能重复充值！')
            else:
                form.add_error(None, f'保存失败：{str(e)}')
            return self.form_invalid(form)


class VillageRechargeDeleteView(LoginRequiredMixin, DeleteView):
    """删除村售水机充值记录视图"""
    model = VillageRechargeRecord
    template_name = 'water_machine/village_recharge_confirm_delete.html'
    success_url = reverse_lazy('water_machine:village_recharge_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '充值记录删除成功！')
        return super().delete(request, *args, **kwargs)


class CustomerQuartersAPIView(LoginRequiredMixin, View):
    """获取客户已充值季度的API视图"""

    def get(self, request, customer_id):
        try:
            customer = VillageCustomer.objects.get(pk=customer_id)
            # 获取当年已充值的季度
            import datetime
            current_year = datetime.datetime.now().year
            paid_quarters = list(customer.recharge_records.filter(
                quarter__startswith=str(current_year)
            ).values_list('quarter', flat=True))

            return JsonResponse({
                'success': True,
                'customer_name': customer.name,
                'paid_quarters': paid_quarters
            })
        except VillageCustomer.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '客户不存在'
            }, status=404)


class VillageCustomerExportView(LoginRequiredMixin, View):
    """村售水机客户导出视图"""

    def get(self, request):
        # 获取所有客户数据
        customers = VillageCustomer.objects.all().order_by('created_at')

        # 准备导出数据
        data = []
        for customer in customers:
            # 将时区感知的datetime转换为本地时间
            from django.utils import timezone
            local_created_time = timezone.localtime(customer.created_at)

            quarter_status = customer.get_quarter_status()
            data.append({
                '姓名': customer.name,
                '住址': customer.address,
                '电话': customer.phone,
                '水桶数量': customer.water_bucket,
                '正常卡号': customer.normal_card_number or '',
                '丢失卡号': customer.get_lost_card_display(),
                '使用人数': customer.people_count,
                '季度金额': float(customer.get_fixed_amount()),
                'Q1状态': '已充值' if quarter_status.get('Q1') else '未充值',
                'Q2状态': '已充值' if quarter_status.get('Q2') else '未充值',
                'Q3状态': '已充值' if quarter_status.get('Q3') else '未充值',
                'Q4状态': '已充值' if quarter_status.get('Q4') else '未充值',
                '创建时间': local_created_time.strftime('%Y-%m-%d %H:%M:%S'),
            })

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='村售水机客户', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f'村售水机客户_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response


class VillageCustomerImportView(LoginRequiredMixin, View):
    """村售水机客户批量导入视图"""

    def get(self, request):
        form = VillageCustomerImportForm()
        return render(request, 'water_machine/village_customer_import.html', {'form': form})

    def post(self, request):
        form = VillageCustomerImportForm(request.POST, request.FILES)

        if form.is_valid():
            try:
                file = request.FILES['file']

                # 读取Excel文件
                df = pd.read_excel(file)

                # 验证必需的列
                required_columns = ['姓名', '住址', '电话', '水桶数量', '正常卡号', '使用人数']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    messages.error(request, f'Excel文件缺少必需的列：{", ".join(missing_columns)}')
                    return render(request, 'water_machine/village_customer_import.html', {'form': form})

                # 处理数据
                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # 检查必填字段
                        if pd.isna(row['姓名']) or pd.isna(row['电话']):
                            errors.append(f'第{index+2}行：姓名和电话不能为空')
                            error_count += 1
                            continue

                        # 验证电话号码
                        phone = str(row['电话']).strip()
                        phone_digits = ''.join(filter(str.isdigit, phone))
                        if len(phone_digits) != 11:
                            errors.append(f'第{index+2}行：电话号码必须是11位数字')
                            error_count += 1
                            continue
                        if not phone_digits.startswith('1'):
                            errors.append(f'第{index+2}行：手机号码必须以1开头')
                            error_count += 1
                            continue

                        # 验证住址唯一性
                        address = str(row['住址']).strip() if not pd.isna(row['住址']) else ''
                        if address and VillageCustomer.objects.filter(address=address).exists():
                            errors.append(f'第{index+2}行：住址"{address}"已存在')
                            error_count += 1
                            continue

                        # 检查卡号是否重复
                        card_number = str(row['正常卡号']).strip() if not pd.isna(row['正常卡号']) else ''
                        if card_number and VillageCustomer.objects.filter(normal_card_number=card_number).exists():
                            errors.append(f'第{index+2}行：卡号{card_number}已存在')
                            error_count += 1
                            continue

                        # 创建客户
                        customer = VillageCustomer.objects.create(
                            name=str(row['姓名']).strip(),
                            address=address,
                            phone=phone_digits,  # 使用验证后的11位数字
                            water_bucket=int(row['水桶数量']) if not pd.isna(row['水桶数量']) else 1,
                            normal_card_number=card_number,
                            lost_card_numbers=str(row['丢失卡号']).strip() if not pd.isna(row['丢失卡号']) else '',
                            people_count=int(row['使用人数']) if not pd.isna(row['使用人数']) else 1,
                        )
                        success_count += 1

                    except Exception as e:
                        errors.append(f'第{index+2}行：{str(e)}')
                        error_count += 1

                # 显示结果
                if success_count > 0:
                    messages.success(request, f'成功导入{success_count}条客户记录')

                if error_count > 0:
                    error_msg = f'导入失败{error_count}条记录'
                    if errors:
                        error_msg += '：\n' + '\n'.join(errors[:10])  # 只显示前10个错误
                        if len(errors) > 10:
                            error_msg += f'\n...还有{len(errors)-10}个错误'
                    messages.error(request, error_msg)

                if success_count > 0:
                    return redirect('water_machine:village_customer_list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, 'water_machine/village_customer_import.html', {'form': form})


class VillageCustomerImportTemplateView(LoginRequiredMixin, View):
    """下载导入模板"""

    def get(self, request):
        # 创建模板数据
        template_data = {
            '姓名': ['张三', '李四'],
            '住址': ['村东路1号', '村西路2号'],
            '电话': ['13800138001', '13900139002'],
            '水桶数量': [1, 2],
            '正常卡号': ['12345678', '87654321'],
            '丢失卡号': ['', '11111111,22222222'],
            '使用人数': [3, 5],
        }

        df = pd.DataFrame(template_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='客户导入模板', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="村售水机客户导入模板.xlsx"'

        return response


# AJAX视图
class CalculateAmountView(LoginRequiredMixin, View):
    """计算充值金额的AJAX视图"""
    
    def get(self, request):
        people_count = request.GET.get('people_count', 0)
        try:
            people_count = int(people_count)
            amount = people_count * 15
            return JsonResponse({'amount': amount})
        except (ValueError, TypeError):
            return JsonResponse({'amount': 0})


class VillageRechargeExportView(LoginRequiredMixin, View):
    """村售水机充值记录导出视图"""

    def get(self, request):
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')

        if not start_date or not end_date:
            messages.error(request, '请选择开始日期和结束日期')
            return redirect('water_machine:village_recharge_list')

        try:
            # 解析日期并转换为时区感知的datetime对象
            from datetime import datetime, time
            from django.utils import timezone

            # 创建naive datetime对象
            start_naive = datetime.combine(
                datetime.strptime(start_date, '%Y-%m-%d').date(),
                time.min
            )
            end_naive = datetime.combine(
                datetime.strptime(end_date, '%Y-%m-%d').date(),
                time.max
            )

            # 转换为时区感知的datetime对象
            start_datetime = timezone.make_aware(start_naive)
            end_datetime = timezone.make_aware(end_naive)

            # 获取时间范围内的充值记录
            records = VillageRechargeRecord.objects.select_related('customer').filter(
                recharge_date__gte=start_datetime,
                recharge_date__lte=end_datetime
            ).order_by('-recharge_date')

            # 如果没有找到记录，提示用户但不导出所有记录
            if not records.exists():
                messages.warning(request, f'在指定日期范围（{start_date} 至 {end_date}）内没有找到充值记录')
                return redirect('water_machine:village_recharge_list')

            # 准备导出数据
            data = []
            for record in records:
                # 将时区感知的datetime转换为本地时间
                from django.utils import timezone
                local_time = timezone.localtime(record.recharge_date)

                data.append({
                    '客户姓名': record.customer.name,
                    '住址': record.customer.address,
                    '使用卡号': record.card_number,
                    '使用人数': record.people_count,
                    '充值金额': float(record.amount),
                    '充值季度': record.quarter,
                    '充值时间': local_time.strftime('%Y-%m-%d %H:%M:%S'),
                    '备注': record.notes or '',
                })

            # 创建DataFrame
            if data:
                df = pd.DataFrame(data)
            else:
                # 创建空的DataFrame但包含列名
                df = pd.DataFrame(columns=[
                    '客户姓名', '住址', '使用卡号', '使用人数',
                    '充值金额', '充值季度', '充值时间', '备注'
                ])

            # 创建Excel文件
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='充值记录', index=False)

            output.seek(0)

            # 设置响应
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            filename = f'村售水机充值记录_{start_datetime.strftime("%Y%m%d")}_{end_datetime.strftime("%Y%m%d")}.xlsx'
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except ValueError:
            messages.error(request, '日期格式错误')
            return redirect('water_machine:village_recharge_list')
        except Exception as e:
            messages.error(request, f'导出失败：{str(e)}')
            return redirect('water_machine:village_recharge_list')


# 租户售水机卡号记录系统
class TenantWaterCardListView(LoginRequiredMixin, ListView):
    """租户售水机卡号记录列表视图"""
    model = TenantWaterCard
    template_name = 'water_machine/tenant_water_card_list.html'
    context_object_name = 'records'
    paginate_by = 15

    def get_queryset(self):
        queryset = TenantWaterCard.objects.all().order_by('-record_time')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(address__icontains=search) |
                Q(phone__icontains=search) |
                Q(normal_card_number__icontains=search) |
                Q(lost_card_numbers__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TenantWaterCardSearchForm(self.request.GET)

        # 获取当前查询集
        queryset = self.get_queryset()

        # 统计信息
        context['total_count'] = queryset.count()

        # 计算总桶数
        from django.db.models import Sum
        total_buckets = queryset.aggregate(total_buckets=Sum('water_bucket'))['total_buckets'] or 0
        context['total_buckets'] = total_buckets

        # 计算正常卡数（有正常卡号的记录数）
        normal_cards_count = queryset.exclude(
            normal_card_number__isnull=True
        ).exclude(normal_card_number='').count()
        context['normal_cards_count'] = normal_cards_count

        return context


class TenantWaterCardCreateView(LoginRequiredMixin, CreateView):
    """租户售水机卡号记录创建视图"""
    model = TenantWaterCard
    form_class = TenantWaterCardForm
    template_name = 'water_machine/tenant_water_card_form.html'
    success_url = reverse_lazy('water_machine:tenant_water_card_list')

    def form_valid(self, form):
        messages.success(self.request, '租户售水机卡号记录创建成功！')
        return super().form_valid(form)


class TenantWaterCardUpdateView(LoginRequiredMixin, UpdateView):
    """租户售水机卡号记录编辑视图"""
    model = TenantWaterCard
    form_class = TenantWaterCardForm
    template_name = 'water_machine/tenant_water_card_form.html'
    success_url = reverse_lazy('water_machine:tenant_water_card_list')

    def form_valid(self, form):
        messages.success(self.request, '租户售水机卡号记录更新成功！')
        return super().form_valid(form)


class TenantWaterCardDeleteView(LoginRequiredMixin, DeleteView):
    """租户售水机卡号记录删除视图"""
    model = TenantWaterCard
    template_name = 'water_machine/tenant_water_card_confirm_delete.html'
    success_url = reverse_lazy('water_machine:tenant_water_card_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '租户售水机卡号记录删除成功！')
        return super().delete(request, *args, **kwargs)


class TenantWaterCardExportView(LoginRequiredMixin, View):
    """租户售水机卡号记录导出视图"""

    def get(self, request):
        # 获取所有记录数据
        records = TenantWaterCard.objects.all().order_by('-record_time')

        # 准备导出数据
        data = []
        for record in records:
            # 将时区感知的datetime转换为本地时间
            from django.utils import timezone
            local_time = timezone.localtime(record.record_time)

            data.append({
                '姓名': record.name,
                '住址': record.address,
                '电话': record.phone,
                '水桶数量': record.water_bucket,
                '正常卡号': record.normal_card_number or '',
                '丢失卡号': record.get_lost_card_display(),
                '记录时间': local_time.strftime('%Y-%m-%d %H:%M:%S'),
            })

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='租户售水机卡号记录', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f'租户售水机卡号记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response


class TenantWaterCardImportView(LoginRequiredMixin, View):
    """租户售水机卡号记录批量导入视图"""

    def get(self, request):
        form = TenantWaterCardImportForm()
        return render(request, 'water_machine/tenant_water_card_import.html', {'form': form})

    def post(self, request):
        form = TenantWaterCardImportForm(request.POST, request.FILES)

        if form.is_valid():
            try:
                file = request.FILES['file']

                # 读取Excel文件
                df = pd.read_excel(file)

                # 检查必需的列
                required_columns = ['姓名', '住址', '电话', '水桶数量', '正常卡号', '丢失卡号']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    messages.error(request, f'Excel文件缺少必需的列：{", ".join(missing_columns)}')
                    return render(request, 'water_machine/tenant_water_card_import.html', {'form': form})

                # 处理数据
                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # 检查必填字段
                        if pd.isna(row['姓名']) or pd.isna(row['电话']):
                            errors.append(f'第{index+2}行：姓名和电话不能为空')
                            error_count += 1
                            continue

                        # 验证电话号码
                        phone = str(row['电话']).strip()
                        phone_digits = ''.join(filter(str.isdigit, phone))
                        if len(phone_digits) != 11:
                            errors.append(f'第{index+2}行：电话号码必须是11位数字')
                            error_count += 1
                            continue
                        if not phone_digits.startswith('1'):
                            errors.append(f'第{index+2}行：手机号码必须以1开头')
                            error_count += 1
                            continue

                        # 验证住址唯一性
                        address = str(row['住址']).strip() if not pd.isna(row['住址']) else ''
                        if address and TenantWaterCard.objects.filter(address=address).exists():
                            errors.append(f'第{index+2}行：住址"{address}"已存在')
                            error_count += 1
                            continue

                        # 创建记录
                        record = TenantWaterCard.objects.create(
                            name=str(row['姓名']).strip(),
                            address=address,
                            phone=phone_digits,
                            water_bucket=int(row['水桶数量']) if not pd.isna(row['水桶数量']) and str(row['水桶数量']).strip() else 0,
                            normal_card_number=str(row['正常卡号']).strip() if not pd.isna(row['正常卡号']) else '',
                            lost_card_numbers=str(row['丢失卡号']).strip() if not pd.isna(row['丢失卡号']) else '',
                        )
                        success_count += 1

                    except Exception as e:
                        errors.append(f'第{index+2}行：{str(e)}')
                        error_count += 1

                # 显示结果
                if success_count > 0:
                    messages.success(request, f'成功导入 {success_count} 条记录')

                if error_count > 0:
                    error_message = f'导入失败 {error_count} 条记录：\n' + '\n'.join(errors[:10])
                    if len(errors) > 10:
                        error_message += f'\n... 还有 {len(errors) - 10} 个错误'
                    messages.error(request, error_message)

                if success_count > 0:
                    return redirect('water_machine:tenant_water_card_list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, 'water_machine/tenant_water_card_import.html', {'form': form})


class TenantWaterCardImportTemplateView(LoginRequiredMixin, View):
    """下载导入模板"""

    def get(self, request):
        # 创建模板数据
        template_data = {
            '姓名': ['张三', '李四'],
            '住址': ['A栋101', 'B栋202'],
            '电话': ['13800138001', '13900139002'],
            '水桶数量': [0, 2],
            '正常卡号': ['12345678', '87654321'],
            '丢失卡号': ['', '11111111,22222222'],
        }

        # 创建DataFrame
        df = pd.DataFrame(template_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='租户售水机卡号记录模板', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="租户售水机卡号记录导入模板.xlsx"'

        return response


class TenantWaterCardLostView(LoginRequiredMixin, View):
    """租户售水机水卡挂失视图"""

    def get(self, request, pk):
        tenant_card = get_object_or_404(TenantWaterCard, pk=pk)
        form = TenantWaterCardLostForm(tenant_card=tenant_card)

        context = {
            'tenant_card': tenant_card,
            'form': form,
        }
        return render(request, 'water_machine/tenant_water_card_lost.html', context)

    def post(self, request, pk):
        tenant_card = get_object_or_404(TenantWaterCard, pk=pk)
        form = TenantWaterCardLostForm(request.POST, tenant_card=tenant_card)

        if form.is_valid():
            lost_card_number = form.cleaned_data['lost_card_number']
            new_card_number = form.cleaned_data.get('new_card_number')

            # 执行挂失操作
            success, message = tenant_card.report_card_lost(lost_card_number, new_card_number)

            if success:
                messages.success(request, message)
                if new_card_number:
                    messages.info(request, f'新卡号 {new_card_number} 已设置为正常卡号')
                return redirect('water_machine:tenant_water_card_list')
            else:
                messages.error(request, message)

        context = {
            'tenant_card': tenant_card,
            'form': form,
        }
        return render(request, 'water_machine/tenant_water_card_lost.html', context)
