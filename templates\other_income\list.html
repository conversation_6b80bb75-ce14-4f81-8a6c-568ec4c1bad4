{% extends 'base/base.html' %}
{% load static %}

{% block title %}物业其它收入流水统计{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-coins me-2"></i>物业其它收入流水统计
                            </h4>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 数据看板 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">今日收入</h6>
                                            <h4 class="mb-0">¥{{ today_amount|floatformat:2 }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-day fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">本月收入</h6>
                                            <h4 class="mb-0">¥{{ current_month_amount|floatformat:2 }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">上月收入</h6>
                                            <h4 class="mb-0">¥{{ last_month_amount|floatformat:2 }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-minus fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">总收入</h6>
                                            <h4 class="mb-0">¥{{ total_amount|floatformat:2 }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-chart-line fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮和搜索表单 -->
                    <div class="mb-4">
                        <div class="row g-3 align-items-center">
                            <div class="col-md-3">
                                <a href="{% url 'other_income:create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>新增收入记录
                                </a>
                                <a href="{% url 'other_income:export' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-success ms-2">
                                    <i class="fas fa-download me-1"></i>导出流水
                                </a>
                            </div>
                            <div class="col-md-9">
                                <form method="get" class="d-flex justify-content-end">
                                    <div class="search-box me-2" style="width: 250px;">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" name="search" class="form-control"
                                               placeholder="搜索备注..."
                                               value="{{ request.GET.search }}">
                                    </div>
                                    <input type="date" name="start_date" class="form-control me-2" style="width: 150px;" 
                                           value="{{ request.GET.start_date }}" placeholder="开始日期">
                                    <input type="date" name="end_date" class="form-control me-2" style="width: 150px;" 
                                           value="{{ request.GET.end_date }}" placeholder="结束日期">
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'other_income:list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>

                    {% if records %}
                        <!-- 数据表格 - 分离表头表体结构 -->
                        <div class="table-container">
                            <!-- 固定表头 -->
                            <div class="table-header-fixed">
                                <table class="table" id="header-table">
                                    <thead>
                                        <tr>
                                            <th>收入日期</th>
                                            <th>总金额</th>
                                            <th>水费</th>
                                            <th>储藏间电费</th>
                                            <th>净水机费</th>
                                            <th>净水卡费</th>
                                            <th>水桶费</th>
                                            <th>商铺水费</th>
                                            <th>补自来水卡费</th>
                                            <th>补净水机卡费</th>
                                            <th>停车场收费</th>
                                            <th>电梯梯控卡费</th>
                                            <th>自来水表电池费</th>
                                            <th>其它费用</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>

                            <!-- 可滚动表体 -->
                            <div class="table-body-scroll">
                                <table class="table table-striped table-hover" id="data-table">
                                    <thead style="visibility: hidden;">
                                        <tr>
                                            <th>收入日期</th>
                                            <th>总金额</th>
                                            <th>水费</th>
                                            <th>储藏间电费</th>
                                            <th>净水机费</th>
                                            <th>净水卡费</th>
                                            <th>水桶费</th>
                                            <th>商铺水费</th>
                                            <th>补自来水卡费</th>
                                            <th>补净水机卡费</th>
                                            <th>停车场收费</th>
                                            <th>电梯梯控卡费</th>
                                            <th>自来水表电池费</th>
                                            <th>其它费用</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for record in records %}
                                        <tr>
                                            <td>{{ record.income_date|date:"Y-m-d" }}</td>
                                            <td class="fw-bold text-success">¥{{ record.amount|floatformat:2 }}</td>
                                            <td>{{ record.water_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.storage_electricity_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.water_purifier_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.water_card_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.water_bucket_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.shop_water_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.water_card_supplement_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.water_purifier_card_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.parking_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.elevator_card_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.water_meter_battery_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.other_fee|default:"0.00"|floatformat:2 }}</td>
                                            <td>{{ record.notes|default:"-" }}</td>
                                            <td>
                                                <div class="income-action-buttons">
                                                    <a href="{% url 'other_income:edit' record.pk %}" class="wave-btn edit-wave" title="编辑收入记录">
                                                        <i class="fas fa-edit"></i>
                                                        <span>编辑</span>
                                                    </a>
                                                    <a href="{% url 'other_income:delete' record.pk %}" class="wave-btn delete-wave" title="删除收入记录">
                                                        <i class="fas fa-trash"></i>
                                                        <span>删除</span>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 美化的分页导航 -->
                        {% if is_paginated %}
                        <div class="pagination-container">
                            <!-- 分页统计信息 -->
                            <div class="pagination-info">
                                <div class="info-item">
                                    <i class="fas fa-list-ol text-primary"></i>
                                    <span class="info-text">
                                        共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                    </span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-file-alt text-success"></i>
                                    <span class="info-text">
                                        每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                    </span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-bookmark text-info"></i>
                                    <span class="info-text">
                                        第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                    </span>
                                </div>
                            </div>

                            <!-- 分页按钮 -->
                            <nav aria-label="分页导航" class="pagination-nav">
                                <ul class="pagination pagination-modern">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="首页">
                                                <i class="fas fa-angle-double-left"></i>
                                                <span class="d-none d-sm-inline">首页</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="上一页">
                                                <i class="fas fa-angle-left"></i>
                                                <span class="d-none d-sm-inline">上一页</span>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-angle-double-left"></i>
                                                <span class="d-none d-sm-inline">首页</span>
                                            </span>
                                        </li>
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <i class="fas fa-angle-left"></i>
                                                <span class="d-none d-sm-inline">上一页</span>
                                            </span>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link page-link-current">
                                            {{ page_obj.number }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="下一页">
                                                <span class="d-none d-sm-inline">下一页</span>
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="末页">
                                                <span class="d-none d-sm-inline">末页</span>
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <span class="d-none d-sm-inline">下一页</span>
                                                <i class="fas fa-angle-right"></i>
                                            </span>
                                        </li>
                                        <li class="page-item disabled">
                                            <span class="page-link">
                                                <span class="d-none d-sm-inline">末页</span>
                                                <i class="fas fa-angle-double-right"></i>
                                            </span>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无收入记录</h5>
                            <p class="text-muted">点击上方按钮添加第一条收入记录</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin-bottom: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1200px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding-top: 0 !important;
        background: white;
    }

    .table-body-scroll table {
        margin-bottom: 0 !important;
        background: white;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        width: 100%;
        min-width: 1200px;
    }

    /* 表格容器间距最小化 */
    .table-container {
        margin-bottom: 0 !important;
    }

    #header-table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1200px;
    }

    #data-table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1200px;
    }

    /* 表体隐藏表头 */
    #data-table thead {
        visibility: hidden;
        height: 0 !important;
        line-height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    #data-table thead th {
        padding: 0 !important;
        height: 0 !important;
        border: none !important;
        margin: 0 !important;
        line-height: 0 !important;
    }

    /* 表格内容样式 */
    #data-table td {
        padding: 0.6rem 0.5rem;
        text-align: center !important;
        vertical-align: middle !important;
        border-top: 1px solid #dee2e6;
        border-right: 1px solid #dee2e6;
        font-size: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }

    /* 备注列和操作列允许换行 */
    #data-table td:nth-child(14),
    #data-table td:nth-child(15) {
        white-space: normal !important;
        overflow: visible;
        text-overflow: unset;
    }

    #data-table td:last-child {
        border-right: none;
        white-space: normal;
        text-align: center !important;
    }

    /* 备注列不截断，支持换行 */
    #data-table td:nth-child(14) {
        white-space: normal !important;
        word-break: break-word;
        word-wrap: break-word;
        text-align: center !important;
        max-width: 150px;
        line-height: 1.4;
        padding: 0.6rem 0.3rem;
    }

    /* 鼠标悬停效果 */
    #data-table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* 列宽由JavaScript动态设置，这里不需要CSS定义 */

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.375rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }

        .table-body-scroll {
            max-height: 60vh;
        }

        #header-table th,
        #data-table td {
            font-size: 14px;
            padding: 0.5rem 0.4rem;
        }
    }

    /* 物业其它收入专用波浪形液体风格按钮 */
    .income-action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
        padding: 6px;
    }

    .wave-btn {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        min-width: 70px;
        overflow: hidden;
        border-radius: 25px;
        color: white;
        text-transform: capitalize;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 1;
    }

    .wave-btn i {
        font-size: 14px;
        margin-right: 6px;
        transition: transform 0.2s ease;
        position: relative;
        z-index: 10;
    }

    .wave-btn span {
        font-size: 11px;
        font-weight: 700;
        position: relative;
        z-index: 10;
    }

    .wave-btn:hover {
        transform: translateY(-2px);
        text-decoration: none;
        color: white;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    }

    .wave-btn:hover i {
        transform: scale(1.1);
    }

    .wave-btn:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }

    /* 编辑按钮 - 蓝色液体波浪 */
    .edit-wave {
        background: linear-gradient(45deg, #4facfe, #00f2fe);
        position: relative;
    }

    .edit-wave:hover {
        background: linear-gradient(45deg, #3d8bfe, #00d4fe);
        color: white;
    }

    .edit-wave::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.2) 30%, transparent 60%);
        border-radius: 25px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 2;
        pointer-events: none;
    }

    .edit-wave:hover::before {
        opacity: 1;
    }

    /* 删除按钮 - 红色液体波浪 */
    .delete-wave {
        background: linear-gradient(45deg, #fa709a, #fee140);
        position: relative;
    }

    .delete-wave:hover {
        background: linear-gradient(45deg, #f85f88, #fdd835);
        color: white;
    }

    .delete-wave::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.2) 35%, transparent 65%);
        border-radius: 25px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 2;
        pointer-events: none;
    }

    .delete-wave:hover::before {
        opacity: 1;
    }

    /* 简化的光效动画 */
    .wave-btn::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        border-radius: 25px;
        transition: left 0.6s ease;
        z-index: 5;
        pointer-events: none;
    }

    .wave-btn:hover::after {
        left: 100%;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .income-action-buttons {
            gap: 6px;
            flex-wrap: wrap;
        }

        .wave-btn {
            padding: 6px 12px;
            min-width: 60px;
            font-size: 10px;
        }

        .wave-btn i {
            font-size: 12px;
            margin-right: 4px;
        }

        .wave-btn span {
            font-size: 9px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 同步表头和表体列宽
    function syncColumnWidths() {
        const headerTable = document.getElementById('header-table');
        const dataTable = document.getElementById('data-table');

        if (!headerTable || !dataTable) {
            console.log('表头或表体表格未找到');
            return;
        }

        const headerCells = headerTable.querySelectorAll('th');
        const bodyHeaderCells = dataTable.querySelectorAll('thead th');

        // 定义列宽 - 其它收入流水表格的列宽配置
        const columnWidths = [
            '6%',   // 收入日期
            '6%',   // 总金额
            '5%',   // 水费
            '7%',   // 储藏间电费
            '6%',   // 净水机费
            '6%',   // 净水卡费
            '5%',   // 水桶费
            '6%',   // 商铺水费
            '7%',   // 补自来水卡费
            '7%',   // 补净水机卡费
            '6%',   // 停车场收费
            '6%',   // 电梯梯控卡费
            '7%',   // 自来水表电池费
            '5%',   // 其它费用
            '5%',   // 备注
            '15%'   // 操作
        ];

        // 设置表头列宽
        headerCells.forEach((th, index) => {
            if (index < columnWidths.length) {
                th.style.width = columnWidths[index];
                th.style.minWidth = columnWidths[index];
                th.style.maxWidth = columnWidths[index];
                th.style.boxSizing = 'border-box';
            }
        });

        // 设置表体隐藏表头列宽（用于对齐）
        bodyHeaderCells.forEach((th, index) => {
            if (index < columnWidths.length) {
                th.style.width = columnWidths[index];
                th.style.minWidth = columnWidths[index];
                th.style.maxWidth = columnWidths[index];
                th.style.boxSizing = 'border-box';
            }
        });

        // 设置表体数据列宽
        const bodyRows = dataTable.querySelectorAll('tbody tr');
        bodyRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach((td, index) => {
                if (index < columnWidths.length) {
                    td.style.width = columnWidths[index];
                    td.style.minWidth = columnWidths[index];
                    td.style.maxWidth = columnWidths[index];
                    td.style.boxSizing = 'border-box';
                }
            });
        });

        // 确保两个表格宽度一致
        headerTable.style.width = '100%';
        dataTable.style.width = '100%';
        headerTable.style.minWidth = '1200px';
        dataTable.style.minWidth = '1200px';

        console.log('✅ 表头表体列宽同步完成');
    }

    // 初始同步
    setTimeout(syncColumnWidths, 100);

    // 窗口大小改变时重新同步
    window.addEventListener('resize', function() {
        setTimeout(syncColumnWidths, 50);
    });
});
</script>
{% endblock %}
