from django import forms
from django.utils import timezone
from .models import VillageCustomer, VillageRechargeRecord, TenantWaterCard, get_quarter_choices


class VillageCustomerForm(forms.ModelForm):
    """村售水机客户信息表单"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 如果是编辑模式且没有正常卡号，则设为必填
        if self.instance and self.instance.pk and not self.instance.normal_card_number:
            self.fields['normal_card_number'].required = True
            self.fields['normal_card_number'].widget.attrs['placeholder'] = '请输入新的水卡号（必填）'
            self.fields['normal_card_number'].widget.attrs['class'] += ' border-warning'

    def clean_normal_card_number(self):
        """验证正常卡号"""
        card_number = self.cleaned_data.get('normal_card_number')

        if card_number:
            # 检查卡号是否已被其他客户使用
            existing_customer = VillageCustomer.objects.filter(
                normal_card_number=card_number
            ).exclude(pk=self.instance.pk if self.instance else None).first()

            if existing_customer:
                raise forms.ValidationError(f'卡号 {card_number} 已被客户 {existing_customer.name} 使用，请输入其他卡号！')

        return card_number

    def clean_phone(self):
        """验证电话号码"""
        phone = self.cleaned_data.get('phone')
        if phone:
            # 去除所有非数字字符
            phone = ''.join(filter(str.isdigit, phone))

            # 检查是否为11位数字
            if len(phone) != 11:
                raise forms.ValidationError('电话号码必须是11位数字')

            # 检查是否以1开头（中国手机号格式）
            if not phone.startswith('1'):
                raise forms.ValidationError('手机号码必须以1开头')

        return phone

    def clean_address(self):
        """验证住址唯一性"""
        address = self.cleaned_data.get('address')
        if address:
            address = address.strip()

            # 检查住址是否重复（排除当前编辑的记录）
            existing_query = VillageCustomer.objects.filter(address=address)
            if self.instance and self.instance.pk:
                existing_query = existing_query.exclude(pk=self.instance.pk)

            if existing_query.exists():
                existing_customer = existing_query.first()
                raise forms.ValidationError(f'住址"{address}"已被客户"{existing_customer.name}"使用，请输入不同的住址')

        return address

    class Meta:
        model = VillageCustomer
        fields = ['name', 'address', 'phone', 'water_bucket', 'normal_card_number', 'lost_card_numbers', 'people_count']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入客户姓名'
            }),
            'address': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入住址'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入11位手机号码',
                'maxlength': '11',
                'pattern': '[0-9]{11}',
                'title': '请输入11位数字'
            }),
            'water_bucket': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'value': '1',
                'placeholder': '请输入水桶数量'
            }),
            'normal_card_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入正常卡号'
            }),
            'lost_card_numbers': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': '请输入丢失卡号，多个卡号用逗号分隔'
            }),
            'people_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': '请输入使用人数'
            }),
        }


class VillageRechargeForm(forms.ModelForm):
    """村售水机充值表单"""

    # 定义季度选择字段
    quarter = forms.ChoiceField(
        label='充值季度',
        choices=[],  # 在__init__中动态设置
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 设置季度选择
        self.fields['quarter'].choices = [('', '请选择充值季度')] + get_quarter_choices()

        # 自定义客户选择显示 - 按地址数字排序
        customer_choices = [('', '请选择客户')]

        # 获取所有客户并进行自定义排序
        all_customers = list(VillageCustomer.objects.all())

        def sort_key(customer):
            address = customer.address
            # 尝试解析地址格式如 "1-101", "1-1001" 等
            try:
                if '-' in address:
                    parts = address.split('-')
                    if len(parts) >= 2:
                        # 提取楼号和房号的数字部分
                        building_num = int(''.join(filter(str.isdigit, parts[0]))) if parts[0] else 0
                        room_num = int(''.join(filter(str.isdigit, parts[1]))) if parts[1] else 0
                        return (building_num, room_num)
                else:
                    # 如果没有连字符，尝试提取数字
                    num = int(''.join(filter(str.isdigit, address))) if any(c.isdigit() for c in address) else 0
                    return (num, 0)
            except:
                # 如果解析失败，使用字符串排序
                return (999999, 999999, address)

            # 默认返回地址字符串排序
            return (999999, 999999, address)

        all_customers.sort(key=sort_key)

        for customer in all_customers:
            label = f"{customer.name} - {customer.phone} ({customer.people_count}人)"
            customer_choices.append((customer.pk, label))
        self.fields['customer'].choices = customer_choices

    def clean(self):
        """验证表单数据，防止重复充值"""
        cleaned_data = super().clean()
        customer = cleaned_data.get('customer')
        quarter = cleaned_data.get('quarter')

        if customer and quarter:
            # 检查是否已经存在该客户该季度的充值记录
            existing_record = VillageRechargeRecord.objects.filter(
                customer=customer,
                quarter=quarter
            ).exists()

            if existing_record:
                raise forms.ValidationError(f'客户 {customer.name} 在 {quarter} 季度已经充值过了，不能重复充值！')

        return cleaned_data

    class Meta:
        model = VillageRechargeRecord
        fields = ['customer', 'people_count', 'quarter', 'notes']
        widgets = {
            'customer': forms.Select(attrs={
                'class': 'form-select'
            }),
            'people_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': '请输入使用人数'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '请输入备注信息（可选）'
            }),
        }


class VillageCustomerSearchForm(forms.Form):
    """村售水机客户搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索姓名、电话、住址、卡号...'
        })
    )


class VillageRechargeSearchForm(forms.Form):
    """村售水机充值记录搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索客户姓名、住址、卡号...'
        })
    )


class VillageCustomerImportForm(forms.Form):
    """村售水机客户批量导入表单"""
    file = forms.FileField(
        label='选择Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls',
        }),
        help_text='支持.xlsx和.xls格式，请按照模板格式填写数据'
    )

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # 检查文件扩展名
            if not file.name.lower().endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('请上传Excel文件（.xlsx或.xls格式）')

            # 检查文件大小（限制为5MB）
            if file.size > 5 * 1024 * 1024:
                raise forms.ValidationError('文件大小不能超过5MB')

        return file


class TenantWaterCardForm(forms.ModelForm):
    """租户售水机卡号记录表单"""

    class Meta:
        model = TenantWaterCard
        fields = ['name', 'address', 'phone', 'water_bucket', 'normal_card_number', 'lost_card_numbers']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '请输入租户姓名'}),
            'address': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '请输入租户住址'}),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入11位手机号码',
                'maxlength': '11',
                'pattern': '[0-9]{11}',
                'title': '请输入11位数字'
            }),
            'water_bucket': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '10'}),
            'normal_card_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '请输入正常卡号'}),
            'lost_card_numbers': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': '请输入丢失卡号，多个卡号用逗号分隔'}),
        }
        labels = {
            'name': '租户姓名',
            'address': '住址',
            'phone': '电话',
            'water_bucket': '水桶数量',
            'normal_card_number': '正常卡号',
            'lost_card_numbers': '丢失卡号',
        }

    def clean_phone(self):
        """验证电话号码"""
        phone = self.cleaned_data.get('phone')
        if phone:
            # 去除所有非数字字符
            phone = ''.join(filter(str.isdigit, phone))

            # 检查是否为11位数字
            if len(phone) != 11:
                raise forms.ValidationError('电话号码必须是11位数字')

            # 检查是否以1开头（中国手机号格式）
            if not phone.startswith('1'):
                raise forms.ValidationError('手机号码必须以1开头')

        return phone

    def clean_address(self):
        """验证住址唯一性"""
        address = self.cleaned_data.get('address')
        if address:
            address = address.strip()

            # 检查住址是否重复（排除当前编辑的记录）
            existing_query = TenantWaterCard.objects.filter(address=address)
            if self.instance and self.instance.pk:
                existing_query = existing_query.exclude(pk=self.instance.pk)

            if existing_query.exists():
                existing_record = existing_query.first()
                raise forms.ValidationError(f'住址"{address}"已被租户"{existing_record.name}"使用，请输入不同的住址')

        return address


class TenantWaterCardSearchForm(forms.Form):
    """租户售水机卡号记录搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索租户姓名、住址、电话、卡号...'
        })
    )


class TenantWaterCardImportForm(forms.Form):
    """租户售水机卡号记录批量导入表单"""
    file = forms.FileField(
        label='Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls'
        }),
        help_text='支持.xlsx和.xls格式，文件大小不超过5MB'
    )

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # 检查文件扩展名
            if not file.name.lower().endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('请上传Excel文件（.xlsx或.xls格式）')

            # 检查文件大小（限制为5MB）
            if file.size > 5 * 1024 * 1024:
                raise forms.ValidationError('文件大小不能超过5MB')

        return file


class TenantWaterCardLostForm(forms.Form):
    """租户售水机水卡挂失表单"""
    lost_card_number = forms.CharField(
        label='挂失卡号',
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入要挂失的卡号'
        }),
        help_text='请输入需要挂失的水卡号码'
    )

    new_card_number = forms.CharField(
        label='新卡号',
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入新的水卡号码（可选）'
        }),
        help_text='如果有新卡号，请输入新的水卡号码'
    )

    def __init__(self, *args, **kwargs):
        self.tenant_card = kwargs.pop('tenant_card', None)
        super().__init__(*args, **kwargs)

    def clean_lost_card_number(self):
        """验证挂失卡号"""
        lost_card_number = self.cleaned_data.get('lost_card_number')

        if not lost_card_number:
            raise forms.ValidationError('请输入要挂失的卡号')

        if self.tenant_card:
            # 检查卡号是否属于该租户
            if (self.tenant_card.normal_card_number != lost_card_number and
                lost_card_number not in self.tenant_card.get_lost_card_list()):
                raise forms.ValidationError('该卡号不属于当前租户')

            # 检查是否已经挂失
            if lost_card_number in self.tenant_card.get_lost_card_list():
                raise forms.ValidationError('该卡号已经挂失')

        return lost_card_number

    def clean_new_card_number(self):
        """验证新卡号"""
        new_card_number = self.cleaned_data.get('new_card_number')

        if new_card_number:
            # 检查新卡号是否已被其他租户使用
            existing_record = TenantWaterCard.objects.filter(
                normal_card_number=new_card_number
            ).first()

            if existing_record and existing_record != self.tenant_card:
                raise forms.ValidationError(f'卡号 {new_card_number} 已被其他租户使用')

        return new_card_number
