from django.core.management.base import BaseCommand
from decimal import Decimal
from commercial_properties.models import CommercialProperty


class Command(BaseCommand):
    help = '更新所有商品房的物业费总额，确保数据一致性'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示需要更新的记录，不实际更新',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(self.style.SUCCESS('开始检查商品房物业费总额...'))
        
        inconsistent_count = 0
        total_count = 0
        updated_records = []

        for property in CommercialProperty.objects.all():
            total_count += 1
            calculated_fee = property.calculate_property_fee()
            stored_fee = property.property_fee_total
            
            # 检查是否有差异（允许1分钱的误差）
            if abs(calculated_fee - stored_fee) > Decimal('0.01'):
                inconsistent_count += 1
                
                record_info = {
                    'property': property,
                    'stored_fee': stored_fee,
                    'calculated_fee': calculated_fee,
                    'difference': calculated_fee - stored_fee
                }
                updated_records.append(record_info)
                
                self.stdout.write(
                    f'发现不一致: {property.building_number}-{property.room_number} '
                    f'存储值: {stored_fee} 计算值: {calculated_fee} '
                    f'差异: {calculated_fee - stored_fee}'
                )
                
                if not dry_run:
                    # 更新为正确的值
                    property.property_fee_total = calculated_fee
                    property.save()

        # 输出统计信息
        self.stdout.write(f'\n总共检查了 {total_count} 条记录')
        self.stdout.write(f'发现 {inconsistent_count} 条不一致的记录')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('这是试运行，没有实际更新数据'))
            if inconsistent_count > 0:
                self.stdout.write('要实际更新数据，请运行: python manage.py update_property_fees')
        else:
            if inconsistent_count > 0:
                self.stdout.write(self.style.SUCCESS(f'已成功更新 {inconsistent_count} 条记录'))
            else:
                self.stdout.write(self.style.SUCCESS('所有记录都是一致的，无需更新'))
