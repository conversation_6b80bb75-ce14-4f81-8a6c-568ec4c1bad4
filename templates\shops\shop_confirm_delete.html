{% extends 'base/base.html' %}
{% load static %}

{% block title %}删除商铺 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>确认删除商铺
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>警告
                        </h5>
                        <p class="mb-0">您确定要删除以下商铺吗？此操作不可撤销！</p>
                    </div>
                    
                    <!-- 商铺信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-store me-2"></i>商铺信息
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>商铺号：</strong>{{ object.shop_number }}</p>
                                            <p><strong>租户姓名：</strong>{{ object.tenant_name }}</p>
                                            <p><strong>门牌号：</strong>{{ object.door_number|default:"-" }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>电话：</strong>{{ object.phone|default:"-" }}</p>
                                            <p><strong>面积：</strong>{{ object.area }}㎡</p>
                                            <p><strong>状态：</strong>
                                                <span class="badge 
                                                    {% if object.status == 'active' %}bg-success
                                                    {% elif object.status == 'overdue' %}bg-danger
                                                    {% elif object.status == 'checkout' %}bg-secondary
                                                    {% else %}bg-warning{% endif %}">
                                                    {{ object.get_status_display }}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 删除确认表单 -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="d-flex justify-content-end">
                            {% if request.GET.next %}
                                <a href="{{ request.GET.next }}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            {% elif 'overdue' in request.META.HTTP_REFERER or object.status == 'overdue' %}
                                <a href="{% url 'shops:overdue' %}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            {% elif 'checkout' in request.META.HTTP_REFERER or object.status == 'checkout' %}
                                <a href="{% url 'shops:checkout' %}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            {% else %}
                                <a href="{% url 'shops:list' %}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                            {% endif %}
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>确认删除
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
