# Generated by Django 5.2.3 on 2025-06-19 01:48

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CommercialProperty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('building_number', models.CharField(max_length=10, verbose_name='楼号')),
                ('unit_number', models.CharField(blank=True, max_length=10, verbose_name='单元号')),
                ('room_number', models.CharField(max_length=10, verbose_name='房号')),
                ('area', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='平米数')),
                ('owner_name', models.CharField(max_length=50, verbose_name='业主姓名')),
                ('owner_phone', models.CharField(max_length=20, verbose_name='业主电话')),
                ('floor', models.IntegerField(default=1, verbose_name='楼层')),
                ('has_basement', models.BooleanField(default=False, verbose_name='地下室')),
                ('has_parking', models.BooleanField(default=False, verbose_name='车位')),
                ('property_fee_due_date', models.DateField(verbose_name='物业费到期日期')),
                ('property_fee_total', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='物业费总额')),
                ('status', models.CharField(choices=[('active', '正常'), ('overdue', '逾期')], default='active', max_length=10, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '商品房',
                'verbose_name_plural': '商品房',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommercialPropertyPaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='交费时间')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='缴费金额')),
                ('fee_start_date', models.DateField(verbose_name='物业费开始时间')),
                ('fee_end_date', models.DateField(verbose_name='物业费结束时间')),
                ('payment_method', models.CharField(default='现金', max_length=20, verbose_name='缴费方式')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='commercial_properties.commercialproperty', verbose_name='商品房')),
            ],
            options={
                'verbose_name': '商品房缴费记录',
                'verbose_name_plural': '商品房缴费记录',
                'ordering': ['-payment_date'],
            },
        ),
    ]
