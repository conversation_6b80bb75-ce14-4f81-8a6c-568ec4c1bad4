# run_waitress.py - 优化版本（静态文件加速）
import os
import socket

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'property_management.settings')

from waitress import serve
from property_management.wsgi import application

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

if __name__ == '__main__':
    local_ip = get_local_ip()
    port = 8000

    print('=' * 60)
    print('东悦物业管理系统 - Waitress服务器（静态文件优化版）')
    print('=' * 60)
    print(f'本机访问: http://localhost:{port}')
    print(f'本机访问: http://127.0.0.1:{port}')
    print(f'局域网访问: http://{local_ip}:{port}')
    print('=' * 60)
    print('优化特性: 静态文件缓存 + Gzip压缩 + 多线程')
    print('按 Ctrl+C 停止服务器')
    print()

    try:
        serve(
            application,
            host='0.0.0.0',
            port=port,
            threads=16,                    # 增加线程数处理并发请求
            connection_limit=200,          # 连接限制
            cleanup_interval=10,           # 清理间隔
            channel_timeout=300,           # 通道超时
            log_socket_errors=False,       # 减少日志输出
            send_bytes=65536,              # 发送缓冲区（64KB）
            recv_bytes=65536,              # 接收缓冲区（64KB）
            expose_tracebacks=False,       # 生产环境关闭
            asyncore_use_poll=True,        # 使用poll而不是select
            # 静态文件优化
            max_request_header_size=262144, # 256KB请求头
            max_request_body_size=1073741824, # 1GB请求体
        )
    except KeyboardInterrupt:
        print('\n服务器已停止')
    except Exception as e:
        print(f'服务器启动失败: {e}')