from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Sum
from django.utils import timezone
from django.db import connection
from datetime import datetime, timedelta
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from openpyxl import Workbook

from .models import OtherIncomeRecord
from .forms import OtherIncomeRecordForm, OtherIncomeSearchForm


def ensure_tables_exist():
    """确保数据库表存在"""
    with connection.cursor() as cursor:
        try:
            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'other_income_otherincomerecord'")
            if not cursor.fetchone():
                # 创建其它收入记录表
                cursor.execute("""
                    CREATE TABLE `other_income_otherincomerecord` (
                        `id` bigint NOT NULL AUTO_INCREMENT,
                        `income_source` varchar(50) NOT NULL,
                        `amount` decimal(10,2) NOT NULL,
                        `income_date` date NOT NULL,
                        `notes` longtext,
                        `water_fee` decimal(10,2) DEFAULT NULL,
                        `storage_electricity_fee` decimal(10,2) DEFAULT NULL,
                        `water_purifier_fee` decimal(10,2) DEFAULT NULL,
                        `water_card_fee` decimal(10,2) DEFAULT NULL,
                        `water_bucket_fee` decimal(10,2) DEFAULT NULL,
                        `shop_water_fee` decimal(10,2) DEFAULT NULL,
                        `water_card_supplement_fee` decimal(10,2) DEFAULT NULL,
                        `water_purifier_card_fee` decimal(10,2) DEFAULT NULL,
                        `parking_fee` decimal(10,2) DEFAULT NULL,
                        `elevator_card_fee` decimal(10,2) DEFAULT NULL,
                        `created_at` datetime(6) NOT NULL,
                        `updated_at` datetime(6) NOT NULL,
                        PRIMARY KEY (`id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)

                # 添加迁移记录
                cursor.execute("""
                    INSERT IGNORE INTO `django_migrations` (`app`, `name`, `applied`)
                    VALUES ('other_income', '0001_initial', NOW())
                """)
        except Exception as e:
            print(f"创建表时出错: {e}")


class OtherIncomeListView(LoginRequiredMixin, ListView):
    """其它收入流水列表视图"""
    model = OtherIncomeRecord
    template_name = 'other_income/list.html'
    context_object_name = 'records'
    paginate_by = 15

    def dispatch(self, request, *args, **kwargs):
        # 确保数据库表存在
        ensure_tables_exist()
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = OtherIncomeRecord.objects.all()

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(Q(notes__icontains=search))

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(income_date__gte=start_date)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(income_date__lte=end_date)
            except ValueError:
                pass

        return queryset.order_by('-income_date', '-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 搜索表单
        from .forms import OtherIncomeSearchForm
        context['search_form'] = OtherIncomeSearchForm(self.request.GET)

        # 数据看板统计
        today = timezone.now().date()
        current_month_start = today.replace(day=1)
        last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
        last_month_end = current_month_start - timedelta(days=1)

        # 应用相同的筛选条件
        base_queryset = self.get_queryset()

        # 今日金额
        today_amount = base_queryset.filter(
            income_date=today
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # 本月金额
        current_month_amount = base_queryset.filter(
            income_date__gte=current_month_start,
            income_date__lte=today
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # 上月金额
        last_month_amount = base_queryset.filter(
            income_date__gte=last_month_start,
            income_date__lte=last_month_end
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        # 总金额
        total_amount = base_queryset.aggregate(
            total=Sum('amount')
        )['total'] or Decimal('0.00')

        context.update({
            'today_amount': today_amount,
            'current_month_amount': current_month_amount,
            'last_month_amount': last_month_amount,
            'total_amount': total_amount,
        })

        return context


class OtherIncomeCreateView(LoginRequiredMixin, CreateView):
    """新增其它收入记录视图"""
    model = OtherIncomeRecord
    form_class = OtherIncomeRecordForm
    template_name = 'other_income/create.html'
    success_url = reverse_lazy('other_income:list')

    def form_valid(self, form):
        messages.success(self.request, '收入记录添加成功！')
        return super().form_valid(form)


class OtherIncomeUpdateView(LoginRequiredMixin, UpdateView):
    """编辑其它收入记录视图"""
    model = OtherIncomeRecord
    form_class = OtherIncomeRecordForm
    template_name = 'other_income/edit.html'
    success_url = reverse_lazy('other_income:list')

    def form_valid(self, form):
        messages.success(self.request, '收入记录更新成功！')
        return super().form_valid(form)


class OtherIncomeDeleteView(LoginRequiredMixin, DeleteView):
    """删除其它收入记录视图"""
    model = OtherIncomeRecord
    template_name = 'other_income/delete.html'
    success_url = reverse_lazy('other_income:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '收入记录删除成功！')
        return super().delete(request, *args, **kwargs)


class ExportOtherIncomeView(LoginRequiredMixin, View):
    """导出其它收入流水"""
    
    def get(self, request):
        try:
            # 获取筛选条件
            search = request.GET.get('search')
            start_date = request.GET.get('start_date')
            end_date = request.GET.get('end_date')
            
            # 构建查询
            records = OtherIncomeRecord.objects.all()
            
            if search:
                records = records.filter(Q(notes__icontains=search))
            
            if start_date:
                try:
                    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                    records = records.filter(income_date__gte=start_date_obj)
                except ValueError:
                    pass
            
            if end_date:
                try:
                    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                    records = records.filter(income_date__lte=end_date_obj)
                except ValueError:
                    pass
            
            records = records.order_by('-income_date', '-created_at')
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "其它收入流水"
            
            # 设置表头
            headers = [
                '收入日期', '总金额', '水费', '储藏间电费', '净水机费',
                '净水卡费', '水桶费', '商铺水费', '补自来水卡费', '补净水机卡费',
                '停车场收费', '电梯梯控卡费', '自来水表电池费', '其它费用', '备注'
            ]
            
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
            
            # 写入数据
            for row, record in enumerate(records, 2):
                ws.cell(row=row, column=1, value=record.income_date.strftime('%Y-%m-%d'))
                ws.cell(row=row, column=2, value=float(record.amount))
                ws.cell(row=row, column=3, value=float(record.water_fee) if record.water_fee else 0)
                ws.cell(row=row, column=4, value=float(record.storage_electricity_fee) if record.storage_electricity_fee else 0)
                ws.cell(row=row, column=5, value=float(record.water_purifier_fee) if record.water_purifier_fee else 0)
                ws.cell(row=row, column=6, value=float(record.water_card_fee) if record.water_card_fee else 0)
                ws.cell(row=row, column=7, value=float(record.water_bucket_fee) if record.water_bucket_fee else 0)
                ws.cell(row=row, column=8, value=float(record.shop_water_fee) if record.shop_water_fee else 0)
                ws.cell(row=row, column=9, value=float(record.water_card_supplement_fee) if record.water_card_supplement_fee else 0)
                ws.cell(row=row, column=10, value=float(record.water_purifier_card_fee) if record.water_purifier_card_fee else 0)
                ws.cell(row=row, column=11, value=float(record.parking_fee) if record.parking_fee else 0)
                ws.cell(row=row, column=12, value=float(record.elevator_card_fee) if record.elevator_card_fee else 0)
                ws.cell(row=row, column=13, value=float(record.water_meter_battery_fee) if record.water_meter_battery_fee else 0)
                ws.cell(row=row, column=14, value=float(record.other_fee) if record.other_fee else 0)
                ws.cell(row=row, column=15, value=record.notes or '')
            
            # 调整列宽
            column_widths = [12, 10, 8, 12, 10, 10, 8, 12, 15, 15, 12, 15, 15, 10, 20]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[get_column_letter(i)].width = width
            
            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
            # 生成文件名
            filename = "其它收入流水"
            if start_date and end_date:
                filename += f"_{start_date}至{end_date}"
            elif start_date:
                filename += f"_{start_date}起"
            elif end_date:
                filename += f"_至{end_date}"
            filename += f"_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            # 保存工作簿
            wb.save(response)
            return response
            
        except Exception as e:
            messages.error(request, f'导出失败：{str(e)}')
            return redirect('other_income:list')
