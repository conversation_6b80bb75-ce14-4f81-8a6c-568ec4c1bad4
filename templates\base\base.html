{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}东悦物业管理系统{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">
    <link rel="apple-touch-icon" href="{% static 'img/favicon.png' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard:index' %}">
                <i class="fas fa-building"></i> 东悦物业管理系统
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i> 租客管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'tenants:list' %}">
                                <i class="fas fa-list"></i> 租客列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'tenants:overdue' %}">
                                <i class="fas fa-exclamation-triangle"></i> 物业费到期列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'tenants:checkout' %}">
                                <i class="fas fa-sign-out-alt"></i> 租客退房列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'tenants:payment_history' %}">
                                <i class="fas fa-chart-line"></i> 物业费流水统计
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-home"></i> 商品房管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'commercial_properties:list' %}">
                                <i class="fas fa-building"></i> 商品房管理
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'commercial_properties:overdue' %}">
                                <i class="fas fa-clock"></i> 物业费到期列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'commercial_properties:payment_history' %}">
                                <i class="fas fa-money-bill-wave"></i> 物业费流水统计
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-store"></i> 商铺管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'shops:list' %}">
                                <i class="fas fa-store-alt"></i> 商铺租房管理
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'shops:overdue' %}">
                                <i class="fas fa-hourglass-half"></i> 商铺物业费到期列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'shops:checkout' %}">
                                <i class="fas fa-door-open"></i> 商铺退房列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'shops:payment_history' %}">
                                <i class="fas fa-receipt"></i> 商铺物业费流水
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-car"></i> 车位管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'parking:list' %}">
                                <i class="fas fa-parking"></i> 地下车位出租列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'parking:overdue' %}">
                                <i class="fas fa-calendar-times"></i> 车位到期列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'parking:checkout' %}">
                                <i class="fas fa-car-side"></i> 退车位列表
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'parking:payment_history' %}">
                                <i class="fas fa-file-invoice-dollar"></i> 车位物业费流水
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'other_income:list' %}">
                            <i class="fas fa-coins"></i> 其它收入管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tint"></i> 售水机管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'water_machine:index' %}">
                                <i class="fas fa-home"></i> 管理首页
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'water_machine:village_customer_list' %}">
                                <i class="fas fa-users"></i> 村售水机充值系统
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'water_machine:tenant_water_card_list' %}">
                                <i class="fas fa-id-card"></i> 租户售水机卡号记录
                            </a></li>
                        </ul>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'logout' %}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="{% if user.is_authenticated %}container-fluid main-content{% endif %}">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
