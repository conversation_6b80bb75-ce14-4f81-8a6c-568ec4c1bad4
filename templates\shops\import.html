{% extends 'base/base.html' %}
{% load static %}

{% block title %}批量导入商铺 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-upload me-2"></i>批量导入商铺
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 导入说明 -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>导入说明
                        </h6>
                        <ul class="mb-0">
                            <li>请使用Excel文件(.xlsx或.xls格式)</li>
                            <li>第一行为表头，从第二行开始为数据</li>
                            <li>表头顺序：商铺号、门牌号、租户或业主姓名、身份证号（可选）、租户电话、平米数、业主姓名（可选）、业主电话（可选）、物业费开始时间、物业费到期时间</li>
                            <li>日期格式：YYYY-MM-DD（如：2024-01-01）</li>
                            <li>租户电话为必填项，可输入多个电话号码，用逗号分隔</li>
                            <li>业主姓名和业主电话为可选字段，可以留空</li>
                            <li><strong>平米数支持小数</strong>（如：100.5、120.8等）</li>
                            <li>物业费将根据平米数自动计算（每平米1.5元）</li>
                        </ul>
                    </div>
                    
                    <!-- 模板下载 -->
                    <div class="alert alert-light">
                        <h6 class="alert-heading">
                            <i class="fas fa-download me-2"></i>模板下载
                        </h6>
                        <p class="mb-2">建议先下载模板文件，按照格式填写数据后再导入：</p>
                        <a href="{% url 'shops:download_template' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-file-excel me-1"></i>下载Excel模板
                        </a>
                    </div>
                    
                    <!-- 导入表单 -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.file.id_for_label }}" class="form-label">
                                <i class="fas fa-file me-1"></i>选择Excel文件 <span class="text-danger">*</span>
                            </label>
                            {{ form.file }}
                            {% if form.file.errors %}
                                <div class="text-danger small">{{ form.file.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">支持.xlsx和.xls格式的Excel文件</div>
                        </div>
                        
                        <!-- 导入选项 -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" checked>
                                <label class="form-check-label" for="skip_duplicates">
                                    跳过重复记录（根据商铺号判断）
                                </label>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'shops:list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i>开始导入
                            </button>
                        </div>
                    </form>
                    
                    <!-- 示例数据 -->
                    <div class="mt-4">
                        <h6>
                            <i class="fas fa-table me-2"></i>数据格式示例
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>商铺号</th>
                                        <th>门牌号</th>
                                        <th>租户或业主姓名</th>
                                        <th>身份证号（可选）</th>
                                        <th>租户电话</th>
                                        <th>平米数</th>
                                        <th>物业费开始时间</th>
                                        <th>物业费到期时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>S001</td>
                                        <td>顺安路555-1</td>
                                        <td>张三</td>
                                        <td>110101199001011234</td>
                                        <td>13800138001</td>
                                        <td>100.5</td>
                                        <td>2024-01-01</td>
                                        <td>2024-12-31</td>
                                    </tr>
                                    <tr>
                                        <td>S002</td>
                                        <td>顺安路555-3</td>
                                        <td>李四</td>
                                        <td>110101199002021234</td>
                                        <td>13800138002</td>
                                        <td>120.8</td>
                                        <td>2024-02-01</td>
                                        <td>2025-01-31</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('id_file');
        const form = document.querySelector('form');
        
        // 文件选择验证
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const fileName = file.name.toLowerCase();
                if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                    alert('请选择Excel文件(.xlsx或.xls格式)');
                    this.value = '';
                    return;
                }
                
                // 检查文件大小（限制10MB）
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    this.value = '';
                    return;
                }
            }
        });
        
        // 表单提交时显示加载状态
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>导入中...';
            
            // 如果导入失败，恢复按钮状态
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }, 30000);
        });
    });
</script>
{% endblock %}
