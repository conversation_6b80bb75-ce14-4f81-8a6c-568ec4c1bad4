{% extends 'base/base.html' %}
{% load static %}

{% block title %}水卡管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-credit-card text-success me-2"></i>
            水卡管理
        </h2>
        <div>
            <a href="{% url 'water_machine:index' %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>返回首页
            </a>
            <a href="{% url 'water_machine:water_card_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>新增水卡
            </a>
        </div>
    </div>

    <!-- 搜索表单 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        {{ search_form.search }}
                    </div>
                </div>
                <div class="col-md-3">
                    {{ search_form.status }}
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{% url 'water_machine:water_card_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i>重置
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 水卡列表 -->
    {% if water_cards %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>卡号</th>
                                <th>客户姓名</th>
                                <th>客户电话</th>
                                <th>状态</th>
                                <th>总充值金额</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for water_card in water_cards %}
                            <tr>
                                <td>
                                    <strong>{{ water_card.card_number }}</strong>
                                </td>
                                <td>{{ water_card.customer.name }}</td>
                                <td>{{ water_card.customer.phone }}</td>
                                <td>
                                    {% if water_card.status == 'normal' %}
                                        <span class="badge bg-success">正常</span>
                                    {% else %}
                                        <span class="badge bg-danger">丢失</span>
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    <strong class="text-success">¥{{ water_card.get_total_recharge_amount|floatformat:2 }}</strong>
                                </td>
                                <td>{{ water_card.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if water_card.status == 'normal' %}
                                            <form method="post" action="{% url 'water_machine:water_card_lost' water_card.pk %}" style="display: inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-outline-warning" title="标记丢失" 
                                                        onclick="return confirm('确定要标记此水卡为丢失状态吗？')">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </button>
                                            </form>
                                        {% else %}
                                            <span class="badge bg-secondary">已丢失</span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if is_paginated %}
                <nav aria-label="分页导航" class="mt-3">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无水卡信息</h5>
                <p class="text-muted">点击上方"新增水卡"按钮添加第一张水卡</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }
</style>
{% endblock %}
