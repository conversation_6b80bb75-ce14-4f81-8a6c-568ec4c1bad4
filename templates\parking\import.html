{% extends 'base/base.html' %}

{% block title %}导入车位数据{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>导入车位数据
                    </h5>
                    <a href="{% url 'parking:list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- 导入说明 -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>导入说明</h6>
                        <ul class="mb-0">
                            <li>请先下载导入模板，按照模板格式填写数据</li>
                            <li>支持 .xlsx 和 .xls 格式的Excel文件</li>
                            <li>表头格式：车位号、房号、租户姓名、电话、车牌号、车位所有者、所有者电话、租车位时间、物业费到期时间</li>
                            <li>电话号码为必填项，车位所有者信息为可选项</li>
                            <li>日期格式：YYYY-MM-DD（如：2024-01-01）</li>
                            <li>房号格式：3-1001 或 9-1-1001</li>
                        </ul>
                    </div>

                    <!-- 下载模板 -->
                    <div class="mb-4">
                        <a href="{% url 'parking:download_template' %}" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>下载导入模板
                        </a>
                    </div>

                    <!-- 导入表单 -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.file.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-excel me-1"></i>选择Excel文件 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.file }}
                                    {% if form.file.errors %}
                                        <div class="text-danger small">{{ form.file.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check">
                                        {{ form.skip_duplicates }}
                                        <label class="form-check-label" for="{{ form.skip_duplicates.id_for_label }}">
                                            跳过重复记录
                                        </label>
                                    </div>
                                    <small class="text-muted">勾选后将跳过车位号重复的记录</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'parking:list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>开始导入
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .alert-info {
        border-left: 4px solid #17a2b8;
    }
    
    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 文件选择验证
        const fileInput = document.getElementById('{{ form.file.id_for_label }}');
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(e) {
            if (!fileInput.files.length) {
                alert('请选择要导入的Excel文件');
                e.preventDefault();
                return;
            }
            
            const file = fileInput.files[0];
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                alert('请选择Excel文件（.xlsx 或 .xls 格式）');
                e.preventDefault();
                return;
            }
            
            // 显示加载提示
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>正在导入...';
            submitBtn.disabled = true;
        });
        
        // 文件选择时显示文件名
        fileInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                console.log('选择的文件：', fileName);
            }
        });
    });
</script>
{% endblock %}
