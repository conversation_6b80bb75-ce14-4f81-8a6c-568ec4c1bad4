from django import forms
from django.utils import timezone
from .models import ParkingSpace, ParkingPaymentHistory


class ParkingSpaceForm(forms.ModelForm):
    """车位表单"""

    # 车牌号省份选择
    PROVINCE_CHOICES = [
        ('', '请选择省份'),
        ('京', '京'),
        ('津', '津'),
        ('沪', '沪'),
        ('渝', '渝'),
        ('冀', '冀'),
        ('豫', '豫'),
        ('云', '云'),
        ('辽', '辽'),
        ('黑', '黑'),
        ('湘', '湘'),
        ('皖', '皖'),
        ('鲁', '鲁'),
        ('新', '新'),
        ('苏', '苏'),
        ('浙', '浙'),
        ('赣', '赣'),
        ('鄂', '鄂'),
        ('桂', '桂'),
        ('甘', '甘'),
        ('晋', '晋'),
        ('蒙', '蒙'),
        ('陕', '陕'),
        ('吉', '吉'),
        ('闽', '闽'),
        ('贵', '贵'),
        ('粤', '粤'),
        ('青', '青'),
        ('藏', '藏'),
        ('川', '川'),
        ('宁', '宁'),
        ('琼', '琼'),
    ]

    license_plate_province = forms.ChoiceField(
        choices=PROVINCE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'license_plate_province'
        })
    )

    license_plate_number = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入车牌号码（如：A12345）',
            'id': 'license_plate_number',
            'style': 'text-transform: uppercase;'
        })
    )

    class Meta:
        model = ParkingSpace
        fields = [
            'room_number', 'tenant_name', 'phone', 'parking_number',
            'owner_name', 'owner_phone',
            'lease_start_date', 'property_fee_due_date'
        ]
        widgets = {
            'room_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入房号（如：3-1001 或 9-1-1001）'
            }),
            'tenant_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入租户姓名'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入电话号码'
            }),
            'parking_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入车位号',
                'style': 'text-transform: uppercase;'
            }),

            'owner_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入车位所有者姓名'
            }),
            'owner_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入所有者电话号码'
            }),
            'lease_start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'property_fee_due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 如果是编辑模式且有实例数据，设置日期字段的初始值
        if self.instance and self.instance.pk:
            if self.instance.lease_start_date:
                self.fields['lease_start_date'].initial = self.instance.lease_start_date
                self.fields['lease_start_date'].widget.attrs['value'] = self.instance.lease_start_date.strftime('%Y-%m-%d')
            if self.instance.property_fee_due_date:
                self.fields['property_fee_due_date'].initial = self.instance.property_fee_due_date
                self.fields['property_fee_due_date'].widget.attrs['value'] = self.instance.property_fee_due_date.strftime('%Y-%m-%d')

            # 处理车牌号分解显示
            if self.instance.license_plate:
                license_plate = self.instance.license_plate
                # 提取省份（第一个字符）
                if len(license_plate) > 0:
                    province = license_plate[0]
                    number = license_plate[1:] if len(license_plate) > 1 else ''

                    self.fields['license_plate_province'].initial = province
                    self.fields['license_plate_number'].initial = number
                    self.fields['license_plate_number'].widget.attrs['value'] = number
        else:
            # 新增模式，设置默认值为当前日期
            from django.utils import timezone
            today = timezone.now().date()
            self.fields['lease_start_date'].initial = today
            self.fields['lease_start_date'].widget.attrs['value'] = today.strftime('%Y-%m-%d')
            self.fields['property_fee_due_date'].initial = today
            self.fields['property_fee_due_date'].widget.attrs['value'] = today.strftime('%Y-%m-%d')



    def clean_phone(self):
        phone = self.cleaned_data['phone']
        if not phone:
            raise forms.ValidationError('电话号码不能为空')
        return phone

    def clean_parking_number(self):
        parking_number = self.cleaned_data.get('parking_number', '')
        # 自动转换为大写
        parking_number = parking_number.upper()

        # 检查车位号是否重复（排除当前编辑的记录）
        queryset = ParkingSpace.objects.filter(parking_number=parking_number)
        if self.instance.pk:
            queryset = queryset.exclude(pk=self.instance.pk)

        # 只检查非退车位状态的记录
        queryset = queryset.exclude(status='checkout')

        if queryset.exists():
            raise forms.ValidationError('该车位号已存在，请检查输入')

        return parking_number

    def clean(self):
        cleaned_data = super().clean()

        # 处理车牌号组合
        province = cleaned_data.get('license_plate_province')
        number = cleaned_data.get('license_plate_number')

        if province and number:
            # 组合完整车牌号并转换为大写
            full_license_plate = f"{province}{number.upper()}"
            cleaned_data['license_plate'] = full_license_plate
        elif province or number:
            # 如果只填了一部分，提示用户
            if not province:
                self.add_error('license_plate_province', '请选择车牌省份')
            if not number:
                self.add_error('license_plate_number', '请输入车牌号码')
        else:
            # 都没填写，设置为空
            cleaned_data['license_plate'] = ''

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)

        # 设置车牌号
        if 'license_plate' in self.cleaned_data:
            instance.license_plate = self.cleaned_data['license_plate']

        if commit:
            instance.save()
        return instance


class ParkingSearchForm(forms.Form):
    """车位搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索车位号、租户姓名、车牌号...'
        })
    )


class ParkingRenewForm(forms.Form):
    """车位续费表单"""
    months = forms.IntegerField(
        label='续费月数',
        min_value=1,
        max_value=24,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入续费月数'
        })
    )
    
    payment_method = forms.ChoiceField(
        label='缴费方式',
        choices=[
            ('现金', '现金'),
            ('转账', '转账'),
            ('微信', '微信'),
            ('支付宝', '支付宝'),
        ],
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    notes = forms.CharField(
        label='备注',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '请输入备注信息（可选）'
        })
    )


class ParkingPaymentHistorySearchForm(forms.Form):
    """车位缴费流水搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索车位号、租户姓名...'
        })
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class ParkingImportForm(forms.Form):
    """车位导入表单"""
    file = forms.FileField(
        label='选择Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls'
        })
    )
    
    skip_duplicates = forms.BooleanField(
        label='跳过重复记录',
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )
