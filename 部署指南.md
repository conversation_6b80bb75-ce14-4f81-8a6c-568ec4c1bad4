# 东悦物业管理系统 - 部署指南

## 🚀 生产环境部署

### 环境要求

#### 服务器配置
- **操作系统**：Ubuntu 20.04 LTS 或 CentOS 8+
- **内存**：最低 2GB，推荐 4GB+
- **存储**：最低 20GB，推荐 50GB+
- **CPU**：最低 2核，推荐 4核+

#### 软件要求
- **Python**：3.8+
- **MySQL**：8.0+
- **Nginx**：1.18+
- **Redis**：6.0+（可选，用于缓存）

### 1. 系统准备

#### Ubuntu系统
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y python3 python3-pip python3-venv
sudo apt install -y mysql-server nginx
sudo apt install -y git curl wget

# 安装Python开发包
sudo apt install -y python3-dev default-libmysqlclient-dev build-essential
```

#### CentOS系统
```bash
# 更新系统
sudo yum update -y

# 安装必要软件
sudo yum install -y python3 python3-pip
sudo yum install -y mysql-server nginx
sudo yum install -y git curl wget

# 安装开发工具
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python3-devel mysql-devel
```

### 2. 数据库配置

#### 安装和配置MySQL
```bash
# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE dywy_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'dywy_user'@'localhost' IDENTIFIED BY 'your_strong_password';

-- 授权
GRANT ALL PRIVILEGES ON dywy_production.* TO 'dywy_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 3. 应用部署

#### 创建应用目录
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash dywy
sudo usermod -aG sudo dywy

# 切换到应用用户
sudo su - dywy

# 创建应用目录
mkdir -p /home/<USER>/apps
cd /home/<USER>/apps
```

#### 部署应用代码
```bash
# 克隆代码
git clone <项目地址> dywy
cd dywy

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 配置生产环境设置
```bash
# 创建生产环境配置文件
cp property_management/settings.py property_management/settings_production.py
```

编辑 `property_management/settings_production.py`：

```python
import os
from .settings import *

# 生产环境配置
DEBUG = False
ALLOWED_HOSTS = ['your-domain.com', 'your-server-ip']

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dywy_production',
        'USER': 'dywy_user',
        'PASSWORD': 'your_strong_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}

# 静态文件配置
STATIC_ROOT = '/home/<USER>/apps/dywy/staticfiles'
MEDIA_ROOT = '/home/<USER>/apps/dywy/media'

# 安全配置
SECRET_KEY = 'your-new-secret-key'
SECURE_SSL_REDIRECT = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# 会话安全
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/home/<USER>/apps/dywy/logs/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

#### 数据库迁移和静态文件
```bash
# 设置环境变量
export DJANGO_SETTINGS_MODULE=property_management.settings_production

# 创建日志目录
mkdir -p logs

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic --noinput
```

### 4. Gunicorn配置

#### 安装Gunicorn
```bash
pip install gunicorn
```

#### 创建Gunicorn配置文件
```bash
# 创建配置文件
nano /home/<USER>/apps/dywy/gunicorn.conf.py
```

```python
# gunicorn.conf.py
import multiprocessing

# 服务器配置
bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程配置
user = "dywy"
group = "dywy"
daemon = False
pidfile = "/home/<USER>/apps/dywy/gunicorn.pid"

# 日志配置
accesslog = "/home/<USER>/apps/dywy/logs/gunicorn_access.log"
errorlog = "/home/<USER>/apps/dywy/logs/gunicorn_error.log"
loglevel = "info"

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190
```

#### 创建systemd服务文件
```bash
sudo nano /etc/systemd/system/dywy.service
```

```ini
[Unit]
Description=Dywy Property Management System
After=network.target

[Service]
Type=notify
User=dywy
Group=dywy
RuntimeDirectory=dywy
WorkingDirectory=/home/<USER>/apps/dywy
Environment=DJANGO_SETTINGS_MODULE=property_management.settings_production
ExecStart=/home/<USER>/apps/dywy/venv/bin/gunicorn property_management.wsgi:application -c /home/<USER>/apps/dywy/gunicorn.conf.py
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
# 重新加载systemd
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start dywy
sudo systemctl enable dywy

# 检查状态
sudo systemctl status dywy
```

### 5. Nginx配置

#### 创建Nginx配置文件
```bash
sudo nano /etc/nginx/sites-available/dywy
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 静态文件
    location /static/ {
        alias /home/<USER>/apps/dywy/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 媒体文件
    location /media/ {
        alias /home/<USER>/apps/dywy/media/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 应用代理
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
}
```

#### 启用站点
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/dywy /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 6. SSL证书配置

#### 使用Let's Encrypt（免费）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 监控和维护

#### 日志监控
```bash
# 查看应用日志
tail -f /home/<USER>/apps/dywy/logs/django.log
tail -f /home/<USER>/apps/dywy/logs/gunicorn_error.log

# 查看系统日志
sudo journalctl -u dywy -f
sudo journalctl -u nginx -f
```

#### 数据备份
```bash
# 创建备份脚本
nano /home/<USER>/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u dywy_user -p'your_password' dywy_production > $BACKUP_DIR/db_backup_$DATE.sql

# 媒体文件备份
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz /home/<USER>/apps/dywy/media/

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
# 设置执行权限
chmod +x /home/<USER>/backup.sh

# 添加到定时任务
crontab -e
# 每天凌晨2点备份
0 2 * * * /home/<USER>/backup.sh
```

### 8. 性能优化

#### 数据库优化
```sql
-- MySQL配置优化
-- 编辑 /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
query_cache_type = 1
```

#### 应用优化
```python
# 在settings_production.py中添加缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}

# 启用缓存中间件
MIDDLEWARE = [
    'django.middleware.cache.UpdateCacheMiddleware',
    # ... 其他中间件
    'django.middleware.cache.FetchFromCacheMiddleware',
]
```

### 9. 安全检查清单

- [ ] 修改默认密码
- [ ] 配置防火墙
- [ ] 启用SSL证书
- [ ] 设置安全头
- [ ] 配置日志监控
- [ ] 定期备份数据
- [ ] 更新系统补丁
- [ ] 限制文件上传大小
- [ ] 配置访问控制

### 10. 故障排除

#### 常见问题
1. **502 Bad Gateway**：检查Gunicorn服务状态
2. **静态文件404**：检查Nginx静态文件配置
3. **数据库连接失败**：检查数据库服务和配置
4. **SSL证书错误**：检查证书有效性和配置

#### 调试命令
```bash
# 检查服务状态
sudo systemctl status dywy
sudo systemctl status nginx
sudo systemctl status mysql

# 查看端口占用
sudo netstat -tlnp | grep :8000
sudo netstat -tlnp | grep :80

# 测试数据库连接
mysql -u dywy_user -p dywy_production
```

---

**注意**：部署前请仔细阅读所有配置，根据实际环境调整相关参数。建议先在测试环境验证部署流程。
