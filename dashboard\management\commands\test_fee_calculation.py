from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, date
from dateutil.relativedelta import relativedelta


class Command(BaseCommand):
    help = '测试新增租客的物业费计算逻辑'

    def handle(self, *args, **options):
        self.stdout.write('测试新增租客物业费计算逻辑...')
        
        # 测试案例1：2025年6月19日入住，2025年8月18日到期（应该是2个月）
        self.test_fee_calculation(
            date(2025, 6, 19), 
            date(2025, 8, 18), 
            "案例1：2025年6月19日入住，2025年8月18日到期",
            expected_months=2
        )
        
        # 测试案例2：2025年6月19日入住，2025年9月18日到期（应该是3个月）
        self.test_fee_calculation(
            date(2025, 6, 19), 
            date(2025, 9, 18), 
            "案例2：2025年6月19日入住，2025年9月18日到期",
            expected_months=3
        )
        
        # 测试案例3：2025年1月31日入住，2025年3月30日到期（应该是2个月）
        self.test_fee_calculation(
            date(2025, 1, 31), 
            date(2025, 3, 30), 
            "案例3：2025年1月31日入住，2025年3月30日到期",
            expected_months=2
        )
        
        # 测试案例4：2025年1月15日入住，2025年2月14日到期（应该是1个月）
        self.test_fee_calculation(
            date(2025, 1, 15), 
            date(2025, 2, 14), 
            "案例4：2025年1月15日入住，2025年2月14日到期",
            expected_months=1
        )
        
    def test_fee_calculation(self, move_in_date, due_date, description, expected_months):
        self.stdout.write(f"\n{description}")
        self.stdout.write(f"入住时间: {move_in_date}")
        self.stdout.write(f"到期时间: {due_date}")
        
        # 使用与视图相同的计算逻辑
        months_diff = 0
        current_date = move_in_date
        
        while current_date <= due_date:
            months_diff += 1
            # 计算下一个月的同一天
            next_month_date = move_in_date + relativedelta(months=months_diff)
            if next_month_date > due_date:
                break
            current_date = next_month_date
        
        self.stdout.write(f"计算结果: {months_diff}个月")
        self.stdout.write(f"期望结果: {expected_months}个月")
        
        if months_diff == expected_months:
            self.stdout.write(self.style.SUCCESS("✓ 计算正确"))
        else:
            self.stdout.write(self.style.ERROR(f"✗ 计算错误"))
            
        # 显示详细的计算过程
        self.stdout.write("计算过程:")
        current_date = move_in_date
        for i in range(1, months_diff + 1):
            next_date = move_in_date + relativedelta(months=i)
            self.stdout.write(f"  第{i}个月: {move_in_date} + {i}月 = {next_date}")
            if next_date > due_date:
                self.stdout.write(f"    {next_date} > {due_date}，停止计算")
                break
