from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
import openpyxl.utils
from io import BytesIO

from .models import Tenant, TenantPaymentHistory
from .forms import TenantForm, TenantSearchForm, OverdueTenantSearchForm, TenantRenewForm, PaymentHistorySearchForm, TenantImportForm
from utils.date_parser import parse_date


class TenantListView(LoginRequiredMixin, ListView):
    """租客列表视图"""
    model = Tenant
    template_name = 'tenants/list.html'
    context_object_name = 'tenants'
    paginate_by = 15

    def get_queryset(self):
        # 先更新逾期状态
        today = timezone.now().date()
        Tenant.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 只显示正常状态的租客
        queryset = Tenant.objects.filter(status='active')

        # 搜索功能
        search = self.request.GET.get('search')
        search_type = self.request.GET.get('search_type', 'room_number')

        if search:
            if search_type == 'room_number':
                queryset = queryset.filter(room_number__icontains=search)
            elif search_type == 'tenant_name':
                queryset = queryset.filter(tenant_name__icontains=search)
            elif search_type == 'id_card':
                queryset = queryset.filter(id_card__icontains=search)
            elif search_type == 'tenant_phone':
                queryset = queryset.filter(tenant_phone__icontains=search)

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TenantSearchForm(self.request.GET)

        queryset = self.get_queryset()
        total_count = queryset.count()
        context['total_count'] = total_count

        # 计算总人数
        from django.db.models import Sum, Avg
        total_residents = queryset.aggregate(
            total=Sum('resident_count')
        )['total'] or 0
        context['total_residents'] = total_residents

        # 计算平均人数/户
        if total_count > 0:
            context['average_residents'] = total_residents / total_count
        else:
            context['average_residents'] = 0

        # 计算活跃租客数（物业费未到期的）
        from django.utils import timezone
        today = timezone.now().date()
        active_tenants = queryset.filter(property_fee_due_date__gte=today).count()
        context['active_tenants'] = active_tenants

        return context


class TenantCreateView(LoginRequiredMixin, CreateView):
    """租客创建视图"""
    model = Tenant
    form_class = TenantForm
    template_name = 'tenants/form.html'
    success_url = reverse_lazy('tenants:list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # 为新租客创建初始缴费记录
        tenant = self.object

        # 计算从入住时间到物业费到期时间的费用期间
        move_in_date = tenant.move_in_date
        due_date = tenant.property_fee_due_date

        # 使用正确的月数计算逻辑
        from dateutil.relativedelta import relativedelta

        # 计算月数：从入住日期开始，每次加一个月，看需要多少个月才能到达或超过到期日期
        months_diff = 0
        current_date = move_in_date

        while current_date <= due_date:
            months_diff += 1
            # 计算下一个月的同一天
            next_month_date = move_in_date + relativedelta(months=months_diff)
            if next_month_date > due_date:
                break
            current_date = next_month_date

        if months_diff > 0:
            # 计算缴费金额
            total_amount = tenant.property_fee * months_diff

            # 创建缴费记录
            TenantPaymentHistory.objects.create(
                tenant=tenant,
                amount=total_amount,
                fee_start_date=move_in_date,
                fee_end_date=due_date,
                payment_method='现金',
                notes=f'新租客入住缴费{months_diff}个月'
            )

        messages.success(self.request, '租客信息添加成功！')
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增租客'
        return context


class TenantUpdateView(LoginRequiredMixin, UpdateView):
    """租客编辑视图"""
    model = Tenant
    form_class = TenantForm
    template_name = 'tenants/form.html'
    success_url = reverse_lazy('tenants:list')

    def form_valid(self, form):
        messages.success(self.request, '租客信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑租客'
        return context


class TenantDeleteView(LoginRequiredMixin, DeleteView):
    """租客删除视图"""
    model = Tenant
    template_name = 'tenants/confirm_delete.html'

    def get_success_url(self):
        """根据来源页面决定重定向目标"""
        # 检查是否有next参数
        next_url = self.request.GET.get('next')
        if next_url:
            return next_url

        # 检查HTTP_REFERER来判断来源页面
        referer = self.request.META.get('HTTP_REFERER', '')
        if 'overdue' in referer:
            return reverse_lazy('tenants:overdue')
        elif 'checkout' in referer:
            return reverse_lazy('tenants:checkout')
        else:
            return reverse_lazy('tenants:list')

    def delete(self, request, *args, **kwargs):
        tenant_name = self.get_object().tenant_name
        messages.success(request, f'租客 {tenant_name} 删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueTenantListView(LoginRequiredMixin, ListView):
    """逾期租客列表视图"""
    model = Tenant
    template_name = 'tenants/overdue_list.html'
    context_object_name = 'tenants'
    paginate_by = 15

    def get_queryset(self):
        # 更新逾期状态
        today = timezone.now().date()
        Tenant.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态（将已续费的逾期租客状态改回正常）
        Tenant.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = Tenant.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        search_type = self.request.GET.get('search_type', 'room_number')

        if search:
            if search_type == 'room_number':
                queryset = queryset.filter(room_number__icontains=search)
            elif search_type == 'tenant_name':
                queryset = queryset.filter(tenant_name__icontains=search)
            elif search_type == 'tenant_phone':
                queryset = queryset.filter(tenant_phone__icontains=search)

        return queryset.order_by('property_fee_due_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = OverdueTenantSearchForm(self.request.GET)

        # 计算逾期统计数据 - 基于所有逾期租客，不只是当前页面
        all_overdue_tenants = self.get_queryset()
        total_overdue = all_overdue_tenants.count()

        if total_overdue > 0:
            # 计算短期逾期（7天内）
            short_overdue = sum(1 for tenant in all_overdue_tenants if tenant.overdue_days <= 7)
            # 计算长期逾期（30天以上）
            long_overdue = sum(1 for tenant in all_overdue_tenants if tenant.overdue_days > 30)
        else:
            short_overdue = 0
            long_overdue = 0

        context['total_overdue'] = total_overdue
        context['short_overdue'] = short_overdue
        context['long_overdue'] = long_overdue

        return context


class CheckoutTenantListView(LoginRequiredMixin, ListView):
    """退房租客列表视图"""
    model = Tenant
    template_name = 'tenants/checkout_list.html'
    context_object_name = 'tenants'
    paginate_by = 15

    def get_queryset(self):
        queryset = Tenant.objects.filter(status='checkout')

        # 搜索功能
        search = self.request.GET.get('search')
        search_type = self.request.GET.get('search_type', 'room_number')

        if search:
            if search_type == 'room_number':
                queryset = queryset.filter(room_number__icontains=search)
            elif search_type == 'tenant_name':
                queryset = queryset.filter(tenant_name__icontains=search)
            elif search_type == 'id_card':
                queryset = queryset.filter(id_card__icontains=search)
            elif search_type == 'tenant_phone':
                queryset = queryset.filter(tenant_phone__icontains=search)

        return queryset.order_by('-updated_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TenantSearchForm(self.request.GET)

        # 计算退房统计数据 - 基于全部退房数据，不是分页数据
        from django.utils import timezone

        # 获取所有退房租客（不分页）
        all_checkout_tenants = Tenant.objects.filter(status='checkout')

        # 应用搜索过滤（如果有搜索条件）
        search = self.request.GET.get('search')
        search_type = self.request.GET.get('search_type', 'room_number')

        if search:
            if search_type == 'room_number':
                all_checkout_tenants = all_checkout_tenants.filter(room_number__icontains=search)
            elif search_type == 'tenant_name':
                all_checkout_tenants = all_checkout_tenants.filter(tenant_name__icontains=search)
            elif search_type == 'tenant_phone':
                all_checkout_tenants = all_checkout_tenants.filter(tenant_phone__icontains=search)

        # 总退房数
        total_checkout_count = all_checkout_tenants.count()

        # 本月退房数
        current_month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_count = all_checkout_tenants.filter(
            updated_at__gte=current_month_start
        ).count()

        # 最近退房日期
        latest_checkout_tenant = all_checkout_tenants.order_by('-updated_at').first()
        latest_checkout = latest_checkout_tenant.updated_at if latest_checkout_tenant else None

        context['total_checkout_count'] = total_checkout_count
        context['this_month_checkout_count'] = this_month_count
        context['latest_checkout'] = latest_checkout

        # 计算退房率（退房户数 / 总户数 * 100）
        total_tenants = Tenant.objects.count()  # 所有状态的租客总数
        if total_tenants > 0:
            checkout_rate = (total_checkout_count / total_tenants) * 100
        else:
            checkout_rate = 0
        context['checkout_rate'] = round(checkout_rate, 1)

        return context


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """租客缴费流水视图"""
    model = TenantPaymentHistory
    template_name = 'tenants/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 15

    def get_queryset(self):
        queryset = TenantPaymentHistory.objects.select_related('tenant')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tenant__tenant_name__icontains=search) |
                Q(tenant__room_number__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                # 将日期字符串转换为日期对象，然后设置为当天的开始时间
                from datetime import datetime
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass  # 忽略无效的日期格式

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                # 将日期字符串转换为日期对象，然后设置为当天的结束时间
                from datetime import datetime, timedelta
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                # 设置为当天的23:59:59
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass  # 忽略无效的日期格式

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = PaymentHistorySearchForm(self.request.GET)

        # 重新获取当前筛选条件下的所有数据来计算统计
        queryset = self.get_queryset()

        # 导入必要的模块
        from decimal import Decimal
        from django.db.models import Sum, Count
        from django.utils import timezone
        from datetime import timedelta

        # 计算统计数据
        if queryset.exists():
            # 使用数据库聚合函数计算，确保准确性
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0
            average_amount = total_amount / total_count if total_count > 0 else Decimal('0')

            # 计算本月缴费数量和金额
            now = timezone.now()

            # 使用本地时间计算月份范围
            local_now = timezone.localtime(now)

            # 计算本月的开始和结束时间
            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if local_now.month == 12:
                next_month_start = this_month_start.replace(year=local_now.year + 1, month=1)
            else:
                next_month_start = this_month_start.replace(month=local_now.month + 1)

            # 转换为UTC时间进行查询
            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 计算上月缴费金额
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # 转换为UTC时间
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context


class RenewTenantView(LoginRequiredMixin, View):
    """租客续费视图"""
    template_name = 'tenants/renew.html'

    def get(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        form = TenantRenewForm(tenant=tenant)
        return render(request, self.template_name, {
            'tenant': tenant,
            'form': form
        })

    def post(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        form = TenantRenewForm(tenant=tenant, data=request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data['notes']

            # 计算续费金额
            monthly_fee = tenant.property_fee
            total_amount = monthly_fee * months

            # 计算费用期间 - 始终基于物业费到期时间计算
            current_due_date = tenant.property_fee_due_date

            # 从当前物业费到期日期的下一天开始计算（无论是否逾期）
            fee_start_date = current_due_date + timedelta(days=1)

            # 使用dateutil计算新的到期日期（精确按月计算）
            new_due_date = Tenant.add_months_to_date(fee_start_date, months) - timedelta(days=1)
            fee_end_date = new_due_date

            # 更新租客信息
            tenant.property_fee_due_date = new_due_date
            tenant.status = 'active'
            tenant.save()

            # 创建缴费记录
            # 如果用户没有填写备注，自动生成续费信息
            if not notes or notes.strip() == '':
                notes = f'续费{months}个月'

            TenantPaymentHistory.objects.create(
                tenant=tenant,
                amount=total_amount,
                fee_start_date=fee_start_date,
                fee_end_date=fee_end_date,
                payment_method=payment_method,
                notes=notes
            )

            messages.success(request, f'租客 {tenant.tenant_name} 续费成功！续费 {months} 个月，金额 ¥{total_amount}')
            return redirect('tenants:list')

        return render(request, self.template_name, {
            'tenant': tenant,
            'form': form
        })


class CheckoutTenantView(LoginRequiredMixin, View):
    """租客退房视图"""

    def post(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        tenant.status = 'checkout'
        tenant.save()

        messages.success(request, f'租客 {tenant.tenant_name} 已成功退房！')
        # 根据来源页面决定重定向位置
        next_url = request.GET.get('next', 'tenants:overdue')
        return redirect(next_url)


class RestoreTenantView(LoginRequiredMixin, View):
    """恢复租客视图"""

    def post(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        tenant.status = 'active'
        tenant.save()

        messages.success(request, f'租客 {tenant.tenant_name} 已成功恢复！')
        return redirect('tenants:checkout')


class BatchDeleteCheckoutView(LoginRequiredMixin, View):
    """批量删除退房租客"""

    def post(self, request):
        selected_items = request.POST.getlist('selected_items')
        confirmed = request.POST.get('confirmed', 'false')

        if selected_items:
            # 先获取要删除的租客数量
            tenants_to_delete = Tenant.objects.filter(
                pk__in=selected_items,
                status='checkout'
            )
            deleted_count = tenants_to_delete.count()

            # 执行删除操作
            tenants_to_delete.delete()

            messages.success(request, f'成功删除 {deleted_count} 个退房租客！')
        else:
            messages.warning(request, '请选择要删除的租客！')

        return redirect('tenants:checkout')


class BatchDeletePaymentView(LoginRequiredMixin, View):
    """批量删除缴费记录"""

    def post(self, request):
        payment_ids = request.POST.getlist('payment_ids')
        if payment_ids:
            deleted_count = TenantPaymentHistory.objects.filter(
                pk__in=payment_ids
            ).delete()[0]

            messages.success(request, f'成功删除 {deleted_count} 条缴费记录！')
        else:
            messages.warning(request, '请选择要删除的缴费记录！')

        return redirect('tenants:payment_history')


class ExportPaymentHistoryView(LoginRequiredMixin, View):
    """导出缴费流水数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "租客缴费流水"

        # 设置表头
        headers = [
            '缴费时间', '租客姓名', '房号', '缴费金额', '费用开始时间',
            '费用结束时间', '缴费方式', '备注'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        payments = TenantPaymentHistory.objects.select_related('tenant')

        # 应用与列表页面相同的筛选条件
        # 搜索功能
        search = request.GET.get('search')
        if search:
            payments = payments.filter(
                Q(tenant__tenant_name__icontains=search) |
                Q(tenant__room_number__icontains=search) |
                Q(tenant__building_number__icontains=search) |
                Q(payment_method__icontains=search) |
                Q(notes__icontains=search)
            )

        # 日期筛选
        start_date = request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                # 设置为当天的23:59:59
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        payments = payments.order_by('-payment_date')

        # 写入数据
        for row, payment in enumerate(payments, 2):
            # 将时区感知的datetime转换为本地时间
            local_payment_date = timezone.localtime(payment.payment_date)

            ws.cell(row=row, column=1, value=local_payment_date.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=2, value=payment.tenant.tenant_name)
            ws.cell(row=row, column=3, value=f"{payment.tenant.building_number}-{payment.tenant.room_number}")
            ws.cell(row=row, column=4, value=float(payment.amount))
            ws.cell(row=row, column=5, value=payment.fee_start_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=6, value=payment.fee_end_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=7, value=payment.payment_method)
            ws.cell(row=row, column=8, value=payment.notes)

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # 生成文件名，包含时间范围信息
        filename = "租客缴费流水"
        if start_date and end_date:
            filename += f"_{start_date}至{end_date}"
        elif start_date:
            filename += f"_{start_date}起"
        elif end_date:
            filename += f"_至{end_date}"
        filename += f"_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # 保存到响应
        wb.save(response)
        return response


class ExportTenantView(LoginRequiredMixin, View):
    """导出租客数据（只导出当前页面显示的活跃租客）"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "租客列表"

        # 设置表头
        headers = [
            '楼号', '房号', '平米数', '租客姓名', '身份证号', '租客电话', '租住人数',
            '房东姓名', '房东电话', '入住时间', '物业费到期时间',
            '楼层', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据 - 使用与列表页面相同的查询逻辑
        # 先更新逾期状态
        today = timezone.now().date()
        Tenant.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 只导出正常状态的租客（与列表页面一致）
        queryset = Tenant.objects.filter(status='active')

        # 搜索功能（如果有搜索参数）
        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tenant_name__icontains=search) |
                Q(room_number__icontains=search) |
                Q(id_card__icontains=search) |
                Q(tenant_phone__icontains=search) |
                Q(landlord_name__icontains=search)
            )

        tenants = queryset.order_by('-created_at')

        # 写入数据
        for row, tenant in enumerate(tenants, 2):
            ws.cell(row=row, column=1, value=tenant.building_number)
            ws.cell(row=row, column=2, value=tenant.room_number)
            ws.cell(row=row, column=3, value=float(tenant.area))
            ws.cell(row=row, column=4, value=tenant.tenant_name)
            ws.cell(row=row, column=5, value=tenant.id_card)
            ws.cell(row=row, column=6, value=tenant.tenant_phone or '')
            ws.cell(row=row, column=7, value=tenant.resident_count)
            ws.cell(row=row, column=8, value=tenant.landlord_name)
            ws.cell(row=row, column=9, value=tenant.landlord_phone)
            ws.cell(row=row, column=10, value=tenant.move_in_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=11, value=tenant.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=12, value=tenant.floor)
            ws.cell(row=row, column=13, value=tenant.get_status_display())
            # 将时区感知的datetime转换为本地时间
            local_created_at = timezone.localtime(tenant.created_at)
            ws.cell(row=row, column=14, value=local_created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="活跃租客列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ExportOverdueTenantView(LoginRequiredMixin, View):
    """导出逾期租客数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "逾期租客列表"

        # 设置表头
        headers = [
            '楼号', '房号', '平米数', '租客姓名', '身份证号', '租客电话',
            '房东姓名', '房东电话', '物业费到期时间', '已逾期天数', '月物业费'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

        # 获取逾期租客数据
        today = timezone.now().date()
        Tenant.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        tenants = Tenant.objects.filter(status='overdue').order_by('property_fee_due_date')

        # 写入数据
        for row, tenant in enumerate(tenants, 2):
            ws.cell(row=row, column=1, value=tenant.building_number)
            ws.cell(row=row, column=2, value=tenant.room_number)
            ws.cell(row=row, column=3, value=float(tenant.area))
            ws.cell(row=row, column=4, value=tenant.tenant_name)
            ws.cell(row=row, column=5, value=tenant.id_card)
            ws.cell(row=row, column=6, value=tenant.tenant_phone or '')
            ws.cell(row=row, column=7, value=tenant.landlord_name)
            ws.cell(row=row, column=8, value=tenant.landlord_phone)
            ws.cell(row=row, column=9, value=tenant.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=10, value=tenant.overdue_days)
            ws.cell(row=row, column=11, value=float(tenant.property_fee))

            # 根据逾期天数设置行颜色
            if tenant.overdue_days > 30:
                fill_color = 'FFCCCC'  # 深红色
            elif tenant.overdue_days > 7:
                fill_color = 'FFE6CC'  # 橙色
            else:
                fill_color = 'FFFFCC'  # 浅黄色

            for col in range(1, 12):
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color=fill_color, end_color=fill_color, fill_type='solid')

        # 调整列宽
        column_widths = [8, 8, 10, 12, 20, 15, 12, 15, 15, 12, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="逾期租客列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ExportCheckoutTenantView(LoginRequiredMixin, View):
    """导出退房租客数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "退房租客列表"

        # 设置表头
        headers = [
            '楼号', '房号', '平米数', '租客姓名', '身份证号', '租客电话',
            '房东姓名', '房东电话', '入住时间', '退房日期'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='E6E6E6', end_color='E6E6E6', fill_type='solid')

        # 获取退房租客数据
        tenants = Tenant.objects.filter(status='checkout').order_by('-updated_at')

        # 写入数据
        for row, tenant in enumerate(tenants, 2):
            ws.cell(row=row, column=1, value=tenant.building_number)
            ws.cell(row=row, column=2, value=tenant.room_number)
            ws.cell(row=row, column=3, value=float(tenant.area))
            ws.cell(row=row, column=4, value=tenant.tenant_name)
            ws.cell(row=row, column=5, value=tenant.id_card)
            ws.cell(row=row, column=6, value=tenant.tenant_phone or '')
            ws.cell(row=row, column=7, value=tenant.landlord_name)
            ws.cell(row=row, column=8, value=tenant.landlord_phone)
            ws.cell(row=row, column=9, value=tenant.move_in_date.strftime('%Y-%m-%d'))
            # 将时区感知的datetime转换为本地时间
            local_updated_at = timezone.localtime(tenant.updated_at)
            ws.cell(row=row, column=10, value=local_updated_at.strftime('%Y-%m-%d'))

        # 调整列宽
        column_widths = [8, 8, 10, 12, 20, 15, 12, 15, 12, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="退房租客列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ImportTenantView(LoginRequiredMixin, View):
    """导入租客数据"""
    template_name = 'tenants/import.html'

    def get(self, request):
        form = TenantImportForm()
        return render(request, self.template_name, {'form': form})

    def convert_to_date(self, date_value):
        """
        将各种日期格式转换为date对象 - 强化版本
        """
        try:
            # 如果值为空或None
            if not date_value:
                return timezone.now().date()

            # 如果是datetime对象
            if isinstance(date_value, datetime):
                return date_value.date()

            # 如果已经是date对象
            if isinstance(date_value, date):
                return date_value

            # 如果是字符串
            if isinstance(date_value, str):
                date_value = date_value.strip()
                if not date_value:
                    return timezone.now().date()

                # 尝试不同的日期格式
                date_formats = [
                    '%Y-%m-%d',      # 2024-01-01
                    '%Y/%m/%d',      # 2024/01/01
                    '%Y.%m.%d',      # 2024.01.01
                    '%d/%m/%Y',      # 01/01/2024
                    '%d-%m-%Y',      # 01-01-2024
                    '%d.%m.%Y',      # 01.01.2024
                    '%Y%m%d',        # 20240101
                ]

                for fmt in date_formats:
                    try:
                        parsed_date = datetime.strptime(date_value, fmt)
                        return parsed_date.date()
                    except (ValueError, TypeError):
                        continue

            # 如果有date方法（可能是其他类型的datetime对象）
            if hasattr(date_value, 'date') and callable(getattr(date_value, 'date')):
                try:
                    return date_value.date()
                except (AttributeError, TypeError):
                    pass

            # 如果有年月日属性（可能是date-like对象）
            if hasattr(date_value, 'year') and hasattr(date_value, 'month') and hasattr(date_value, 'day'):
                try:
                    return date(date_value.year, date_value.month, date_value.day)
                except (ValueError, TypeError, AttributeError):
                    pass

        except Exception:
            # 任何异常都返回当前日期
            pass

        # 最终回退：返回当前日期
        return timezone.now().date()

    def post(self, request):
        form = TenantImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']
            skip_duplicates = request.POST.get('skip_duplicates') == 'on'

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                skip_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:4]):  # 如果前4列都为空，跳过这行
                            continue

                        # 解析数据
                        building_number = str(row[0]) if row[0] else ''
                        room_number = str(row[1]) if row[1] else ''
                        area = Decimal(str(row[2])) if row[2] else Decimal('0')
                        tenant_name = str(row[3]) if row[3] else ''
                        id_card = str(row[4]).strip() if row[4] and str(row[4]).strip() else None
                        tenant_phone = str(row[5]) if row[5] else ''
                        resident_count = int(row[6]) if row[6] else 1
                        landlord_name = str(row[7]) if row[7] else ''
                        landlord_phone = str(row[8]).strip() if row[8] and str(row[8]).strip() else None

                        # 验证必填字段
                        if not tenant_name:
                            errors.append(f'第{row_num}行: 租客姓名不能为空')
                            error_count += 1
                            continue

                        # 解析日期 - 使用新的日期转换方法
                        move_in_date = self.convert_to_date(row[9])
                        default_due_date = timezone.now().date() + timedelta(days=365)
                        property_fee_due_date = self.convert_to_date(row[10]) if row[10] else default_due_date

                        floor = int(row[11]) if len(row) > 11 and row[11] else 1

                        # 检查重复记录
                        if skip_duplicates:
                            existing = Tenant.objects.filter(
                                building_number=building_number,
                                room_number=room_number
                            ).exists()
                            if existing:
                                skip_count += 1
                                continue

                        # 根据房号自动设置面积和楼层
                        if room_number:
                            last_two = room_number[-2:] if len(room_number) >= 2 else ''
                            if last_two in ['01', '04']:
                                area = Decimal('130.00')
                            elif last_two in ['02', '03']:
                                area = Decimal('90.00')

                            # 自动计算楼层：从房号中提取楼层数
                            if len(room_number) >= 3:
                                floor_number = room_number[:-2]
                                if floor_number and floor_number.isdigit():
                                    floor = int(floor_number)

                        # 创建租客记录 - 直接使用转换后的date对象
                        tenant = Tenant.objects.create(
                            building_number=building_number,
                            room_number=room_number,
                            area=area,
                            tenant_name=tenant_name,
                            id_card=id_card,
                            tenant_phone=tenant_phone,
                            resident_count=resident_count,
                            landlord_name=landlord_name,
                            landlord_phone=landlord_phone,
                            move_in_date=move_in_date,
                            property_fee_due_date=property_fee_due_date,
                            floor=floor,
                            status='active'  # 明确设置状态
                        )
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                # 显示导入结果
                result_messages = []
                if success_count > 0:
                    result_messages.append(f'成功导入 {success_count} 条记录')
                if skip_count > 0:
                    result_messages.append(f'跳过重复记录 {skip_count} 条')
                if error_count > 0:
                    result_messages.append(f'导入失败 {error_count} 条记录')

                if success_count > 0:
                    messages.success(request, '；'.join(result_messages) + '！')
                elif skip_count > 0:
                    messages.warning(request, '；'.join(result_messages) + '！')

                if error_count > 0:
                    error_msg = '导入错误详情：\n' + '\n'.join(errors[:5])
                    if len(errors) > 5:
                        error_msg += f'\n... 还有 {len(errors) - 5} 个错误'
                    messages.error(request, error_msg)

                return redirect('tenants:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})


class DownloadTemplateView(LoginRequiredMixin, View):
    """下载导入模板"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "租客导入模板"

        # 设置表头
        headers = [
            '楼号', '房号', '平米数', '租客姓名', '身份证号', '租客电话', '租住人数',
            '房东姓名', '房东电话', '入住时间', '物业费到期时间', '楼层'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 添加示例数据
        example_data = [
            ['1', '101', '130', '张三', '110101199001011234', '13800138001', '2', '李房东', '13800138000', '2024-01-01', '2024-12-31', '1'],
            ['1', '102', '90', '李四', '110101199002021234', '13800138002', '1', '王房东', '13900139000', '2024-02-01', '2025-01-31', '1'],
        ]

        for row_num, row_data in enumerate(example_data, 2):
            for col_num, value in enumerate(row_data, 1):
                ws.cell(row=row_num, column=col_num, value=value)

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="租客导入模板.xlsx"'

        # 保存到响应
        wb.save(response)
        return response
