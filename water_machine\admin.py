from django.contrib import admin
from .models import VillageCustomer, VillageRechargeRecord


@admin.register(VillageCustomer)
class VillageCustomerAdmin(admin.ModelAdmin):
    list_display = ['name', 'address', 'phone', 'water_bucket', 'normal_card_number', 'people_count', 'get_fixed_amount', 'created_at']
    list_filter = ['water_bucket', 'created_at']
    search_fields = ['name', 'phone', 'address', 'normal_card_number']
    ordering = ['address']

    def get_fixed_amount(self, obj):
        return f"¥{obj.get_fixed_amount()}"
    get_fixed_amount.short_description = '季度金额'


@admin.register(VillageRechargeRecord)
class VillageRechargeRecordAdmin(admin.ModelAdmin):
    list_display = ['customer', 'card_number', 'people_count', 'amount', 'quarter', 'recharge_date']
    list_filter = ['quarter', 'recharge_date']
    search_fields = ['customer__name', 'card_number']
    ordering = ['-recharge_date']
