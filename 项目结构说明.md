# 东悦物业管理系统 - 项目结构说明

## 📁 部署后目录结构

```
C:\dywy\                              # 项目根目录
├── 📁 venv\                          # Python 虚拟环境
│   ├── 📁 Scripts\                   # 虚拟环境脚本
│   │   ├── activate.bat              # 激活虚拟环境（批处理）
│   │   ├── activate.ps1              # 激活虚拟环境（PowerShell）
│   │   ├── python.exe                # Python 解释器
│   │   └── pip.exe                   # 包管理器
│   └── 📁 Lib\                       # Python 库文件
│
├── 📁 property_management\           # Django 主项目目录
│   ├── __init__.py
│   ├── settings.py                   # 项目设置文件
│   ├── settings_production.py        # 生产环境设置（可选）
│   ├── urls.py                       # URL 路由配置
│   ├── wsgi.py                       # WSGI 应用入口
│   └── asgi.py                       # ASGI 应用入口
│
├── 📁 tenants\                       # 租户管理应用
│   ├── 📁 migrations\                # 数据库迁移文件
│   ├── 📁 templates\                 # 模板文件
│   ├── 📁 static\                    # 静态文件
│   ├── models.py                     # 数据模型
│   ├── views.py                      # 视图函数
│   ├── urls.py                       # URL 配置
│   ├── forms.py                      # 表单定义
│   └── admin.py                      # 管理后台配置
│
├── 📁 commercial_properties\         # 商业物业管理应用
│   ├── 📁 migrations\
│   ├── 📁 templates\
│   ├── 📁 static\
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── forms.py
│   └── admin.py
│
├── 📁 shops\                         # 商铺管理应用
│   ├── 📁 migrations\
│   ├── 📁 templates\
│   ├── 📁 static\
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── forms.py
│   └── admin.py
│
├── 📁 parking\                       # 停车管理应用
│   ├── 📁 migrations\
│   ├── 📁 templates\
│   ├── 📁 static\
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── forms.py
│   └── admin.py
│
├── 📁 dashboard\                     # 仪表板应用
│   ├── 📁 migrations\
│   ├── 📁 templates\
│   ├── 📁 static\
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   └── admin.py
│
├── 📁 other_income\                  # 其他收入管理应用
│   ├── 📁 migrations\
│   ├── 📁 templates\
│   ├── 📁 static\
│   ├── models.py
│   ├── views.py
│   ├── urls.py
│   ├── forms.py
│   └── admin.py
│
├── 📁 static\                        # 全局静态文件
│   ├── 📁 css\                       # CSS 样式文件
│   ├── 📁 js\                        # JavaScript 文件
│   ├── 📁 images\                    # 图片文件
│   └── 📁 fonts\                     # 字体文件
│
├── 📁 staticfiles\                   # 收集的静态文件（生产环境）
│   └── ...                           # 所有应用的静态文件集合
│
├── 📁 media\                         # 用户上传的媒体文件
│   ├── 📁 uploads\                   # 上传文件
│   └── 📁 exports\                   # 导出文件
│
├── 📁 templates\                     # 全局模板文件
│   ├── base.html                     # 基础模板
│   ├── navbar.html                   # 导航栏模板
│   └── 📁 registration\              # 用户认证模板
│
├── 📁 logs\                          # 日志文件目录
│   ├── django.log                    # Django 应用日志
│   ├── gunicorn_access.log           # Gunicorn 访问日志
│   └── gunicorn_error.log            # Gunicorn 错误日志
│
├── 📁 backups\                       # 备份文件目录
│   ├── db_backup_20241205_143000.sql # 数据库备份
│   └── media_backup_20241205_143000.zip # 媒体文件备份
│
├── 📁 cache\                         # 缓存文件目录（如果使用文件缓存）
│   └── ...                           # 缓存文件
│
├── 📄 manage.py                      # Django 管理脚本
├── 📄 requirements.txt               # Python 依赖包列表
├── 📄 .env                           # 环境变量文件（不提交到版本控制）
├── 📄 .gitignore                     # Git 忽略文件配置
├── 📄 README.md                      # 项目说明文档
│
├── 📄 start.bat                      # 开发环境启动脚本
├── 📄 start_production.bat           # 生产环境启动脚本
├── 📄 start_production.py            # 生产环境启动 Python 脚本
│
├── 📄 backup.ps1                     # 数据备份脚本
├── 📄 restore.ps1                    # 数据恢复脚本
├── 📄 monitor.ps1                    # 系统监控脚本
├── 📄 maintenance.ps1                # 系统维护脚本
├── 📄 update.ps1                     # 系统更新脚本
└── 📄 debug.ps1                      # 调试信息收集脚本
```

## 📋 重要文件说明

### 核心配置文件

| 文件 | 说明 | 重要性 |
|------|------|--------|
| `manage.py` | Django 管理脚本，用于执行各种管理命令 | ⭐⭐⭐⭐⭐ |
| `property_management/settings.py` | 项目主配置文件 | ⭐⭐⭐⭐⭐ |
| `property_management/urls.py` | 主 URL 路由配置 | ⭐⭐⭐⭐ |
| `requirements.txt` | Python 依赖包列表 | ⭐⭐⭐⭐⭐ |

### 启动脚本

| 文件 | 用途 | 使用场景 |
|------|------|----------|
| `start.bat` | 开发环境启动 | 开发、测试 |
| `start_production.bat` | 生产环境启动 | 生产部署 |
| `start_production.py` | Waitress 服务器配置 | 生产环境 |

### 维护脚本

| 文件 | 功能 | 执行频率 |
|------|------|----------|
| `backup.ps1` | 数据库和文件备份 | 每日 |
| `monitor.ps1` | 系统状态监控 | 按需 |
| `maintenance.ps1` | 系统维护清理 | 每周 |
| `update.ps1` | 系统更新 | 按需 |

## 🔧 目录权限说明

### Windows 权限要求

```powershell
# 项目目录权限
icacls "C:\dywy" /grant "Users:(OI)(CI)F" /T

# 日志目录权限
icacls "C:\dywy\logs" /grant "Users:(OI)(CI)F" /T

# 媒体文件目录权限
icacls "C:\dywy\media" /grant "Users:(OI)(CI)F" /T

# 备份目录权限
icacls "C:\dywy\backups" /grant "Users:(OI)(CI)F" /T
```

## 📊 磁盘空间规划

### 目录大小估算

| 目录 | 初始大小 | 增长速度 | 说明 |
|------|----------|----------|------|
| `venv\` | ~200MB | 稳定 | Python 虚拟环境 |
| `static\` | ~50MB | 缓慢 | 静态文件 |
| `media\` | ~10MB | 中等 | 用户上传文件 |
| `logs\` | ~1MB | 快速 | 日志文件 |
| `backups\` | ~100MB | 快速 | 备份文件 |
| `cache\` | ~50MB | 中等 | 缓存文件 |

### 清理建议

```powershell
# 清理过期日志（保留30天）
Get-ChildItem "C:\dywy\logs\*.log.*" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-30) } | Remove-Item

# 清理过期备份（保留7天）
Get-ChildItem "C:\dywy\backups\*" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-7) } | Remove-Item

# 清理缓存文件
Remove-Item "C:\dywy\cache\*" -Recurse -Force
```

## 🔒 安全注意事项

### 敏感文件保护

1. **`.env` 文件**：包含数据库密码等敏感信息，不应提交到版本控制
2. **`logs\` 目录**：可能包含敏感信息，需要定期清理
3. **`backups\` 目录**：包含完整数据备份，需要加密存储

### 文件权限设置

```powershell
# 限制敏感文件访问
icacls "C:\dywy\.env" /inheritance:r /grant:r "Administrators:F" "SYSTEM:F"
icacls "C:\dywy\backups" /inheritance:r /grant:r "Administrators:F" "SYSTEM:F"
```

## 📝 维护检查清单

### 每日检查
- [ ] 检查服务运行状态
- [ ] 查看错误日志
- [ ] 确认备份完成

### 每周检查
- [ ] 清理过期日志
- [ ] 检查磁盘空间
- [ ] 更新系统补丁

### 每月检查
- [ ] 数据库优化
- [ ] 性能监控
- [ ] 安全审计

---

**注意**：此结构说明基于标准部署配置，实际结构可能因项目定制而有所不同。
