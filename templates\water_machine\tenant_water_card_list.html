{% extends "base/base.html" %}

{% block title %}租户售水机卡号记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-id-card text-primary me-2"></i>
            租户售水机卡号记录
            <span class="badge bg-primary ms-2">{{ total_count }}条</span>
            <span class="badge bg-info ms-1">{{ total_buckets }}个桶</span>
            <span class="badge bg-success ms-1">{{ normal_cards_count }}卡</span>
        </h2>
        <div class="btn-group">
            <a href="{% url 'water_machine:tenant_water_card_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>新增记录
            </a>
            <a href="{% url 'water_machine:tenant_water_card_export' %}" class="btn btn-success">
                <i class="fas fa-download me-1"></i>导出记录
            </a>
            <a href="{% url 'water_machine:tenant_water_card_import' %}" class="btn btn-info">
                <i class="fas fa-upload me-1"></i>批量导入
            </a>
        </div>
    </div>

    <!-- 搜索表单 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        {{ search_form.search }}
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>
                <div class="col-md-2">
                    <a href="{% url 'water_machine:tenant_water_card_list' %}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                </div>
            </form>
        </div>
    </div>



    <!-- 记录列表 -->
    {% if records %}
        <div class="card">
            <div class="card-body">
                <!-- 数据表格 - 分离表头表体结构 -->
                <div class="table-container">
                    <!-- 固定表头 -->
                    <div class="table-header-fixed">
                        <table class="table mb-0" id="header-table">
                            <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>住址</th>
                                    <th>电话</th>
                                    <th>水桶</th>
                                    <th>正常卡号</th>
                                    <th>丢失卡号</th>
                                    <th>记录时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    <!-- 可滚动表体 -->
                    <div class="table-body-scroll">
                        <table class="table table-hover mb-0" id="body-table">
                            <thead style="visibility: hidden;">
                                <tr>
                                    <th>姓名</th>
                                    <th>住址</th>
                                    <th>电话</th>
                                    <th>水桶</th>
                                    <th>正常卡号</th>
                                    <th>丢失卡号</th>
                                    <th>记录时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in records %}
                                <tr>
                                    <td>{{ record.name }}</td>
                                    <td>{{ record.address }}</td>
                                    <td>{{ record.phone }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ record.water_bucket }}个</span>
                                    </td>
                                    <td>
                                        {% if record.normal_card_number %}
                                            <strong class="text-primary">{{ record.normal_card_number }}</strong>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if record.get_lost_card_display != "-" %}
                                            <span class="lost-card-text">{{ record.get_lost_card_display }}</span>
                                        {% else %}
                                            <span class="text-muted">{{ record.get_lost_card_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ record.record_time|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'water_machine:tenant_water_card_edit' record.pk %}"
                                               class="action-btn edit-btn" title="编辑记录信息">
                                                <i class="fas fa-edit"></i>
                                                <span class="btn-text">编辑</span>
                                            </a>
                                            {% if record.normal_card_number %}
                                                <a href="{% url 'water_machine:tenant_water_card_lost' record.pk %}"
                                                   class="action-btn lost-btn" title="挂失水卡">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <span class="btn-text">挂失</span>
                                                </a>
                                            {% endif %}
                                            <a href="{% url 'water_machine:tenant_water_card_delete' record.pk %}"
                                               class="action-btn delete-btn" title="删除记录">
                                                <i class="fas fa-trash"></i>
                                                <span class="btn-text">删除</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 美化的分页导航 -->
                {% if is_paginated %}
                <div class="pagination-container">
                    <!-- 分页统计信息 -->
                    <div class="pagination-info">
                        <div class="info-item">
                            <i class="fas fa-list-ol text-primary"></i>
                            <span class="info-text">
                                共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                            </span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-file-alt text-info"></i>
                            <span class="info-text">
                                第 <strong class="text-info">{{ page_obj.number }}</strong> 页，共 <strong class="text-info">{{ page_obj.paginator.num_pages }}</strong> 页
                            </span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-eye text-success"></i>
                            <span class="info-text">
                                每页显示 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                            </span>
                        </div>
                    </div>

                    <!-- 分页按钮 -->
                    <nav aria-label="分页导航" class="pagination-nav">
                        <ul class="pagination pagination-modern">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="首页">
                                        <i class="fas fa-angle-double-left"></i>
                                        <span class="d-none d-sm-inline">首页</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="上一页">
                                        <i class="fas fa-angle-left"></i>
                                        <span class="d-none d-sm-inline">上一页</span>
                                    </a>
                                </li>
                            {% endif %}

                            <!-- 页码范围 -->
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="下一页">
                                        <span class="d-none d-sm-inline">下一页</span>
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" title="末页">
                                        <span class="d-none d-sm-inline">末页</span>
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无记录</h5>
                    <p class="text-muted">点击"新增记录"按钮添加第一条记录</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    /* 表格容器样式 */
    .table-container {
        position: relative;
        border-radius: 0.375rem;
        overflow: hidden;
        background: white;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
        margin-bottom: 0;
    }

    .table-header-fixed table {
        margin-bottom: 0 !important;
        margin-top: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1000px;
        border-spacing: 0;
        border-collapse: separate;
        border-bottom: none;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 消除表头和表体之间的间隙 */
    .table-header-fixed + .table-body-scroll {
        margin-top: -1px;
        border-top: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        background: white;
        margin-top: 0;
    }

    .table-body-scroll table {
        width: 100%;
        margin-bottom: 0 !important;
        margin-top: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1000px;
        border-top: none;
    }

    /* 表格容器间距最小化 */
    .table-container {
        margin-bottom: 0;
        padding: 0;
    }

    #header-table {
        width: 100%;
        margin-bottom: 0 !important;
        margin-top: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1000px;
    }

    /* 表体隐藏表头 */
    #body-table thead {
        visibility: hidden;
        height: 0;
    }

    #body-table thead th {
        padding: 0;
        border: none;
        height: 0;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
        border-right: 1px solid #dee2e6;
        font-size: 0.875rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-bottom: 1px solid #dee2e6;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    /* 鼠标悬停效果 */
    #body-table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* 设置列宽 - 确保表头和表体对齐 */
    #header-table th:nth-child(1), #body-table th:nth-child(1), #body-table td:nth-child(1) { width: 10%; min-width: 80px; } /* 姓名 */
    #header-table th:nth-child(2), #body-table th:nth-child(2), #body-table td:nth-child(2) { width: 18%; min-width: 150px; } /* 住址 */
    #header-table th:nth-child(3), #body-table th:nth-child(3), #body-table td:nth-child(3) { width: 12%; min-width: 110px; } /* 电话 */
    #header-table th:nth-child(4), #body-table th:nth-child(4), #body-table td:nth-child(4) { width: 8%; min-width: 70px; } /* 水桶 */
    #header-table th:nth-child(5), #body-table th:nth-child(5), #body-table td:nth-child(5) { width: 12%; min-width: 100px; } /* 正常卡号 */
    #header-table th:nth-child(6), #body-table th:nth-child(6), #body-table td:nth-child(6) { width: 16%; min-width: 160px; } /* 丢失卡号 */
    #header-table th:nth-child(7), #body-table th:nth-child(7), #body-table td:nth-child(7) { width: 12%; min-width: 120px; } /* 记录时间 */
    #header-table th:nth-child(8), #body-table th:nth-child(8), #body-table td:nth-child(8) { width: 12%; min-width: 200px; } /* 操作 */

    /* 丢失卡号样式 */
    .lost-card-text {
        color: #dc3545;
        font-weight: 500;
        font-size: 0.8rem;
        word-break: break-all;
        line-height: 1.2;
    }

    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.375rem 0.5rem;
        border-radius: 0.25rem;
        text-decoration: none;
        font-size: 0.75rem;
        font-weight: 500;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        min-width: 60px;
        justify-content: center;
    }

    .edit-btn {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-color: #007bff;
    }

    .edit-btn:hover {
        background: linear-gradient(135deg, #0056b3, #004085);
        color: white;
        border-color: #004085;
    }

    /* 挂失按钮 */
    .lost-btn {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        border-color: #ffc107;
    }

    .lost-btn:hover {
        background: linear-gradient(135deg, #e0a800, #d39e00);
        color: #212529;
        border-color: #d39e00;
    }

    /* 删除按钮 */
    .delete-btn {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border-color: #dc3545;
    }

    .delete-btn:hover {
        background: linear-gradient(135deg, #c82333, #bd2130);
        color: white;
        border-color: #bd2130;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            gap: 2px;
        }

        .action-btn {
            min-width: 50px;
            padding: 4px 8px;
        }

        .btn-text {
            display: none;
        }
    }

    .btn-text {
        font-size: 0.7rem;
    }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: wrap;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: white;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border-left: 4px solid;
        white-space: nowrap;
    }

    .info-item:nth-child(1) { border-left-color: #007bff; }
    .info-item:nth-child(2) { border-left-color: #28a745; }
    .info-item:nth-child(3) { border-left-color: #17a2b8; }

    .info-item i {
        font-size: 0.875rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        background: white;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-first, .page-link-last {
        background: linear-gradient(135deg, #28a745, #20c997) !important;
        border-color: #28a745 !important;
        color: white !important;
    }

    .page-link-first:hover, .page-link-last:hover {
        background: linear-gradient(135deg, #20c997, #17a2b8) !important;
        border-color: #20c997 !important;
        color: white !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4) !important;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.375rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}
