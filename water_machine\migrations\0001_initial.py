# Generated by Django 5.2.3 on 2025-06-30 02:01

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('address', models.Char<PERSON>ield(max_length=200, verbose_name='住址')),
                ('phone', models.CharField(max_length=20, verbose_name='电话')),
                ('water_bucket', models.BooleanField(default=False, help_text='是否有水桶', verbose_name='水桶')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '客户信息',
                'verbose_name_plural': '客户信息',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WaterCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(max_length=20, unique=True, verbose_name='卡号')),
                ('status', models.CharField(choices=[('normal', '正常'), ('lost', '丢失')], default='normal', max_length=10, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='water_cards', to='water_machine.customer', verbose_name='客户')),
            ],
            options={
                'verbose_name': '水卡',
                'verbose_name_plural': '水卡',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RechargeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('people_count', models.PositiveIntegerField(verbose_name='使用人数')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='充值金额')),
                ('quarter', models.CharField(help_text='格式：2025Q1', max_length=10, verbose_name='充值季度')),
                ('recharge_date', models.DateField(default=django.utils.timezone.now, verbose_name='充值日期')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('water_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recharge_records', to='water_machine.watercard', verbose_name='水卡')),
            ],
            options={
                'verbose_name': '充值记录',
                'verbose_name_plural': '充值记录',
                'ordering': ['-recharge_date', '-created_at'],
            },
        ),
    ]
