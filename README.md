# 东悦物业管理系统

## 📋 系统简介

东悦物业管理系统是一个基于Django开发的现代化物业管理平台，提供租客管理、商品房管理、商铺管理、车位管理、售水机管理等全方位的物业服务功能。

### 🌟 主要功能模块

- **🏠 租客管理**：租客信息管理、物业费收缴、到期提醒、退房管理
- **🏢 商品房管理**：业主信息管理、物业费管理、到期列表
- **🏪 商铺管理**：商铺租赁管理、租金收缴、退房处理
- **🚗 车位管理**：车位租赁、停车费管理、车位到期提醒
- **💧 售水机管理**：村售水机充值、租户水卡管理、挂失补办
- **📊 数据统计**：收入统计、流水查询、数据导出
- **📈 仪表盘**：实时数据展示、即将到期提醒、最近收款记录

### 🛠 技术栈

- **后端框架**：Django 5.2
- **数据库**：MySQL 8.0
- **前端技术**：Bootstrap 5.1.3 + jQuery
- **图标库**：Font Awesome 6.0
- **Python版本**：Python 3.8+

## 🚀 安装说明

### 环境要求

- Python 3.8 或更高版本
- MySQL 8.0 或更高版本
- pip 包管理器

### 1. 克隆项目

```bash
git clone <项目地址>
cd dywy
```

### 2. 创建虚拟环境

```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 数据库配置

#### 4.1 创建MySQL数据库

```sql
CREATE DATABASE dywy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dywy_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON dywy_db.* TO 'dywy_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 4.2 配置数据库连接

编辑 `property_management/settings.py` 文件：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dywy_db',
        'USER': 'dywy_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

### 5. 数据库迁移

```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. 创建超级用户

```bash
python manage.py createsuperuser
```

### 7. 收集静态文件

```bash
python manage.py collectstatic
```

### 8. 启动开发服务器

```bash
python manage.py runserver
```

访问 `http://127.0.0.1:8000` 即可使用系统。

## 📖 使用说明

### 登录系统

1. 打开浏览器访问系统地址
2. 使用创建的超级用户账号登录
3. 进入系统主界面

### 🏠 租客管理

#### 租客信息管理
- **添加租客**：点击"新增租客"按钮，填写租客基本信息
- **编辑租客**：在租客列表中点击"编辑"按钮
- **删除租客**：点击"删除"按钮（支持批量删除）
- **搜索租客**：使用搜索框按房号、姓名、身份证号、电话搜索

#### 物业费管理
- **续费操作**：点击"续费"按钮，选择续费月数
- **查看到期**：在"物业费到期列表"查看即将到期的租客
- **流水统计**：在"物业费流水统计"查看收费记录

#### 退房管理
- **办理退房**：点击"退房"按钮，填写退房信息
- **恢复租客**：在"租客退房列表"中可恢复已退房租客

### 🏢 商品房管理

#### 业主信息管理
- **添加业主**：录入业主基本信息和房产信息
- **物业费收缴**：管理业主物业费缴纳情况
- **到期提醒**：查看即将到期的物业费

### 🏪 商铺管理

#### 商铺租赁管理
- **商铺信息**：管理商铺基本信息和租户信息
- **租金管理**：处理商铺租金收缴
- **退租处理**：办理商铺退租手续

### 🚗 车位管理

#### 车位租赁
- **车位信息**：管理车位基本信息
- **租户管理**：记录车位租户信息
- **停车费**：处理停车费收缴

### 💧 售水机管理

#### 村售水机系统
- **客户管理**：管理村售水机客户信息
- **季度充值**：按季度收取水费（人数×15元×3月）
- **水卡管理**：正常卡号和丢失卡号管理
- **挂失补办**：水卡挂失和新卡办理

#### 租户售水机系统
- **卡号记录**：记录租户水卡信息
- **挂失管理**：处理水卡挂失业务

### 📊 数据统计

#### 收入统计
- **仪表盘**：查看实时收入数据
- **流水查询**：按时间范围查询收费流水
- **数据导出**：导出Excel格式的统计报表

## 🔧 系统配置

### 会话设置

系统默认登录保持14天，可在 `settings.py` 中调整：

```python
SESSION_COOKIE_AGE = 1209600  # 14天
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # 浏览器关闭不退出
SESSION_SAVE_EVERY_REQUEST = True  # 活动延期
```

### 分页设置

各列表页面默认每页显示15条记录，可在对应视图中调整 `paginate_by` 参数。

### 搜索功能

所有列表页面都支持搜索功能：
- **下拉选择**：选择搜索条件（房号、姓名、身份证号、电话）
- **关键词搜索**：输入关键词进行模糊搜索
- **重置功能**：一键清除搜索条件

## 📁 项目结构

```
dywy/
├── property_management/     # 项目配置
├── dashboard/              # 仪表盘模块
├── tenants/               # 租客管理模块
├── commercial_properties/ # 商品房管理模块
├── shops/                 # 商铺管理模块
├── parking/               # 车位管理模块
├── water_machine/         # 售水机管理模块
├── static/                # 静态文件
├── templates/             # 模板文件
├── media/                 # 媒体文件
└── requirements.txt       # 依赖包列表
```

## 🛡 安全说明

### 生产环境配置

1. **修改SECRET_KEY**：生成新的密钥
2. **关闭DEBUG**：设置 `DEBUG = False`
3. **配置ALLOWED_HOSTS**：添加域名或IP
4. **HTTPS配置**：启用SSL证书
5. **数据库安全**：使用强密码，限制访问权限

### 数据备份

建议定期备份数据库：

```bash
mysqldump -u dywy_user -p dywy_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🐛 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库配置信息正确
- 验证用户权限设置

### 2. 静态文件加载失败
- 运行 `python manage.py collectstatic`
- 检查 `STATIC_URL` 和 `STATIC_ROOT` 配置

### 3. 中文显示乱码
- 确保数据库字符集为 `utf8mb4`
- 检查文件编码为 `UTF-8`

## 📞 技术支持

如有问题或建议，请联系技术支持团队。

## 📄 许可证

本项目采用 MIT 许可证。
