from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random

from tenants.models import Tenant, TenantPaymentHistory


class Command(BaseCommand):
    help = '创建一些缴费记录用于测试'

    def handle(self, *args, **options):
        self.stdout.write('开始创建缴费记录...')
        
        # 获取所有租客
        tenants = Tenant.objects.all()
        
        if not tenants:
            self.stdout.write(self.style.ERROR('没有找到租客，请先创建租客数据'))
            return
        
        payment_methods = ['现金', '银行转账', '支付宝', '微信支付', '刷卡']
        
        created_count = 0
        
        # 为每个租客创建1-3条缴费记录
        for tenant in tenants:
            payment_count = random.randint(1, 3)
            
            for i in range(payment_count):
                # 随机生成缴费时间（过去6个月内）
                days_ago = random.randint(1, 180)
                payment_date = timezone.now() - timedelta(days=days_ago)
                
                # 计算费用期间
                months = random.choice([1, 3, 6, 12])
                fee_start_date = payment_date.date() - timedelta(days=30)
                fee_end_date = fee_start_date + timedelta(days=30 * months)
                
                # 计算金额
                amount = tenant.property_fee * months
                
                # 随机备注
                notes_list = [
                    '',
                    '按时缴费',
                    '提前缴费',
                    '补缴费用',
                    '年度缴费',
                    '季度缴费'
                ]
                
                TenantPaymentHistory.objects.create(
                    tenant=tenant,
                    payment_date=payment_date,
                    amount=amount,
                    fee_start_date=fee_start_date,
                    fee_end_date=fee_end_date,
                    payment_method=random.choice(payment_methods),
                    notes=random.choice(notes_list)
                )
                
                created_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'成功创建 {created_count} 条缴费记录！')
        )
