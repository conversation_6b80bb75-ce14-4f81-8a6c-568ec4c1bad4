from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import openpyxl.utils
try:
    from dateutil.relativedelta import relativedelta
except ImportError:
    # 如果没有dateutil，使用简单的月份计算
    relativedelta = None

from .models import ParkingSpace, ParkingPaymentHistory
from .forms import ParkingSpaceForm, ParkingSearchForm, ParkingRenewForm, ParkingPaymentHistorySearchForm, ParkingImportForm


class ParkingListView(LoginRequiredMixin, ListView):
    """车位列表视图"""
    model = ParkingSpace
    template_name = 'parking/list.html'
    context_object_name = 'parkings'
    paginate_by = 15

    def get_queryset(self):
        try:
            # 自动更新逾期状态
            today = timezone.now().date()
            ParkingSpace.objects.filter(
                property_fee_due_date__lt=today,
                status='active'
            ).update(status='overdue')

            # 自动更新正常状态
            ParkingSpace.objects.filter(
                property_fee_due_date__gte=today,
                status='overdue'
            ).update(status='active')

            queryset = ParkingSpace.objects.filter(status='active')

            # 搜索功能
            search = self.request.GET.get('search')
            if search:
                queryset = queryset.filter(
                    Q(parking_number__icontains=search) |
                    Q(tenant_name__icontains=search) |
                    Q(phone__icontains=search) |
                    Q(license_plate__icontains=search) |
                    Q(building_number__icontains=search) |
                    Q(room_number__icontains=search)
                )

            return queryset.order_by('parking_number')
        except Exception as e:
            # 如果出错，返回空查询集
            return ParkingSpace.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            context['search_form'] = ParkingSearchForm(self.request.GET)

            queryset = self.get_queryset()
            total_count = queryset.count()
            context['total_count'] = total_count

            # 计算有所有者的车位数量
            owner_count = queryset.exclude(owner_name__isnull=True).exclude(owner_name__exact='').count()
            context['owner_count'] = owner_count

            # 计算活跃车位数（物业费未到期的）
            from django.utils import timezone
            today = timezone.now().date()
            active_parkings = queryset.filter(property_fee_due_date__gte=today).count()
            context['active_parkings'] = active_parkings

        except Exception as e:
            context['search_form'] = ParkingSearchForm()
            context['total_count'] = 0
            context['owner_count'] = 0
            context['active_parkings'] = 0

        return context


class ParkingCreateView(LoginRequiredMixin, CreateView):
    """车位创建视图"""
    model = ParkingSpace
    form_class = ParkingSpaceForm
    template_name = 'parking/form.html'
    success_url = reverse_lazy('parking:list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # 为新车位创建初始缴费记录
        parking = self.object

        # 计算从租车位时间到物业费到期时间的费用期间
        lease_start_date = parking.lease_start_date
        due_date = parking.property_fee_due_date

        # 使用正确的月数计算逻辑
        from dateutil.relativedelta import relativedelta

        # 计算月数：从租车位日期开始，每次加一个月，看需要多少个月才能到达或超过到期日期
        months_diff = 0
        current_date = lease_start_date

        while current_date <= due_date:
            months_diff += 1
            # 计算下一个月的同一天
            next_month_date = lease_start_date + relativedelta(months=months_diff)
            if next_month_date > due_date:
                break
            current_date = next_month_date

        if months_diff > 0:
            # 计算缴费金额（车位费固定每月20元）
            monthly_fee = Decimal('20.00')
            total_amount = monthly_fee * months_diff

            # 创建缴费记录
            ParkingPaymentHistory.objects.create(
                parking=parking,
                amount=total_amount,
                fee_start_date=lease_start_date,
                fee_end_date=due_date,
                payment_method='现金',
                notes=f'新车位租用缴费{months_diff}个月'
            )

        messages.success(self.request, '车位信息添加成功！')
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增车位'
        return context


class ParkingUpdateView(LoginRequiredMixin, UpdateView):
    """车位更新视图"""
    model = ParkingSpace
    form_class = ParkingSpaceForm
    template_name = 'parking/form.html'
    success_url = reverse_lazy('parking:list')

    def get_form(self, form_class=None):
        """重写get_form方法，确保日期字段正确显示"""
        form = super().get_form(form_class)

        # 确保日期字段在编辑时显示正确的值
        if self.object and self.object.pk:
            # 强制设置租车位时间
            if hasattr(self.object, 'lease_start_date') and self.object.lease_start_date:
                date_str = self.object.lease_start_date.strftime('%Y-%m-%d')
                form.fields['lease_start_date'].widget.attrs.update({'value': date_str})
                form.fields['lease_start_date'].initial = self.object.lease_start_date
                # 强制设置widget的值
                form.fields['lease_start_date'].widget.format = '%Y-%m-%d'
                form.fields['lease_start_date'].widget.attrs['data-date'] = date_str

            # 强制设置物业费到期时间
            if hasattr(self.object, 'property_fee_due_date') and self.object.property_fee_due_date:
                date_str = self.object.property_fee_due_date.strftime('%Y-%m-%d')
                form.fields['property_fee_due_date'].widget.attrs.update({'value': date_str})
                form.fields['property_fee_due_date'].initial = self.object.property_fee_due_date
                # 强制设置widget的值
                form.fields['property_fee_due_date'].widget.format = '%Y-%m-%d'
                form.fields['property_fee_due_date'].widget.attrs['data-date'] = date_str

        return form

    def form_valid(self, form):
        messages.success(self.request, '车位信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑车位'
        return context


class ParkingDeleteView(LoginRequiredMixin, DeleteView):
    """车位删除视图"""
    model = ParkingSpace
    template_name = 'parking/parking_confirm_delete.html'

    def get_success_url(self):
        """根据车位状态返回不同的页面"""
        parking = self.get_object()
        if parking.status == 'overdue':
            return reverse_lazy('parking:overdue')
        elif parking.status == 'checkout':
            return reverse_lazy('parking:checkout')
        else:
            return reverse_lazy('parking:list')

    def delete(self, request, *args, **kwargs):
        parking = self.get_object()
        messages.success(request, f'车位 {parking.parking_number} 删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueParkingListView(LoginRequiredMixin, ListView):
    """逾期车位列表视图"""
    model = ParkingSpace
    template_name = 'parking/overdue_list.html'
    context_object_name = 'parkings'
    paginate_by = 15

    def get_queryset(self):
        # 自动更新逾期状态
        today = timezone.now().date()
        ParkingSpace.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态（将已续费的逾期停车位状态改回正常）
        ParkingSpace.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = ParkingSpace.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(parking_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(license_plate__icontains=search) |
                Q(building_number__icontains=search) |
                Q(room_number__icontains=search)
            )

        return queryset.order_by('property_fee_due_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ParkingSearchForm(self.request.GET)

        # 计算逾期统计数据
        parkings = context['parkings']
        if parkings:
            short_overdue = sum(1 for parking in parkings if parking.overdue_days <= 7)
            long_overdue = sum(1 for parking in parkings if parking.overdue_days > 30)
        else:
            short_overdue = 0
            long_overdue = 0

        context['short_overdue'] = short_overdue
        context['long_overdue'] = long_overdue

        return context


class CheckoutParkingListView(LoginRequiredMixin, ListView):
    """退车位列表视图"""
    model = ParkingSpace
    template_name = 'parking/checkout_list.html'
    context_object_name = 'parkings'
    paginate_by = 15

    def get_queryset(self):
        queryset = ParkingSpace.objects.filter(status='checkout')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(parking_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(license_plate__icontains=search) |
                Q(building_number__icontains=search) |
                Q(room_number__icontains=search)
            )

        return queryset.order_by('-updated_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ParkingSearchForm(self.request.GET)

        # 计算退车位统计数据
        parkings = context['parkings']
        if parkings:
            from django.utils import timezone
            current_month = timezone.now().strftime('%Y-%m')
            this_month_count = sum(1 for parking in parkings
                                 if parking.updated_at.strftime('%Y-%m') == current_month)
            latest_checkout = parkings[0].updated_at if parkings else None
        else:
            this_month_count = 0
            latest_checkout = None

        context['this_month_checkout_count'] = this_month_count
        context['latest_checkout'] = latest_checkout

        return context


class RenewParkingView(LoginRequiredMixin, View):
    """车位续费视图"""
    template_name = 'parking/renew.html'

    def get(self, request, pk):
        parking = get_object_or_404(ParkingSpace, pk=pk)
        form = ParkingRenewForm()
        return render(request, self.template_name, {
            'parking': parking,
            'form': form
        })

    def post(self, request, pk):
        parking = get_object_or_404(ParkingSpace, pk=pk)
        form = ParkingRenewForm(request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data.get('notes', '')

            # 计算续费金额（车位费固定每月20元）
            monthly_fee = Decimal('20.00')
            total_amount = monthly_fee * months

            # 计算新的到期日期 - 按照新的计费逻辑
            current_due_date = parking.property_fee_due_date

            if relativedelta:
                # 使用dateutil进行精确的月份计算
                # 费用期间的开始日期：当前到期日期的下一天
                fee_start_date = current_due_date + relativedelta(days=1)

                # 费用期间的结束日期：从开始日期计算指定月数
                fee_end_date = fee_start_date + relativedelta(months=months) - relativedelta(days=1)

                # 新的物业费到期日期就是费用期间的结束日期
                new_due_date = fee_end_date
            else:
                # 简单的月份计算（如果没有dateutil）
                fee_start_date = current_due_date + timedelta(days=1)
                # 简单估算：每月30天
                fee_end_date = fee_start_date + timedelta(days=months * 30 - 1)
                new_due_date = fee_end_date

            # 创建缴费记录
            ParkingPaymentHistory.objects.create(
                parking=parking,
                amount=total_amount,
                fee_start_date=fee_start_date,
                fee_end_date=fee_end_date,
                payment_method=payment_method,
                notes=notes or f'续费{months}个月'
            )

            # 更新车位到期日期和状态
            parking.property_fee_due_date = new_due_date
            parking.status = 'active'
            parking.save()

            messages.success(request, f'车位 {parking.parking_number} 续费成功！续费{months}个月，新到期日期：{new_due_date}')
            return redirect('parking:list')

        return render(request, self.template_name, {
            'parking': parking,
            'form': form
        })


class CheckoutParkingView(LoginRequiredMixin, View):
    """车位退车视图"""

    def post(self, request, pk):
        parking = get_object_or_404(ParkingSpace, pk=pk)

        # 更新状态为已退车位
        parking.status = 'checkout'
        parking.save()

        messages.success(request, f'车位 {parking.parking_number} 已成功退车！')
        return redirect('parking:list')


class RestoreParkingView(LoginRequiredMixin, View):
    """恢复车位"""

    def post(self, request, pk):
        parking = get_object_or_404(ParkingSpace, pk=pk, status='checkout')

        # 恢复车位状态
        parking.status = 'active'
        parking.save()

        messages.success(request, f'车位 {parking.parking_number} 已成功恢复！')
        return redirect('parking:checkout')


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """车位缴费流水统计视图"""
    model = ParkingPaymentHistory
    template_name = 'parking/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 15

    def get_queryset(self):
        queryset = ParkingPaymentHistory.objects.select_related('parking')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(parking__parking_number__icontains=search) |
                Q(parking__tenant_name__icontains=search) |
                Q(parking__room_number__icontains=search) |
                Q(parking__phone__icontains=search) |
                Q(parking__license_plate__icontains=search) |
                Q(payment_method__icontains=search) |
                Q(notes__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ParkingPaymentHistorySearchForm(self.request.GET)

        # 计算统计数据
        queryset = self.get_queryset()

        if queryset.exists():
            # 总统计
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0

            # 本月统计
            now = timezone.now()
            local_now = timezone.localtime(now)

            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month_start = this_month_start.replace(month=local_now.month + 1) if local_now.month < 12 else this_month_start.replace(year=local_now.year + 1, month=1)

            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 上月统计
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context


class ExportParkingView(LoginRequiredMixin, View):
    """导出车位数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "车位列表"

        # 设置表头
        headers = [
            '车位号', '房号', '租户姓名', '电话', '车牌号', '车位所有者', '所有者电话',
            '租车位时间', '物业费到期时间', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据 - 只导出正常状态的车位
        parkings = ParkingSpace.objects.filter(status='active').order_by('parking_number')

        # 写入数据
        for row, parking in enumerate(parkings, 2):
            ws.cell(row=row, column=1, value=parking.parking_number)
            ws.cell(row=row, column=2, value=parking.room_number)
            ws.cell(row=row, column=3, value=parking.tenant_name)
            ws.cell(row=row, column=4, value=parking.phone)
            ws.cell(row=row, column=5, value=parking.license_plate)
            ws.cell(row=row, column=6, value=parking.owner_name or '')
            ws.cell(row=row, column=7, value=parking.owner_phone or '')
            ws.cell(row=row, column=8, value=parking.lease_start_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=9, value=parking.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=10, value=parking.get_status_display())
            # 将时区感知的datetime转换为本地时间
            local_created_at = timezone.localtime(parking.created_at)
            ws.cell(row=row, column=11, value=local_created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="车位列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ExportOverdueParkingView(LoginRequiredMixin, View):
    """导出逾期车位数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "逾期车位列表"

        # 设置表头
        headers = [
            '车位号', '房号', '租户姓名', '电话', '车牌号', '车位所有者', '所有者电话',
            '物业费到期时间', '已逾期天数'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

        # 获取逾期车位数据
        today = timezone.now().date()
        ParkingSpace.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        parkings = ParkingSpace.objects.filter(status='overdue').order_by('property_fee_due_date')

        # 写入数据
        for row, parking in enumerate(parkings, 2):
            ws.cell(row=row, column=1, value=parking.parking_number)
            ws.cell(row=row, column=2, value=parking.room_number)
            ws.cell(row=row, column=3, value=parking.tenant_name)
            ws.cell(row=row, column=4, value=parking.phone)
            ws.cell(row=row, column=5, value=parking.license_plate)
            ws.cell(row=row, column=6, value=parking.owner_name or '')
            ws.cell(row=row, column=7, value=parking.owner_phone or '')
            ws.cell(row=row, column=8, value=parking.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=9, value=parking.overdue_days)

            # 根据逾期天数设置行颜色
            if parking.overdue_days > 30:
                fill_color = 'FFCCCC'  # 深红色
            elif parking.overdue_days > 7:
                fill_color = 'FFE6CC'  # 橙色
            else:
                fill_color = 'FFFFCC'  # 浅黄色

            for col in range(1, 10):
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color=fill_color, end_color=fill_color, fill_type='solid')

        # 调整列宽
        column_widths = [12, 8, 12, 15, 12, 12, 15, 15, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="逾期车位列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ExportCheckoutParkingView(LoginRequiredMixin, View):
    """导出退车位数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "退车位列表"

        # 设置表头
        headers = [
            '车位号', '房号', '租户姓名', '电话', '车牌号', '车位所有者', '所有者电话',
            '租车位时间', '退车位日期'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='E6E6E6', end_color='E6E6E6', fill_type='solid')

        # 获取退车位数据
        parkings = ParkingSpace.objects.filter(status='checkout').order_by('-updated_at')

        # 写入数据
        for row, parking in enumerate(parkings, 2):
            ws.cell(row=row, column=1, value=parking.parking_number)
            ws.cell(row=row, column=2, value=parking.room_number)
            ws.cell(row=row, column=3, value=parking.tenant_name)
            ws.cell(row=row, column=4, value=parking.phone)
            ws.cell(row=row, column=5, value=parking.license_plate)
            ws.cell(row=row, column=6, value=parking.owner_name or '')
            ws.cell(row=row, column=7, value=parking.owner_phone or '')
            ws.cell(row=row, column=8, value=parking.lease_start_date.strftime('%Y-%m-%d'))
            # 将时区感知的datetime转换为本地时间
            local_updated_at = timezone.localtime(parking.updated_at)
            ws.cell(row=row, column=9, value=local_updated_at.strftime('%Y-%m-%d'))

        # 调整列宽
        column_widths = [12, 8, 12, 15, 12, 12, 15, 12, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="退车位列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ExportParkingPaymentHistoryView(LoginRequiredMixin, View):
    """导出车位缴费流水"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "车位缴费流水"

        # 设置表头
        headers = [
            '缴费时间', '车位号', '房号', '租户姓名', '缴费金额', '费用期间', '缴费方式', '备注'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        payments = ParkingPaymentHistory.objects.select_related('parking')

        # 应用与列表页面相同的筛选条件
        # 搜索功能
        search = request.GET.get('search')
        if search:
            payments = payments.filter(
                Q(parking__parking_number__icontains=search) |
                Q(parking__tenant_name__icontains=search) |
                Q(parking__room_number__icontains=search) |
                Q(parking__phone__icontains=search) |
                Q(parking__license_plate__icontains=search) |
                Q(payment_method__icontains=search) |
                Q(notes__icontains=search)
            )

        # 日期筛选
        start_date = request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                # 设置为当天的23:59:59
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        payments = payments.order_by('-payment_date')

        # 写入数据
        for row, payment in enumerate(payments, 2):
            # 将时区感知的datetime转换为本地时间
            local_payment_date = timezone.localtime(payment.payment_date)

            ws.cell(row=row, column=1, value=local_payment_date.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=2, value=payment.parking.parking_number)
            ws.cell(row=row, column=3, value=payment.parking.room_number)
            ws.cell(row=row, column=4, value=payment.parking.tenant_name)
            ws.cell(row=row, column=5, value=float(payment.amount))
            ws.cell(row=row, column=6, value=f"{payment.fee_start_date} 至 {payment.fee_end_date}")
            ws.cell(row=row, column=7, value=payment.payment_method)
            ws.cell(row=row, column=8, value=payment.notes or '')

        # 调整列宽
        column_widths = [20, 12, 15, 12, 12, 25, 12, 30]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(i)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # 生成文件名，包含时间范围信息
        filename = "车位缴费流水"
        if start_date and end_date:
            filename += f"_{start_date}至{end_date}"
        elif start_date:
            filename += f"_{start_date}起"
        elif end_date:
            filename += f"_至{end_date}"
        filename += f"_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # 保存到响应
        wb.save(response)
        return response


class BatchDeleteParkingPaymentView(LoginRequiredMixin, View):
    """批量删除车位缴费记录"""

    def post(self, request):
        payment_ids = request.POST.getlist('payment_ids')

        if not payment_ids:
            messages.error(request, '请选择要删除的缴费记录')
            return redirect('parking:payment_history')

        try:
            # 删除选中的缴费记录
            deleted_count = ParkingPaymentHistory.objects.filter(
                id__in=payment_ids
            ).delete()[0]

            messages.success(request, f'成功删除 {deleted_count} 条缴费记录')
        except Exception as e:
            messages.error(request, f'删除失败：{str(e)}')

        return redirect('parking:payment_history')


class ImportParkingView(LoginRequiredMixin, View):
    """导入车位数据"""
    template_name = 'parking/import.html'

    def get(self, request):
        form = ParkingImportForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = ParkingImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']
            skip_duplicates = request.POST.get('skip_duplicates') == 'on'

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                skip_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:4]):  # 如果前4列都为空，跳过这行
                            continue

                        # 解析数据 - 按照表头格式：车位号、房号、租户姓名、电话、车牌号、车位所有者、所有者电话、租车位时间、物业费到期时间
                        parking_number = str(row[0]) if row[0] else ''
                        room_number = str(row[1]) if row[1] else ''
                        tenant_name = str(row[2]) if row[2] else ''
                        phone = str(row[3]) if row[3] else ''
                        license_plate = str(row[4]) if row[4] else ''
                        owner_name = str(row[5]) if row[5] else ''
                        owner_phone = str(row[6]) if row[6] else ''

                        # 验证必填字段
                        if not phone:
                            errors.append(f'第{row_num}行: 电话不能为空')
                            error_count += 1
                            continue

                        if not parking_number:
                            errors.append(f'第{row_num}行: 车位号不能为空')
                            error_count += 1
                            continue

                        # 解析日期
                        lease_start_date = row[7] if row[7] else timezone.now().date()
                        if isinstance(lease_start_date, str):
                            lease_start_date = datetime.strptime(lease_start_date, '%Y-%m-%d').date()
                        elif isinstance(lease_start_date, datetime):
                            lease_start_date = lease_start_date.date()

                        property_fee_due_date = row[8] if row[8] else timezone.now().date() + timedelta(days=365)
                        if isinstance(property_fee_due_date, str):
                            property_fee_due_date = datetime.strptime(property_fee_due_date, '%Y-%m-%d').date()
                        elif isinstance(property_fee_due_date, datetime):
                            property_fee_due_date = property_fee_due_date.date()

                        # 检查重复记录
                        if skip_duplicates:
                            existing = ParkingSpace.objects.filter(
                                parking_number=parking_number
                            ).exists()
                            if existing:
                                skip_count += 1
                                continue

                        # 创建车位记录
                        parking = ParkingSpace(
                            parking_number=parking_number,
                            building_number='',  # 楼号字段设为空，因为新结构不使用楼号
                            room_number=room_number,
                            tenant_name=tenant_name,
                            phone=phone,
                            license_plate=license_plate,
                            owner_name=owner_name,
                            owner_phone=owner_phone,
                            lease_start_date=lease_start_date,
                            property_fee_due_date=property_fee_due_date,
                            status='active'
                        )
                        parking.save()
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                # 显示导入结果
                result_messages = []
                if success_count > 0:
                    result_messages.append(f'成功导入 {success_count} 条记录')
                if skip_count > 0:
                    result_messages.append(f'跳过重复记录 {skip_count} 条')
                if error_count > 0:
                    result_messages.append(f'导入失败 {error_count} 条记录')

                if success_count > 0:
                    messages.success(request, '；'.join(result_messages) + '！')
                elif skip_count > 0:
                    messages.warning(request, '；'.join(result_messages) + '！')

                if error_count > 0:
                    error_msg = '导入错误详情：\n' + '\n'.join(errors[:5])
                    if len(errors) > 5:
                        error_msg += f'\n... 还有 {len(errors) - 5} 个错误'
                    messages.error(request, error_msg)

                return redirect('parking:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})


class DownloadParkingTemplateView(LoginRequiredMixin, View):
    """下载车位导入模板"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "车位导入模板"

        # 设置表头
        headers = [
            '车位号', '房号', '租户姓名', '电话', '车牌号', '车位所有者', '所有者电话', '租车位时间', '物业费到期时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 添加示例数据
        example_data = [
            ['P001', '3-1001', '张三', '13800138001', '京A12345', '王五', '13900139001', '2024-01-01', '2024-12-31'],
            ['P002', '9-1-1002', '李四', '13800138002', '京B67890', '赵六', '13900139002', '2024-02-01', '2025-01-31'],
        ]

        for row_num, row_data in enumerate(example_data, 2):
            for col_num, value in enumerate(row_data, 1):
                ws.cell(row=row_num, column=col_num, value=value)

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="车位导入模板.xlsx"'

        # 保存到响应
        wb.save(response)
        return response
