# Generated by Django 5.2.3

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='IncomeSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='收入来源')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '收入来源',
                'verbose_name_plural': '收入来源',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='OtherIncomeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('income_source', models.CharField(choices=[('water_fee', '水费'), ('storage_electricity', '储藏间电费'), ('water_purifier_fee', '净水机费'), ('water_card', '净水卡'), ('water_bucket_fee', '水桶费'), ('shop_water_fee', '商铺水费'), ('water_card_supplement', '补自来水卡费'), ('water_purifier_card', '补净水机卡费'), ('parking_fee', '停车场收费'), ('elevator_card_fee', '电梯梯控卡费'), ('other', '其他')], max_length=50, verbose_name='收入来源')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='金额')),
                ('income_date', models.DateField(default=django.utils.timezone.now, verbose_name='收入日期')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('water_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='水费')),
                ('storage_electricity_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='储藏间电费')),
                ('water_purifier_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净水机费')),
                ('water_card_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='净水卡费')),
                ('water_bucket_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='水桶费')),
                ('shop_water_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='商铺水费')),
                ('water_card_supplement_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='补自来水卡费')),
                ('water_purifier_card_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='补净水机卡费')),
                ('parking_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='停车场收费')),
                ('elevator_card_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='电梯梯控卡费')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '其它收入记录',
                'verbose_name_plural': '其它收入记录',
                'ordering': ['-income_date', '-created_at'],
            },
        ),
    ]
