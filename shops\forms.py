from django import forms
from .models import Shop, ShopPaymentHistory


class ShopForm(forms.ModelForm):
    """商铺表单"""

    class Meta:
        model = Shop
        fields = [
            'shop_number', 'door_number', 'tenant_name', 'phone', 'id_card',
            'area', 'owner_name', 'owner_phone', 'lease_start_date', 'property_fee_due_date'
        ]
        
        widgets = {
            'shop_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入商铺号',
                'id': 'id_shop_number'
            }),
            'door_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入门牌号',
                'id': 'id_door_number'
            }),
            'tenant_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入租户或业主姓名'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入电话号码，多个号码用逗号分隔'
            }),
            'id_card': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入身份证号（可选）'
            }),
            'area': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '平米数',
                'step': '0.01',
                'id': 'id_area'
            }),
            'owner_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入业主姓名（可选）'
            }),
            'owner_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入业主电话（可选）'
            }),
            'lease_start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'property_fee_due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 根据商铺号自动填充门牌号和平米数
        if not self.instance.pk:  # 新增时
            self.fields['shop_number'].widget.attrs.update({
                'onchange': 'autoFillShopInfo(this.value)'
            })
        else:
            # 编辑时手动设置初始值和widget的value
            if self.instance.lease_start_date:
                self.fields['lease_start_date'].initial = self.instance.lease_start_date
                self.fields['lease_start_date'].widget.attrs['value'] = self.instance.lease_start_date.strftime('%Y-%m-%d')
            if self.instance.property_fee_due_date:
                self.fields['property_fee_due_date'].initial = self.instance.property_fee_due_date
                self.fields['property_fee_due_date'].widget.attrs['value'] = self.instance.property_fee_due_date.strftime('%Y-%m-%d')

    def clean_shop_number(self):
        shop_number = self.cleaned_data.get('shop_number')

        if shop_number:
            # 检查是否存在相同商铺号且未退房的商铺
            existing_shop = Shop.objects.filter(
                shop_number=shop_number
            ).exclude(status='checkout')

            # 如果是编辑模式，排除当前商铺
            if self.instance.pk:
                existing_shop = existing_shop.exclude(pk=self.instance.pk)

            if existing_shop.exists():
                shop = existing_shop.first()
                if shop.status == 'active':
                    raise forms.ValidationError(f'商铺号 {shop_number} 已存在且正在使用中，请先办理退房手续')
                elif shop.status == 'overdue':
                    raise forms.ValidationError(f'商铺号 {shop_number} 已存在且处于逾期状态，请先办理退房手续')
                else:
                    raise forms.ValidationError(f'商铺号 {shop_number} 已存在，请使用其他商铺号')

        return shop_number


class ShopSearchForm(forms.Form):
    """商铺搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索商铺号、租户姓名、电话...',
            'name': 'search'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', '全部状态')] + Shop.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )


class ShopRenewForm(forms.Form):
    """商铺续费表单"""
    months = forms.IntegerField(
        label='续费月数',
        min_value=1,
        max_value=24,
        initial=12,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入续费月数'
        })
    )

    payment_method = forms.ChoiceField(
        label='缴费方式',
        choices=[
            ('现金', '现金'),
            ('银行转账', '银行转账'),
            ('支付宝', '支付宝'),
            ('微信', '微信'),
            ('刷卡', '刷卡'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    notes = forms.CharField(
        label='备注',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '备注信息（可选）'
        })
    )


class ShopPaymentForm(forms.ModelForm):
    """商铺缴费表单"""
    months = forms.IntegerField(
        label='缴费月数',
        min_value=1,
        max_value=24,
        initial=12,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入缴费月数'
        })
    )

    class Meta:
        model = ShopPaymentHistory
        fields = ['payment_method', 'notes']

        widgets = {
            'payment_method': forms.Select(
                choices=[
                    ('现金', '现金'),
                    ('银行转账', '银行转账'),
                    ('支付宝', '支付宝'),
                    ('微信', '微信'),
                    ('刷卡', '刷卡'),
                ],
                attrs={'class': 'form-select'}
            ),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '备注信息（可选）'
            }),
        }

    def __init__(self, shop=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if shop:
            # 计算缴费金额
            monthly_fee = shop.calculate_property_fee()
            self.fields['amount'] = forms.DecimalField(
                label='缴费金额',
                initial=monthly_fee * 12,
                widget=forms.NumberInput(attrs={
                    'class': 'form-control',
                    'readonly': True
                })
            )


class ShopImportForm(forms.Form):
    """商铺导入表单"""
    file = forms.FileField(
        label='Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls'
        })
    )


class ShopPaymentHistorySearchForm(forms.Form):
    """商铺缴费流水搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索商铺号、租户姓名...'
        })
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
