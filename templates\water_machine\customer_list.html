{% extends 'base/base.html' %}
{% load static %}

{% block title %}客户管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-users text-primary me-2"></i>
            客户管理
        </h2>
        <div>
            <a href="{% url 'water_machine:index' %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>返回首页
            </a>
            <a href="{% url 'water_machine:customer_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>新增客户
            </a>
        </div>
    </div>

    <!-- 搜索表单 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        {{ search_form.search }}
                    </div>
                </div>
                <div class="col-md-3">
                    {{ search_form.water_bucket }}
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{% url 'water_machine:customer_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i>重置
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 客户列表 -->
    {% if customers %}
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>姓名</th>
                                <th>住址</th>
                                <th>电话</th>
                                <th>水桶</th>
                                <th>水卡状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>{{ customer.name }}</td>
                                <td>{{ customer.address }}</td>
                                <td>{{ customer.phone }}</td>
                                <td>
                                    {% if customer.water_bucket %}
                                        <span class="badge bg-success">有水桶</span>
                                    {% else %}
                                        <span class="badge bg-secondary">无水桶</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% with active_card=customer.get_active_water_card %}
                                        {% if active_card %}
                                            <span class="badge bg-success">{{ active_card.card_number }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">未分配水卡</span>
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td>{{ customer.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'water_machine:customer_edit' customer.pk %}" class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'water_machine:customer_delete' customer.pk %}" class="btn btn-outline-danger" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if is_paginated %}
                <nav aria-label="分页导航" class="mt-3">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.water_bucket %}&water_bucket={{ request.GET.water_bucket }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.water_bucket %}&water_bucket={{ request.GET.water_bucket }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.water_bucket %}&water_bucket={{ request.GET.water_bucket }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.water_bucket %}&water_bucket={{ request.GET.water_bucket }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无客户信息</h5>
                <p class="text-muted">点击上方"新增客户"按钮添加第一个客户</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }
</style>
{% endblock %}
