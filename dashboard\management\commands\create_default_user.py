from django.core.management.base import BaseCommand
from django.contrib.auth.models import User


class Command(BaseCommand):
    help = '创建默认用户'

    def handle(self, *args, **options):
        username = '东悦物业'
        password = '123456'
        
        if not User.objects.filter(username=username).exists():
            User.objects.create_user(
                username=username,
                password=password,
                is_staff=True,
                is_superuser=True
            )
            self.stdout.write(
                self.style.SUCCESS(f'成功创建用户: {username}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'用户 {username} 已存在')
            )
