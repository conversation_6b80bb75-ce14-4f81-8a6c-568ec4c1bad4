{% extends "base/base.html" %}

{% block title %}
    {% if object %}编辑租户售水机卡号记录{% else %}新增租户售水机卡号记录{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-id-card text-primary me-2"></i>
            {% if object %}编辑租户售水机卡号记录{% else %}新增租户售水机卡号记录{% endif %}
        </h2>
        <a href="{% url 'water_machine:tenant_water_card_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>

    <!-- 表单 -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        {% if object %}编辑记录信息{% else %}填写记录信息{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- 基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    {{ form.phone.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.phone.errors.0 }}
                                    </div>
                                {% endif %}
                                <div class="form-text">请输入11位手机号码</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    {{ form.address.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.address.errors.0 }}
                                    </div>
                                {% endif %}
                                <div class="form-text">住址不能与其他租户重复</div>
                            </div>
                            <div class="col-md-4">
                                <label for="{{ form.water_bucket.id_for_label }}" class="form-label">
                                    {{ form.water_bucket.label }}
                                </label>
                                {{ form.water_bucket }}
                                {% if form.water_bucket.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.water_bucket.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 水卡信息 -->
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-credit-card me-1"></i>水卡信息
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label for="{{ form.normal_card_number.id_for_label }}" class="form-label">
                                            {{ form.normal_card_number.label }}
                                        </label>
                                        {{ form.normal_card_number }}
                                        {% if form.normal_card_number.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.normal_card_number.errors.0 }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">正在使用的水卡号码</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <label for="{{ form.lost_card_numbers.id_for_label }}" class="form-label">
                                            {{ form.lost_card_numbers.label }}
                                        </label>
                                        {{ form.lost_card_numbers }}
                                        {% if form.lost_card_numbers.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.lost_card_numbers.errors.0 }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">多个卡号用逗号分隔，例如：12345678,87654321</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 表单错误 -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}

                        <!-- 提交按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'water_machine:tenant_water_card_list' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            {% if object and object.normal_card_number %}
                                <a href="{% url 'water_machine:tenant_water_card_lost' object.pk %}" class="btn btn-outline-warning me-md-2">
                                    <i class="fas fa-exclamation-triangle me-1"></i>挂失水卡
                                </a>
                            {% endif %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if object %}更新记录{% else %}创建记录{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
