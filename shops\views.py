from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
import openpyxl.utils

from .models import Shop, ShopPaymentHistory
from .forms import ShopForm, ShopSearchForm, ShopPaymentForm, ShopRenewForm, ShopImportForm, ShopPaymentHistorySearchForm


class ShopListView(LoginRequiredMixin, ListView):
    """商铺列表视图"""
    model = Shop
    template_name = 'shops/list.html'
    context_object_name = 'shops'
    paginate_by = 15

    def get_queryset(self):
        try:
            # 自动更新逾期状态
            today = timezone.now().date()
            Shop.objects.filter(
                property_fee_due_date__lt=today,
                status='active'
            ).update(status='overdue')

            # 自动更新正常状态
            Shop.objects.filter(
                property_fee_due_date__gte=today,
                status='overdue'
            ).update(status='active')

            queryset = Shop.objects.filter(status='active')

            # 搜索功能
            search = self.request.GET.get('search')
            if search:
                queryset = queryset.filter(
                    Q(shop_number__icontains=search) |
                    Q(tenant_name__icontains=search) |
                    Q(phone__icontains=search) |
                    Q(id_card__icontains=search) |
                    Q(door_number__icontains=search)
                )

            return queryset.order_by('shop_number')
        except Exception as e:
            # 如果出错，返回空查询集
            return Shop.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            context['search_form'] = ShopSearchForm(self.request.GET)

            queryset = self.get_queryset()
            total_count = queryset.count()
            context['total_count'] = total_count

            # 计算总面积
            from django.db.models import Sum
            total_area = queryset.aggregate(total=Sum('area'))['total'] or 0
            context['total_area'] = total_area

            # 计算活跃商铺数（物业费未到期的）
            from django.utils import timezone
            today = timezone.now().date()
            active_shops = queryset.filter(property_fee_due_date__gte=today).count()
            context['active_shops'] = active_shops

        except Exception as e:
            context['search_form'] = ShopSearchForm()
            context['total_count'] = 0
            context['total_area'] = 0
            context['active_shops'] = 0

        return context


class ShopCreateView(LoginRequiredMixin, CreateView):
    """商铺创建视图"""
    model = Shop
    form_class = ShopForm
    template_name = 'shops/form.html'
    success_url = reverse_lazy('shops:list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # 为新商铺创建初始缴费记录
        shop = self.object

        # 计算从起租时间到物业费到期时间的费用期间
        lease_start_date = shop.lease_start_date
        due_date = shop.property_fee_due_date

        # 使用正确的月数计算逻辑
        from dateutil.relativedelta import relativedelta

        # 计算月数：从起租日期开始，每次加一个月，看需要多少个月才能到达或超过到期日期
        months_diff = 0
        current_date = lease_start_date

        while current_date <= due_date:
            months_diff += 1
            # 计算下一个月的同一天
            next_month_date = lease_start_date + relativedelta(months=months_diff)
            if next_month_date > due_date:
                break
            current_date = next_month_date

        if months_diff > 0:
            # 计算缴费金额
            monthly_fee = shop.calculate_property_fee()
            total_amount = monthly_fee * months_diff

            # 创建缴费记录
            ShopPaymentHistory.objects.create(
                shop=shop,
                amount=total_amount,
                fee_start_date=lease_start_date,
                fee_end_date=due_date,
                payment_method='现金',
                notes=f'新商铺入驻缴费{months_diff}个月'
            )

        messages.success(self.request, '商铺信息添加成功！')
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增商铺'
        return context


class ShopUpdateView(LoginRequiredMixin, UpdateView):
    """商铺更新视图"""
    model = Shop
    form_class = ShopForm
    template_name = 'shops/form.html'
    success_url = reverse_lazy('shops:list')

    def get_form(self, form_class=None):
        """重写get_form方法，确保日期字段正确显示"""
        form = super().get_form(form_class)

        # 确保日期字段在编辑时显示正确的值
        if self.object and self.object.pk:
            if self.object.lease_start_date:
                form.fields['lease_start_date'].widget.attrs['value'] = self.object.lease_start_date.strftime('%Y-%m-%d')
            if self.object.property_fee_due_date:
                form.fields['property_fee_due_date'].widget.attrs['value'] = self.object.property_fee_due_date.strftime('%Y-%m-%d')

        return form

    def form_valid(self, form):
        messages.success(self.request, '商铺信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑商铺'
        return context


class ShopDeleteView(LoginRequiredMixin, DeleteView):
    """商铺删除视图"""
    model = Shop
    template_name = 'shops/shop_confirm_delete.html'

    def get_success_url(self):
        """根据商铺状态和来源页面返回不同的页面"""
        shop = self.get_object()

        # 检查是否有next参数
        next_url = self.request.GET.get('next')
        if next_url:
            return next_url

        # 检查HTTP_REFERER来判断来源页面
        referer = self.request.META.get('HTTP_REFERER', '')
        if 'overdue' in referer or shop.status == 'overdue':
            return reverse_lazy('shops:overdue')
        elif 'checkout' in referer or shop.status == 'checkout':
            return reverse_lazy('shops:checkout')
        else:
            return reverse_lazy('shops:list')

    def delete(self, request, *args, **kwargs):
        shop = self.get_object()
        messages.success(request, f'商铺 {shop.shop_number} 删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueShopListView(LoginRequiredMixin, ListView):
    """逾期商铺列表视图"""
    model = Shop
    template_name = 'shops/overdue_list.html'
    context_object_name = 'shops'
    paginate_by = 15

    def get_queryset(self):
        # 自动更新逾期状态
        today = timezone.now().date()
        Shop.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态（将已续费的逾期商铺状态改回正常）
        Shop.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = Shop.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(id_card__icontains=search)
            )

        return queryset.order_by('property_fee_due_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ShopSearchForm(self.request.GET)

        # 计算逾期统计数据
        shops = context['shops']
        if shops:
            short_overdue = sum(1 for shop in shops if shop.overdue_days <= 7)
            long_overdue = sum(1 for shop in shops if shop.overdue_days > 30)
        else:
            short_overdue = 0
            long_overdue = 0

        context['short_overdue'] = short_overdue
        context['long_overdue'] = long_overdue

        return context


class ShopRenewView(LoginRequiredMixin, View):
    """商铺续费视图"""
    template_name = 'shops/renew.html'

    def get(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk)
        form = ShopRenewForm()
        return render(request, self.template_name, {
            'shop': shop,
            'form': form
        })

    def post(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk)
        form = ShopRenewForm(request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data.get('notes', '')

            # 计算续费金额
            monthly_fee = shop.calculate_property_fee()
            total_amount = monthly_fee * months

            # 计算新的到期日期 - 按照新的计费逻辑
            from dateutil.relativedelta import relativedelta
            current_due_date = shop.property_fee_due_date

            # 费用期间的开始日期：当前到期日期的下一天
            # 例如：如果到期日期是2025年12月31日，费用期间从2026年1月1日开始
            fee_start_date = current_due_date + relativedelta(days=1)

            # 费用期间的结束日期：从开始日期计算指定月数
            # 例如：从2026年1月1日开始，续费12个月，结束日期是2026年12月31日
            fee_end_date = fee_start_date + relativedelta(months=months) - relativedelta(days=1)

            # 新的物业费到期日期就是费用期间的结束日期
            new_due_date = fee_end_date

            # 创建缴费记录
            ShopPaymentHistory.objects.create(
                shop=shop,
                amount=total_amount,
                fee_start_date=fee_start_date,
                fee_end_date=fee_end_date,
                payment_method=payment_method,
                notes=notes or f'续费{months}个月'
            )

            # 更新商铺到期日期和状态
            shop.property_fee_due_date = new_due_date
            shop.status = 'active'
            shop.save()

            messages.success(request, f'商铺 {shop.shop_number} 续费成功！续费{months}个月，新到期日期：{new_due_date}')
            return redirect('shops:list')

        return render(request, self.template_name, {
            'shop': shop,
            'form': form
        })


class CheckoutShopListView(LoginRequiredMixin, ListView):
    """退房商铺列表视图"""
    model = Shop
    template_name = 'shops/checkout_list.html'
    context_object_name = 'shops'
    paginate_by = 15

    def get_queryset(self):
        queryset = Shop.objects.filter(status='checkout')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(id_card__icontains=search)
            )

        return queryset.order_by('-updated_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ShopSearchForm(self.request.GET)

        # 计算退房统计数据
        shops = context['shops']
        if shops:
            from django.utils import timezone
            current_month = timezone.now().strftime('%Y-%m')
            this_month_count = sum(1 for shop in shops
                                 if shop.updated_at.strftime('%Y-%m') == current_month)
            latest_checkout = shops[0].updated_at if shops else None
        else:
            this_month_count = 0
            latest_checkout = None

        context['this_month_checkout_count'] = this_month_count
        context['latest_checkout'] = latest_checkout

        return context


class CheckoutShopView(LoginRequiredMixin, View):
    """商铺退房视图"""

    def post(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk)

        # 更新状态为已退房
        shop.status = 'checkout'
        shop.save()

        messages.success(request, f'商铺 {shop.shop_number} 已成功退房！')
        return redirect('shops:list')


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """商铺缴费流水视图"""
    model = ShopPaymentHistory
    template_name = 'shops/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = ShopPaymentHistory.objects.select_related('shop')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop__shop_number__icontains=search) |
                Q(shop__tenant_name__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ShopPaymentHistorySearchForm(self.request.GET)

        # 计算统计数据
        queryset = self.get_queryset()

        if queryset.exists():
            # 总统计
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0

            # 本月统计
            now = timezone.now()
            local_now = timezone.localtime(now)

            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month_start = this_month_start.replace(month=local_now.month + 1) if local_now.month < 12 else this_month_start.replace(year=local_now.year + 1, month=1)

            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 上月统计
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context


class GetShopInfoView(LoginRequiredMixin, View):
    """获取商铺信息API"""

    def get(self, request):
        shop_number = request.GET.get('shop_number', '')

        # 根据您提供的数据创建商铺信息映射
        shop_data = {
            '1-101': {'door_number': '顺安路555-1', 'area': 74.85},
            '1-102': {'door_number': '顺安路555-3', 'area': 98.64},
            '1-103': {'door_number': '顺安路555-5', 'area': 98.51},
            '1-104': {'door_number': '顺安路555-7', 'area': 74.85},
            '2-101': {'door_number': '顺安路555-9', 'area': 74.72},
            '2-102': {'door_number': '顺安路555-11', 'area': 98.47},
            '2-103': {'door_number': '顺安路555-13', 'area': 95.29},
            '2-104': {'door_number': '顺安路555-15', 'area': 74.72},
            '2-105': {'door_number': '顺安路555-17', 'area': 65.03},
            '2-106': {'door_number': '顺安路555-19', 'area': 64.88},
            '3-101': {'door_number': '顺安路555-21', 'area': 64.89},
            '3-102': {'door_number': '顺安路555-23', 'area': 65.04},
            '3-103': {'door_number': '顺安路555-25', 'area': 74.74},
            '3-104': {'door_number': '顺安路555-27', 'area': 98.49},
            '3-105': {'door_number': '顺安路555-29', 'area': 98.37},
            '3-106': {'door_number': '顺安路555-31', 'area': 74.39},
            '8-3888': {'door_number': '七一东路3888', 'area': 258.98},
            '8-3888-1': {'door_number': '七一东路3888-1', 'area': 132.42},
            '8-3888-3': {'door_number': '七一东路3888-3', 'area': 259.14},
            '8-3888-5': {'door_number': '七一东路3888-5', 'area': 233.5},
            '8-3888-7': {'door_number': '七一东路3888-7', 'area': 233.5},
            '8-3888-9': {'door_number': '七一东路3888-9', 'area': 233.5},
            '8-3888-11': {'door_number': '七一东路3888-11', 'area': 233.5},
            '9-101': {'door_number': '', 'area': 131.9},
            '9-102': {'door_number': '', 'area': 83.33},
            '9-103': {'door_number': '', 'area': 83.27},
            '9-104': {'door_number': '', 'area': 135.84},
            '9-105': {'door_number': '', 'area': 135.84},
            '9-106': {'door_number': '', 'area': 83.27},
            '9-107': {'door_number': '', 'area': 83.33},
            '9-108': {'door_number': '', 'area': 131.9},
          

        }

        if shop_number in shop_data:
            data = shop_data[shop_number]
            # 计算物业费（每平米1.5元）
            property_fee = float(data['area']) * 1.5

            return JsonResponse({
                'success': True,
                'door_number': data['door_number'],
                'area': data['area'],
                'property_fee': round(property_fee, 2)
            })
        else:
            return JsonResponse({
                'success': False,
                'message': '未找到该商铺号的信息'
            })


class ExportShopView(LoginRequiredMixin, View):
    """导出商铺数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商铺列表"

        # 设置表头
        headers = [
            '商铺号', '门牌号', '租户姓名', '电话', '身份证号', '平米数',
            '业主姓名', '业主电话', '起租时间', '物业费到期时间', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据 - 只导出正常状态的商铺（商铺租房管理页面显示的商铺）
        shops = Shop.objects.filter(status='active').order_by('shop_number')

        # 写入数据
        for row, shop in enumerate(shops, 2):
            ws.cell(row=row, column=1, value=shop.shop_number)
            ws.cell(row=row, column=2, value=shop.door_number or '')
            ws.cell(row=row, column=3, value=shop.tenant_name)
            ws.cell(row=row, column=4, value=shop.phone)
            ws.cell(row=row, column=5, value=shop.id_card or '')
            ws.cell(row=row, column=6, value=float(shop.area))
            ws.cell(row=row, column=7, value=getattr(shop, 'owner_name', '') or '')
            ws.cell(row=row, column=8, value=getattr(shop, 'owner_phone', '') or '')
            ws.cell(row=row, column=9, value=shop.lease_start_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=10, value=shop.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=11, value=shop.get_status_display())
            # 将时区感知的datetime转换为本地时间
            local_created_at = timezone.localtime(shop.created_at)
            ws.cell(row=row, column=12, value=local_created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="商铺列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response



class ShopPaymentHistoryListView(LoginRequiredMixin, ListView):
    """商铺缴费流水视图"""
    model = ShopPaymentHistory
    template_name = 'shops/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = ShopPaymentHistory.objects.select_related('shop')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop__shop_number__icontains=search) |
                Q(shop__tenant_name__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ShopPaymentHistorySearchForm(self.request.GET)

        # 计算统计数据
        queryset = self.get_queryset()

        if queryset.exists():
            # 总统计
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0

            # 本月统计
            now = timezone.now()
            local_now = timezone.localtime(now)

            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month_start = this_month_start.replace(month=local_now.month + 1) if local_now.month < 12 else this_month_start.replace(year=local_now.year + 1, month=1)

            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 上月统计
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context



class RestoreShopView(LoginRequiredMixin, View):
    """恢复商铺"""

    def post(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk, status='checkout')

        # 恢复商铺状态
        shop.status = 'active'
        shop.save()

        messages.success(request, f'商铺 {shop.shop_number} 已成功恢复！')
        return redirect('shops:checkout')




class ExportOverdueShopView(LoginRequiredMixin, View):
    """导出逾期商铺数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "逾期商铺列表"

        # 设置表头
        headers = [
            '商铺号', '门牌号', '租户姓名', '身份证号', '租户电话',
            '平米数', '业主姓名', '业主电话', '物业费到期时间', '已逾期天数'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

        # 获取逾期商铺数据
        today = timezone.now().date()
        Shop.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态（将已续费的逾期商铺状态改回正常）
        Shop.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        shops = Shop.objects.filter(status='overdue').order_by('property_fee_due_date')

        # 写入数据
        for row, shop in enumerate(shops, 2):
            ws.cell(row=row, column=1, value=shop.shop_number)
            ws.cell(row=row, column=2, value=shop.door_number or '')
            ws.cell(row=row, column=3, value=shop.tenant_name)
            ws.cell(row=row, column=4, value=shop.id_card or '')
            ws.cell(row=row, column=5, value=shop.phone)
            ws.cell(row=row, column=6, value=float(shop.area))
            ws.cell(row=row, column=7, value=getattr(shop, 'owner_name', '') or '')
            ws.cell(row=row, column=8, value=getattr(shop, 'owner_phone', '') or '')
            ws.cell(row=row, column=9, value=shop.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=10, value=shop.overdue_days)

            # 根据逾期天数设置行颜色
            if shop.overdue_days > 30:
                fill_color = 'FFCCCC'  # 深红色
            elif shop.overdue_days > 7:
                fill_color = 'FFE6CC'  # 橙色
            else:
                fill_color = 'FFFFCC'  # 浅黄色

            for col in range(1, 11):
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color=fill_color, end_color=fill_color, fill_type='solid')

        # 调整列宽
        column_widths = [12, 12, 12, 20, 15, 10, 12, 15, 15, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="逾期商铺列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ExportCheckoutShopView(LoginRequiredMixin, View):
    """导出退房商铺数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "退房商铺列表"

        # 设置表头
        headers = [
            '商铺号', '门牌号', '租户姓名', '身份证号', '租户电话',
            '平米数', '起租时间', '退房日期'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='E6E6E6', end_color='E6E6E6', fill_type='solid')

        # 获取退房商铺数据
        shops = Shop.objects.filter(status='checkout').order_by('-updated_at')

        # 写入数据
        for row, shop in enumerate(shops, 2):
            ws.cell(row=row, column=1, value=shop.shop_number)
            ws.cell(row=row, column=2, value=shop.door_number or '')
            ws.cell(row=row, column=3, value=shop.tenant_name)
            ws.cell(row=row, column=4, value=shop.id_card or '')
            ws.cell(row=row, column=5, value=shop.phone)
            ws.cell(row=row, column=6, value=float(shop.area))
            ws.cell(row=row, column=7, value=shop.lease_start_date.strftime('%Y-%m-%d'))
            # 将时区感知的datetime转换为本地时间
            local_updated_at = timezone.localtime(shop.updated_at)
            ws.cell(row=row, column=8, value=local_updated_at.strftime('%Y-%m-%d'))

        # 调整列宽
        column_widths = [12, 12, 12, 20, 15, 10, 12, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="退房商铺列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ImportShopView(LoginRequiredMixin, View):
    """导入商铺数据"""
    template_name = 'shops/import.html'

    def get(self, request):
        form = ShopImportForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = ShopImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']
            skip_duplicates = request.POST.get('skip_duplicates') == 'on'

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                skip_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:4]):  # 如果前4列都为空，跳过这行
                            continue

                        # 解析数据 - 按照新的表头格式：商铺号、门牌号、租户姓名、身份证号、租户电话、平米数、业主姓名、业主电话、起租时间、物业费到期时间
                        shop_number = str(row[0]) if row[0] else ''
                        door_number = str(row[1]) if row[1] else ''
                        tenant_name = str(row[2]) if row[2] else ''
                        id_card = str(row[3]).strip() if row[3] and str(row[3]).strip() else None
                        phone = str(row[4]) if row[4] else ''
                        # 修复平米数字段 - 使用Decimal正确处理小数
                        try:
                            area = Decimal(str(row[5])) if row[5] else Decimal('0')
                        except (ValueError, TypeError, InvalidOperation):
                            area = Decimal('0')

                        # 业主信息（可选）
                        owner_name = str(row[6]).strip() if row[6] and str(row[6]).strip() else None
                        owner_phone = str(row[7]).strip() if row[7] and str(row[7]).strip() else None

                        # 验证必填字段
                        if not phone:
                            errors.append(f'第{row_num}行: 租户电话不能为空')
                            error_count += 1
                            continue

                        if not shop_number:
                            errors.append(f'第{row_num}行: 商铺号不能为空')
                            error_count += 1
                            continue

                        # 解析日期 - 修复日期类型不匹配问题
                        def parse_date(date_value, default_date=None):
                            """统一的日期解析函数"""
                            if not date_value:
                                return default_date or timezone.now().date()

                            if isinstance(date_value, str):
                                try:
                                    return datetime.strptime(date_value, '%Y-%m-%d').date()
                                except ValueError:
                                    try:
                                        # 尝试其他日期格式
                                        return datetime.strptime(date_value, '%Y/%m/%d').date()
                                    except ValueError:
                                        return default_date or timezone.now().date()
                            elif hasattr(date_value, 'date'):
                                # 如果是datetime对象，转换为date
                                return date_value.date()
                            elif hasattr(date_value, 'year'):
                                # 如果已经是date对象
                                return date_value
                            else:
                                return default_date or timezone.now().date()

                        lease_start_date = parse_date(row[8])
                        property_fee_due_date = parse_date(row[9], timezone.now().date() + timedelta(days=365))

                        # 检查重复记录
                        if skip_duplicates:
                            existing = Shop.objects.filter(
                                shop_number=shop_number
                            ).exists()
                            if existing:
                                skip_count += 1
                                continue

                        # 创建商铺记录
                        shop = Shop(
                            shop_number=shop_number,
                            door_number=door_number,
                            tenant_name=tenant_name,
                            id_card=id_card,
                            phone=phone,
                            area=area,
                            owner_name=owner_name,
                            owner_phone=owner_phone,
                            lease_start_date=lease_start_date,
                            property_fee_due_date=property_fee_due_date,
                            status='active'
                        )
                        shop.save()
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                # 显示导入结果
                result_messages = []
                if success_count > 0:
                    result_messages.append(f'成功导入 {success_count} 条记录')
                if skip_count > 0:
                    result_messages.append(f'跳过重复记录 {skip_count} 条')
                if error_count > 0:
                    result_messages.append(f'导入失败 {error_count} 条记录')

                if success_count > 0:
                    messages.success(request, '；'.join(result_messages) + '！')
                elif skip_count > 0:
                    messages.warning(request, '；'.join(result_messages) + '！')

                if error_count > 0:
                    error_msg = '导入错误详情：\n' + '\n'.join(errors[:5])
                    if len(errors) > 5:
                        error_msg += f'\n... 还有 {len(errors) - 5} 个错误'
                    messages.error(request, error_msg)

                return redirect('shops:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})


class DownloadShopTemplateView(LoginRequiredMixin, View):
    """下载商铺导入模板"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商铺导入模板"

        # 设置表头
        headers = [
            '商铺号', '门牌号', '租户姓名', '身份证号', '租户电话', '平米数', '业主姓名', '业主电话', '起租时间', '物业费到期时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 添加示例数据
        example_data = [
            ['S001', '顺安路555-1', '张三', '110101199001011234', '13800138001', '100.5', '王五', '13900139001', '2024-01-01', '2024-12-31'],
            ['S002', '顺安路555-3', '李四', '110101199002021234', '13800138002', '120.8', '赵六', '13900139002', '2024-02-01', '2025-01-31'],
        ]

        for row_num, row_data in enumerate(example_data, 2):
            for col_num, value in enumerate(row_data, 1):
                ws.cell(row=row_num, column=col_num, value=value)

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="商铺导入模板.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class TestShopView(View):
    """测试商铺视图"""

    def get(self, request):
        return HttpResponse("商铺管理系统正常运行！")


class ExportShopPaymentHistoryView(LoginRequiredMixin, View):
    """导出商铺缴费流水"""

    def get(self, request):
        from openpyxl.utils import get_column_letter

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商铺缴费流水"

        # 设置表头
        headers = [
            '缴费时间', '商铺号', '租户姓名', '缴费金额', '费用期间', '缴费方式', '备注'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        payments = ShopPaymentHistory.objects.select_related('shop')

        # 应用与列表页面相同的筛选条件
        # 搜索功能
        search = request.GET.get('search')
        if search:
            payments = payments.filter(
                Q(shop__shop_number__icontains=search) |
                Q(shop__tenant_name__icontains=search) |
                Q(payment_method__icontains=search) |
                Q(notes__icontains=search)
            )

        # 日期筛选
        start_date = request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                # 设置为当天的23:59:59
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                payments = payments.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        payments = payments.order_by('-payment_date')

        # 写入数据
        for row, payment in enumerate(payments, 2):
            # 将时区感知的datetime转换为本地时间
            local_payment_date = timezone.localtime(payment.payment_date)

            ws.cell(row=row, column=1, value=local_payment_date.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=2, value=payment.shop.shop_number)
            ws.cell(row=row, column=3, value=payment.shop.tenant_name)
            ws.cell(row=row, column=4, value=float(payment.amount))
            ws.cell(row=row, column=5, value=f"{payment.fee_start_date} 至 {payment.fee_end_date}")
            ws.cell(row=row, column=6, value=payment.payment_method)
            ws.cell(row=row, column=7, value=payment.notes or '')

        # 调整列宽
        column_widths = [20, 12, 12, 12, 25, 12, 30]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(i)].width = width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # 生成文件名，包含时间范围信息
        filename = "商铺缴费流水"
        if start_date and end_date:
            filename += f"_{start_date}至{end_date}"
        elif start_date:
            filename += f"_{start_date}起"
        elif end_date:
            filename += f"_至{end_date}"
        filename += f"_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # 保存到响应
        wb.save(response)
        return response


class BatchDeleteShopPaymentView(LoginRequiredMixin, View):
    """批量删除商铺缴费记录"""

    def post(self, request):
        payment_ids = request.POST.getlist('payment_ids')

        if not payment_ids:
            messages.error(request, '请选择要删除的缴费记录')
            return redirect('shops:payment_history')

        try:
            # 删除选中的缴费记录
            deleted_count = ShopPaymentHistory.objects.filter(
                id__in=payment_ids
            ).delete()[0]

            messages.success(request, f'成功删除 {deleted_count} 条缴费记录')
        except Exception as e:
            messages.error(request, f'删除失败：{str(e)}')

        return redirect('shops:payment_history')
