#!/usr/bin/env python
"""
测试商品房物业费计算
"""
from decimal import Decimal

def calculate_property_fee(area, floor, has_parking=False, has_basement=False):
    """
    计算商品房物业费
    
    Args:
        area: 面积
        floor: 楼层
        has_parking: 是否有车位
        has_basement: 是否有地下室（不影响费用）
    
    Returns:
        tuple: (月费用, 年费用)
    """
    area = Decimal(str(area))
    
    # 基础费用：每平米1元
    base_fee = area * Decimal('1.00')
    
    # 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
    if floor <= 11:
        elevator_fee = area * Decimal('0.30')
    else:
        elevator_fee = area * Decimal('0.35')
    
    # 车位管理费：有车位每月20元
    parking_fee = Decimal('20.00') if has_parking else Decimal('0.00')
    
    # 共用设施设备运行维护费：每月10元
    facility_fee = Decimal('10.00')
    
    # 月费用
    monthly_fee = base_fee + elevator_fee + parking_fee + facility_fee
    
    # 年费用
    yearly_fee = monthly_fee * 12
    
    return monthly_fee.quantize(Decimal('0.01')), yearly_fee.quantize(Decimal('0.01'))

if __name__ == "__main__":
    # 测试9号楼1单元201
    print("=== 9号楼1单元201 物业费计算 ===")
    area = 92.35
    floor = 2
    has_parking = True
    has_basement = True
    
    monthly, yearly = calculate_property_fee(area, floor, has_parking, has_basement)
    
    print(f"面积: {area}㎡")
    print(f"楼层: {floor}层")
    print(f"车位: {'有' if has_parking else '无'}")
    print(f"地下室: {'有' if has_basement else '无'}")
    print()
    
    # 详细计算过程
    area_decimal = Decimal(str(area))
    base_fee = area_decimal * Decimal('1.00')
    elevator_fee = area_decimal * Decimal('0.30')  # 2层，属于11层以下
    parking_fee = Decimal('20.00')
    facility_fee = Decimal('10.00')
    
    print("=== 详细计算过程 ===")
    print(f"基础费用: {area} × 1.00 = {base_fee}")
    print(f"电梯费: {area} × 0.30 = {elevator_fee} (11层以下)")
    print(f"车位费: {parking_fee}")
    print(f"设施费: {facility_fee}")
    print(f"月费用: {base_fee} + {elevator_fee} + {parking_fee} + {facility_fee} = {monthly}")
    print(f"年费用: {monthly} × 12 = {yearly}")
    
    print()
    print(f"最终结果:")
    print(f"月物业费: ¥{monthly}")
    print(f"年物业费: ¥{yearly}")
