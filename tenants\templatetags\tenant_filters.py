from django import template
from django.utils.safestring import mark_safe

register = template.Library()

@register.filter
def format_phone_numbers(phone_string):
    """
    格式化电话号码，多个号码分行显示
    """
    if not phone_string:
        return "-"
    
    # 分割电话号码
    phone_numbers = [phone.strip() for phone in phone_string.split(',') if phone.strip()]
    
    if not phone_numbers:
        return "-"
    
    # 如果只有一个电话号码，直接返回
    if len(phone_numbers) == 1:
        return phone_numbers[0]
    
    # 多个电话号码，用换行符分隔，并添加样式
    formatted_phones = []
    for i, phone in enumerate(phone_numbers):
        if i == 0:
            # 第一个电话号码
            formatted_phones.append(f'<div class="phone-number primary">{phone}</div>')
        else:
            # 其他电话号码，使用较小的字体
            formatted_phones.append(f'<div class="phone-number secondary">{phone}</div>')
    
    return mark_safe(''.join(formatted_phones))

@register.filter
def format_phone_numbers_simple(phone_string):
    """
    简单格式化电话号码，用<br>分行
    """
    if not phone_string:
        return "-"
    
    # 分割电话号码
    phone_numbers = [phone.strip() for phone in phone_string.split(',') if phone.strip()]
    
    if not phone_numbers:
        return "-"
    
    # 用<br>连接
    return mark_safe('<br>'.join(phone_numbers))

@register.filter
def phone_count(phone_string):
    """
    返回电话号码的数量
    """
    if not phone_string:
        return 0
    
    phone_numbers = [phone.strip() for phone in phone_string.split(',') if phone.strip()]
    return len(phone_numbers)
