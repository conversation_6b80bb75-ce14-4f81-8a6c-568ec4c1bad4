from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import random

from tenants.models import Tenant, TenantPaymentHistory
from commercial_properties.models import CommercialProperty, CommercialPropertyPaymentHistory
from shops.models import Shop, ShopPaymentHistory
from parking.models import ParkingSpace, ParkingPaymentHistory


class Command(BaseCommand):
    help = '创建测试数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='每个模块创建的记录数量'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        self.stdout.write('开始创建测试数据...')
        
        # 创建租客数据
        self.create_tenants(count)
        
        # 创建商品房数据
        self.create_commercial_properties(count)
        
        # 创建商铺数据
        self.create_shops(count)
        
        # 创建车位数据
        self.create_parking_spaces(count)
        
        self.stdout.write(
            self.style.SUCCESS(f'成功创建测试数据！每个模块 {count} 条记录')
        )

    def create_tenants(self, count):
        """创建租客数据"""
        names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二']
        landlords = ['刘房东', '陈房东', '杨房东', '黄房东', '赵房东']
        
        for i in range(count):
            name = random.choice(names) + str(i+1)
            landlord = random.choice(landlords)
            building = random.randint(1, 5)
            room_suffix = random.choice(['01', '02', '03', '04'])
            room = f"{random.randint(1, 20)}{room_suffix}"
            
            # 根据房号设置面积
            area = Decimal('130.00') if room_suffix in ['01', '04'] else Decimal('90.00')
            floor = random.randint(1, 20)
            
            # 随机设置到期时间（有些逾期，有些正常）
            days_offset = random.randint(-30, 365)
            due_date = timezone.now().date() + timedelta(days=days_offset)
            
            tenant = Tenant.objects.create(
                building_number=str(building),
                room_number=room,
                area=area,
                tenant_name=name,
                id_card=f"11010119{random.randint(80, 99)}0101{random.randint(1000, 9999)}",
                resident_count=random.randint(1, 4),
                landlord_name=landlord,
                landlord_phone=f"138{random.randint(10000000, 99999999)}",
                move_in_date=timezone.now().date() - timedelta(days=random.randint(30, 365)),
                property_fee_due_date=due_date,
                floor=floor
            )
            
            # 创建一些缴费记录
            if random.choice([True, False]):
                TenantPaymentHistory.objects.create(
                    tenant=tenant,
                    amount=tenant.property_fee * 12,
                    fee_start_date=due_date - timedelta(days=365),
                    fee_end_date=due_date,
                    payment_date=timezone.now() - timedelta(days=random.randint(1, 30))
                )

    def create_commercial_properties(self, count):
        """创建商品房数据"""
        owners = ['业主张', '业主李', '业主王', '业主赵', '业主钱']
        
        for i in range(count):
            owner = random.choice(owners) + str(i+1)
            building = random.randint(1, 5)
            unit = random.randint(1, 3)
            room = f"{random.randint(1, 20)}{random.choice(['01', '02', '03', '04'])}"
            
            area = Decimal(str(random.randint(80, 150)))
            floor = random.randint(1, 20)
            
            # 随机设置到期时间
            days_offset = random.randint(-30, 365)
            due_date = timezone.now().date() + timedelta(days=days_offset)
            
            property = CommercialProperty.objects.create(
                building_number=str(building),
                unit_number=str(unit),
                room_number=room,
                area=area,
                owner_name=owner,
                owner_phone=f"139{random.randint(10000000, 99999999)}",
                floor=floor,
                has_basement=random.choice([True, False]),
                has_parking=random.choice([True, False]),
                property_fee_due_date=due_date
            )
            
            # 创建一些缴费记录
            if random.choice([True, False]):
                CommercialPropertyPaymentHistory.objects.create(
                    property=property,
                    amount=property.property_fee_total * 12,
                    fee_start_date=due_date - timedelta(days=365),
                    fee_end_date=due_date,
                    payment_date=timezone.now() - timedelta(days=random.randint(1, 30))
                )

    def create_shops(self, count):
        """创建商铺数据"""
        shop_names = ['小卖部', '理发店', '餐厅', '服装店', '药店', '书店', '咖啡厅', '便利店']
        
        for i in range(count):
            shop_name = random.choice(shop_names) + f"老板{i+1}"
            shop_number = f"S-{i+1:02d}"
            
            area = Decimal(str(random.randint(20, 100)))
            
            # 随机设置到期时间
            days_offset = random.randint(-30, 365)
            due_date = timezone.now().date() + timedelta(days=days_offset)
            
            shop = Shop.objects.create(
                shop_number=shop_number,
                tenant_name=shop_name,
                phone=f"137{random.randint(10000000, 99999999)}",
                id_card=f"11010119{random.randint(80, 99)}0101{random.randint(1000, 9999)}",
                area=area,
                lease_start_date=timezone.now().date() - timedelta(days=random.randint(30, 365)),
                property_fee_due_date=due_date
            )
            
            # 创建一些缴费记录
            if random.choice([True, False]):
                ShopPaymentHistory.objects.create(
                    shop=shop,
                    amount=shop.property_fee * 12,
                    fee_start_date=due_date - timedelta(days=365),
                    fee_end_date=due_date,
                    payment_date=timezone.now() - timedelta(days=random.randint(1, 30))
                )

    def create_parking_spaces(self, count):
        """创建车位数据"""
        names = ['车主张', '车主李', '车主王', '车主赵', '车主钱']
        
        for i in range(count):
            name = random.choice(names) + str(i+1)
            building = random.randint(1, 5)
            room = f"{random.randint(1, 20)}{random.choice(['01', '02', '03', '04'])}"
            parking_number = f"P-A{i+1:02d}"
            
            # 生成车牌号
            provinces = ['京', '津', '沪', '渝', '冀']
            letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            license_plate = f"{random.choice(provinces)}{random.choice(letters)}{random.randint(10000, 99999)}"
            
            # 随机设置到期时间
            days_offset = random.randint(-30, 365)
            due_date = timezone.now().date() + timedelta(days=days_offset)
            
            parking = ParkingSpace.objects.create(
                building_number=str(building),
                room_number=room,
                tenant_name=name,
                phone=f"136{random.randint(10000000, 99999999)}",
                parking_number=parking_number,
                license_plate=license_plate,
                lease_start_date=timezone.now().date() - timedelta(days=random.randint(30, 365)),
                property_fee_due_date=due_date
            )
            
            # 创建一些缴费记录
            if random.choice([True, False]):
                ParkingPaymentHistory.objects.create(
                    parking=parking,
                    amount=parking.property_fee * 12,
                    fee_start_date=due_date - timedelta(days=365),
                    fee_end_date=due_date,
                    payment_date=timezone.now() - timedelta(days=random.randint(1, 30))
                )
