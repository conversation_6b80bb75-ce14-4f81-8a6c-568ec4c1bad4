from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal


class Tenant(models.Model):
    """租客模型"""
    # 基本信息
    building_number = models.CharField('楼号', max_length=1)
    room_number = models.CharField('房号', max_length=4)
    area = models.DecimalField('平米数', max_digits=8, decimal_places=2)
    tenant_name = models.CharField('租客姓名', max_length=50)
    id_card = models.CharField('身份证号', max_length=18, blank=True, null=True)
    tenant_phone = models.CharField('租客电话', max_length=200, help_text='可输入多个电话号码，用逗号分隔', blank=True)
    resident_count = models.IntegerField('租住人数', default=1)
    landlord_name = models.CharField('房东姓名', max_length=50)
    landlord_phone = models.CharField('房东电话', max_length=20, blank=True, null=True)

    # 租住信息
    move_in_date = models.DateField('入住时间')
    property_fee_due_date = models.DateField('物业费到期时间')
    property_fee = models.DecimalField('物业费用', max_digits=10, decimal_places=2)
    floor = models.IntegerField('楼层', default=1)

    # 状态
    STATUS_CHOICES = [
        ('active', '正常'),
        ('overdue', '逾期'),
        ('checkout', '已退房'),
    ]
    status = models.CharField('状态', max_length=10, choices=STATUS_CHOICES, default='active')

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '租客'
        verbose_name_plural = '租客'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.building_number}-{self.room_number} {self.tenant_name}"

    def get_formatted_phone(self):
        """获取格式化的电话号码，用于HTML显示"""
        if not self.tenant_phone:
            return "-"

        # 分割电话号码
        phone_numbers = [phone.strip() for phone in self.tenant_phone.split(',') if phone.strip()]

        if not phone_numbers:
            return "-"

        # 如果只有一个电话号码，直接返回
        if len(phone_numbers) == 1:
            return phone_numbers[0]

        # 多个电话号码，用HTML格式化
        formatted_phones = []
        for i, phone in enumerate(phone_numbers):
            if i == 0:
                # 第一个电话号码
                formatted_phones.append(f'<div class="phone-number primary">{phone}</div>')
            else:
                # 其他电话号码
                formatted_phones.append(f'<div class="phone-number secondary">{phone}</div>')

        return ''.join(formatted_phones)

    def get_phone_list(self):
        """获取电话号码列表"""
        if not self.tenant_phone:
            return []

        return [phone.strip() for phone in self.tenant_phone.split(',') if phone.strip()]

    @property
    def days_until_due(self):
        """距离到期天数"""
        if not self.property_fee_due_date:
            return 0

        try:
            # 获取当前日期
            current_date = timezone.now().date()

            # 确保due_date是date对象
            due_date = self.property_fee_due_date

            # 如果是datetime对象，转换为date
            if isinstance(due_date, datetime):
                due_date = due_date.date()

            # 计算天数差
            delta = due_date - current_date
            return delta.days

        except Exception:
            # 如果有任何错误，返回0
            return 0

    @property
    def is_overdue(self):
        """是否逾期"""
        return self.days_until_due < 0

    @property
    def overdue_days(self):
        """逾期天数"""
        if self.is_overdue:
            return abs(self.days_until_due)
        return 0

    def calculate_property_fee(self):
        """计算物业费"""
        if not self.area or not self.floor:
            return Decimal('0.00')

        # 基础费用：每平米1元
        base_fee = self.area * Decimal('1.00')

        # 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
        if self.floor <= 11:
            elevator_fee = self.area * Decimal('0.30')
        else:
            elevator_fee = self.area * Decimal('0.35')

        total_fee = base_fee + elevator_fee
        return total_fee.quantize(Decimal('0.01'))

    def save(self, *args, **kwargs):
        # 强化日期字段处理 - 确保所有日期都是正确的date对象
        try:
            if self.move_in_date:
                if isinstance(self.move_in_date, datetime):
                    self.move_in_date = self.move_in_date.date()
                elif hasattr(self.move_in_date, 'date') and callable(getattr(self.move_in_date, 'date')):
                    try:
                        self.move_in_date = self.move_in_date.date()
                    except (AttributeError, TypeError):
                        pass
        except Exception:
            pass

        try:
            if self.property_fee_due_date:
                if isinstance(self.property_fee_due_date, datetime):
                    self.property_fee_due_date = self.property_fee_due_date.date()
                elif hasattr(self.property_fee_due_date, 'date') and callable(getattr(self.property_fee_due_date, 'date')):
                    try:
                        self.property_fee_due_date = self.property_fee_due_date.date()
                    except (AttributeError, TypeError):
                        pass
        except Exception:
            pass

        # 自动计算物业费
        try:
            if self.area and self.floor:
                self.property_fee = self.calculate_property_fee()
        except Exception:
            pass

        # 根据房号自动设置平米数
        try:
            if self.room_number and not self.area:
                last_two = self.room_number[-2:] if len(self.room_number) >= 2 else ''
                if last_two in ['01', '04']:
                    self.area = Decimal('130.00')
                elif last_two in ['02', '03']:
                    self.area = Decimal('90.00')
        except Exception:
            pass

        # 更新状态 - 使用多层try-catch避免任何错误
        try:
            if self.property_fee_due_date:
                try:
                    is_overdue = self.is_overdue
                    if is_overdue and self.status == 'active':
                        self.status = 'overdue'
                    elif not is_overdue and self.status == 'overdue':
                        self.status = 'active'
                except Exception:
                    # 如果状态计算有问题，跳过状态更新
                    pass
        except Exception:
            pass

        super().save(*args, **kwargs)

    @staticmethod
    def add_months_to_date(date, months):
        """给日期添加指定月数，保持日期不变"""
        from dateutil.relativedelta import relativedelta
        return date + relativedelta(months=months)


class TenantPaymentHistory(models.Model):
    """租客缴费流水"""
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, verbose_name='租客')
    payment_date = models.DateTimeField('交费时间', default=timezone.now)
    amount = models.DecimalField('缴费金额', max_digits=10, decimal_places=2)
    fee_start_date = models.DateField('物业费开始时间')
    fee_end_date = models.DateField('物业费结束时间')
    payment_method = models.CharField('缴费方式', max_length=20, default='现金')
    notes = models.TextField('备注', blank=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '租客缴费记录'
        verbose_name_plural = '租客缴费记录'
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.tenant} - ¥{self.amount} - {self.payment_date.strftime('%Y-%m-%d')}"
