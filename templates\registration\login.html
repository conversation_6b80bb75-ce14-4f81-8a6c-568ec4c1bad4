{% extends 'base/base.html' %}
{% load static %}

{% block title %}登录 - 东悦物业管理系统{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
    }

    .login-container {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 25px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(20px);
        padding: 3.5rem;
        max-width: 480px;
        width: 100%;
        margin: 2rem;
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 2.5rem;
        position: relative;
    }

    .login-header h1 {
        color: #2c3e50;
        font-weight: 800;
        margin-bottom: 0.8rem;
        font-size: 2.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .login-header p {
        color: #7f8c8d;
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 0;
    }

    .login-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        position: relative;
        animation: iconPulse 3s ease-in-out infinite;
    }

    .login-icon::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        opacity: 0.3;
        animation: iconGlow 2s ease-in-out infinite alternate;
    }

    .login-icon i {
        font-size: 2.5rem;
        color: white;
        position: relative;
        z-index: 1;
    }

    @keyframes iconPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes iconGlow {
        0% { opacity: 0.3; }
        100% { opacity: 0.6; }
    }
    
    .form-floating {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-control {
        border: 2px solid #e8f0fe;
        border-radius: 15px;
        padding: 1.2rem;
        font-size: 1.1rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        height: 60px;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.3rem rgba(102, 126, 234, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
    }

    .form-floating label {
        color: #7f8c8d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .form-floating .form-control:focus ~ label,
    .form-floating .form-control:not(:placeholder-shown) ~ label {
        color: #667eea;
        font-weight: 600;
    }
    
    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        padding: 1.2rem;
        font-size: 1.2rem;
        font-weight: 700;
        width: 100%;
        color: white;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        margin-top: 1.5rem;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
        height: 60px;
    }

    .btn-login::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-login:hover::before {
        left: 100%;
    }

    .btn-login:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
        color: white;
    }

    .btn-login:active {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    
    .alert {
        border-radius: 15px;
        border: none;
        margin-bottom: 2rem;
        padding: 1.2rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        font-weight: 500;
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    }

    .floating-shapes {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: -1;
    }

    .shape {
        position: absolute;
        border-radius: 50%;
        animation: float 8s ease-in-out infinite;
    }

    .shape:nth-child(1) {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .shape:nth-child(2) {
        width: 200px;
        height: 200px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
        top: 60%;
        left: 75%;
        animation-delay: 2s;
    }

    .shape:nth-child(3) {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
        top: 30%;
        left: 85%;
        animation-delay: 4s;
    }

    .shape:nth-child(4) {
        width: 150px;
        height: 150px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
        top: 80%;
        left: 5%;
        animation-delay: 6s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        33% {
            transform: translateY(-30px) rotate(120deg);
        }
        66% {
            transform: translateY(-15px) rotate(240deg);
        }
    }
    
    .system-info {
        text-align: center;
        margin-top: 2.5rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .system-info::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 1px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .system-info small {
        color: #7f8c8d;
        font-size: 1rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 27px;
        z-index: -1;
        opacity: 0.1;
    }

    .particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: -2;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        animation: particleFloat 10s linear infinite;
    }

    @keyframes particleFloat {
        0% {
            transform: translateY(100vh) translateX(0);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100px) translateX(100px);
            opacity: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
</div>

<div class="particles" id="particles"></div>

<div class="login-container">
    <div class="login-header">
        <div class="login-icon">
            <i class="fas fa-building"></i>
        </div>
        <h1>东悦物业</h1>
        <p>智能物业管理系统</p>
    </div>
    
    {% if form.errors %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            用户名或密码错误，请重试
        </div>
    {% endif %}
    
    <form method="post">
        {% csrf_token %}
        
        <div class="form-floating">
            <input type="text" class="form-control" id="id_username" name="username" 
                   placeholder="用户名" required value="{{ form.username.value|default:'' }}">
            <label for="id_username">
                <i class="fas fa-user me-2"></i>用户名
            </label>
        </div>
        
        <div class="form-floating">
            <input type="password" class="form-control" id="id_password" name="password" 
                   placeholder="密码" required>
            <label for="id_password">
                <i class="fas fa-lock me-2"></i>密码
            </label>
        </div>
        
        <button type="submit" class="btn btn-login">
            <i class="fas fa-sign-in-alt me-2"></i>登录系统
        </button>
    </form>
    
    <div class="system-info">
        <small>
            <i class="fas fa-shield-alt me-1"></i>
            安全登录 · 数据加密保护
        </small>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 添加登录动画效果
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.querySelector('.login-container');
        container.style.opacity = '0';
        container.style.transform = 'translateY(50px) scale(0.9)';

        setTimeout(() => {
            container.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            container.style.opacity = '1';
            container.style.transform = 'translateY(0) scale(1)';
        }, 200);

        // 创建粒子效果
        createParticles();

        // 添加输入框焦点效果
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });

        // 添加按钮点击效果
        const loginBtn = document.querySelector('.btn-login');
        loginBtn.addEventListener('click', function(e) {
            // 创建涟漪效果
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    function createParticles() {
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 10 + 's';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            particlesContainer.appendChild(particle);
        }
    }
</script>

<style>
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: rippleEffect 0.6s linear;
        pointer-events: none;
    }

    @keyframes rippleEffect {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
</style>
{% endblock %}
