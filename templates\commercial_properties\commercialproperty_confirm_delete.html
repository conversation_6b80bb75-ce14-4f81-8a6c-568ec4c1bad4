{% extends 'base/base.html' %}
{% load static %}

{% block title %}删除商品房 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>删除商品房确认
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>警告
                        </h5>
                        <p class="mb-0">您即将删除以下商品房记录，此操作无法撤销！</p>
                    </div>
                    
                    <!-- 商品房信息 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-home me-1"></i>商品房信息
                            </h6>
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>楼号房号：</strong></td>
                                    <td>{{ object.building_number }}{% if object.unit_number %}-{{ object.unit_number }}{% endif %}-{{ object.room_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>业主姓名：</strong></td>
                                    <td>{{ object.owner_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>业主电话：</strong></td>
                                    <td>{{ object.owner_phone }}</td>
                                </tr>
                                <tr>
                                    <td><strong>平米数：</strong></td>
                                    <td>{{ object.area }}㎡</td>
                                </tr>
                                <tr>
                                    <td><strong>楼层：</strong></td>
                                    <td>{{ object.floor }}层</td>
                                </tr>
                                <tr>
                                    <td><strong>地下室：</strong></td>
                                    <td>
                                        {% if object.has_basement %}
                                            <span class="badge bg-info">{{ object.basement_number|default:"有" }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">无</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>车位：</strong></td>
                                    <td>
                                        {% if object.has_parking %}
                                            <span class="badge bg-success">{{ object.parking_number|default:"有" }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">无</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>年物业费：</strong></td>
                                    <td class="text-success fw-bold">¥{{ object.calculate_property_fee }}</td>
                                </tr>
                                <tr>
                                    <td><strong>物业费到期日：</strong></td>
                                    <td>{{ object.property_fee_due_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态：</strong></td>
                                    <td>
                                        {% if object.status == 'overdue' %}
                                            <span class="badge bg-danger">逾期</span>
                                        {% else %}
                                            <span class="badge bg-success">正常</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 删除确认表单 -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-info-circle me-1"></i>删除影响</h6>
                            <ul class="mb-0">
                                <li>商品房基本信息将被永久删除</li>
                                <li>相关的缴费记录也将被删除</li>
                                <li>此操作无法撤销，请谨慎操作</li>
                            </ul>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirm-delete" required>
                            <label class="form-check-label text-danger" for="confirm-delete">
                                <strong>我确认要删除此商品房记录</strong>
                            </label>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'commercial_properties:list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-danger" id="delete-btn" disabled>
                                <i class="fas fa-trash me-1"></i>确认删除
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const confirmCheckbox = document.getElementById('confirm-delete');
        const deleteBtn = document.getElementById('delete-btn');
        
        if (confirmCheckbox && deleteBtn) {
            confirmCheckbox.addEventListener('change', function() {
                deleteBtn.disabled = !this.checked;
            });
        }
    });
</script>
{% endblock %}
