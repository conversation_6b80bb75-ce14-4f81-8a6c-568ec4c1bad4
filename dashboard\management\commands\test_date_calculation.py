from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, date
from dateutil.relativedelta import relativedelta


class Command(BaseCommand):
    help = '测试日期计算逻辑'

    def handle(self, *args, **options):
        self.stdout.write('测试日期计算逻辑...')
        
        # 测试案例1：2025年6月19号到期，续费1个月
        due_date = date(2025, 6, 19)
        self.test_renewal(due_date, 1, "案例1：2025年6月19号到期，续费1个月")
        
        # 测试案例2：2025年6月19号到期，续费2个月
        self.test_renewal(due_date, 2, "案例2：2025年6月19号到期，续费2个月")
        
        # 测试案例3：2025年8月18日到期，续费3个月
        due_date2 = date(2025, 8, 18)
        self.test_renewal(due_date2, 3, "案例3：2025年8月18日到期，续费3个月")
        
        # 测试案例4：跨年续费
        due_date3 = date(2024, 12, 15)
        self.test_renewal(due_date3, 2, "案例4：2024年12月15日到期，续费2个月")
        
    def test_renewal(self, current_due_date, months, description):
        self.stdout.write(f"\n{description}")
        self.stdout.write(f"当前到期日期: {current_due_date}")
        
        # 计算费用开始日期（到期日期的下一天）
        fee_start_date = current_due_date + relativedelta(days=1)
        
        # 计算费用结束日期（精确按月计算）
        fee_end_date = fee_start_date + relativedelta(months=months) - relativedelta(days=1)
        
        self.stdout.write(f"续费月数: {months}个月")
        self.stdout.write(f"费用期间: {fee_start_date} 至 {fee_end_date}")
        self.stdout.write(f"新的到期日期: {fee_end_date}")
        
        # 验证逻辑
        expected_day = current_due_date.day
        actual_day = fee_end_date.day
        
        if expected_day == actual_day:
            self.stdout.write(self.style.SUCCESS("✓ 日期计算正确"))
        else:
            self.stdout.write(self.style.ERROR(f"✗ 日期计算错误，期望日期: {expected_day}，实际日期: {actual_day}"))
