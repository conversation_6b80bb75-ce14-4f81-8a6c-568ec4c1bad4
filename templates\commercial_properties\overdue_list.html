{% extends 'base/base.html' %}
{% load static %}

{% block title %}物业费到期列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        物业费到期列表
                        {% if properties %}
                            <span class="badge bg-danger ms-2">逾期 {{ page_obj.paginator.count }}套</span>
                        {% endif %}
                    </h4>
                    
                    <div class="d-flex gap-2">
                        <a href="{% url 'commercial_properties:list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i>返回列表
                        </a>
                    </div>
                </div>

                <!-- 统计信息栏 -->
                <div class="card-body border-bottom bg-light">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0 text-danger">{{ page_obj.paginator.count }}</h4>
                                    <small class="text-muted">逾期套数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0 text-warning">{{ short_overdue }}</h4>
                                    <small class="text-muted">逾期7天内</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="me-3">
                                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0 text-danger">{{ long_overdue }}</h4>
                                    <small class="text-muted">逾期30天以上</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 操作按钮和搜索表单 -->
                    <div class="mb-4">
                        <div class="row g-3 align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{% url 'commercial_properties:list' %}" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-1"></i>返回商品房物业费管理
                                    </a>
                                    <a href="{% url 'commercial_properties:payment_history' %}" class="btn btn-secondary">
                                        <i class="fas fa-history me-1"></i>物业流水
                                    </a>
                                    <a href="{% url 'commercial_properties:export_overdue' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-success">
                                        <i class="fas fa-download me-1"></i>导出逾期列表
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <form method="get" class="d-flex justify-content-end">
                                    <div class="me-2" style="min-width: 100px;">
                                        <select name="search_type" class="form-select">
                                            <option value="room" {% if request.GET.search_type == 'room' or not request.GET.search_type %}selected{% endif %}>房号</option>
                                            <option value="owner" {% if request.GET.search_type == 'owner' %}selected{% endif %}>业主姓名</option>
                                            <option value="phone" {% if request.GET.search_type == 'phone' %}selected{% endif %}>电话</option>
                                            <option value="parking" {% if request.GET.search_type == 'parking' %}selected{% endif %}>车位</option>
                                        </select>
                                    </div>
                                    <div class="search-box me-2" style="width: 250px;">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" name="search" class="form-control"
                                               placeholder="请输入搜索内容..."
                                               value="{{ request.GET.search }}">
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'commercial_properties:overdue' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    {% if properties %}

                        <!-- 数据表格 - 分离表头表体结构 -->
                        <div class="table-container">
                            <!-- 固定表头 -->
                            <div class="table-header-fixed">
                                <table class="table" id="header-table">
                                    <thead>
                                        <tr>
                                            <th>楼号</th>
                                            <th>单元号</th>
                                            <th>房号</th>
                                            <th>平米数</th>
                                            <th>业主姓名</th>
                                            <th>地下室</th>
                                            <th>车位</th>
                                            <th>业主电话</th>
                                            <th>年物业费</th>
                                            <th>到期日期</th>
                                            <th>逾期天数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>

                            <!-- 可滚动表体 -->
                            <div class="table-body-scroll">
                                <table class="table table-hover" id="body-table">
                                    <thead style="visibility: hidden;">
                                        <tr>
                                            <th>楼号</th>
                                            <th>单元号</th>
                                            <th>房号</th>
                                            <th>平米数</th>
                                            <th>业主姓名</th>
                                            <th>地下室</th>
                                            <th>车位</th>
                                            <th>业主电话</th>
                                            <th>年物业费</th>
                                            <th>到期日期</th>
                                            <th>逾期天数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {% for property in properties %}
                                    <tr class="{% if property.overdue_days > 30 %}table-danger{% elif property.overdue_days > 7 %}table-warning{% endif %}">
                                        <td>{{ property.building_number }}</td>
                                        <td>{{ property.unit_number|default:"-" }}</td>
                                        <td>{{ property.room_number }}</td>
                                        <td>{{ property.area }}㎡</td>
                                        <td>{{ property.owner_name }}</td>
                                        <td>
                                            {% if property.has_basement %}
                                                <span class="badge bg-info">{{ property.basement_number|default:"有" }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">无</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if property.has_parking %}
                                                <span class="badge bg-success">{{ property.parking_number|default:"有" }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">无</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ property.owner_phone }}</td>
                                        <td class="text-success fw-bold">¥{{ property.calculate_property_fee }}</td>
                                        <td>{{ property.property_fee_due_date }}</td>
                                        <td>
                                            <span class="badge bg-danger">{{ property.overdue_days }}天</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons-container">
                                                <a href="{% url 'commercial_properties:renew' property.pk %}" class="action-btn renew-btn" title="续费缴费">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                    <span>续费</span>
                                                </a>
                                                <a href="{% url 'commercial_properties:edit' property.pk %}" class="action-btn edit-btn" title="编辑商品房信息">
                                                    <i class="fas fa-edit"></i>
                                                    <span>编辑</span>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 美化的分页导航 -->
                        {% if is_paginated %}
                        <div class="pagination-container">
                            <!-- 分页统计信息 -->
                            <div class="pagination-info">
                                <div class="info-item">
                                    <i class="fas fa-list-ol text-primary"></i>
                                    <span class="info-text">
                                        共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                    </span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-file-alt text-success"></i>
                                    <span class="info-text">
                                        每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                    </span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-bookmark text-info"></i>
                                    <span class="info-text">
                                        第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                    </span>
                                </div>
                            </div>

                            <!-- 分页按钮 -->
                            <nav aria-label="分页导航" class="pagination-nav">
                                <ul class="pagination pagination-modern">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}" title="首页">
                                                <i class="fas fa-angle-double-left"></i>
                                                <span class="d-none d-sm-inline">首页</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}" title="上一页">
                                                <i class="fas fa-angle-left"></i>
                                                <span class="d-none d-sm-inline">上一页</span>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link page-link-first">
                                                <i class="fas fa-angle-double-left"></i>
                                                <span class="d-none d-sm-inline">首页</span>
                                            </span>
                                        </li>
                                        <li class="page-item disabled">
                                            <span class="page-link page-link-prev">
                                                <i class="fas fa-angle-left"></i>
                                                <span class="d-none d-sm-inline">上一页</span>
                                            </span>
                                        </li>
                                    {% endif %}

                                    <!-- 页码显示 -->
                                    <li class="page-item active">
                                        <span class="page-link page-link-current">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ page_obj.number }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}" title="下一页">
                                                <span class="d-none d-sm-inline">下一页</span>
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.search_type %}&search_type={{ request.GET.search_type }}{% endif %}" title="末页">
                                                <span class="d-none d-sm-inline">末页</span>
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link page-link-next">
                                                <span class="d-none d-sm-inline">下一页</span>
                                                <i class="fas fa-angle-right"></i>
                                            </span>
                                        </li>
                                        <li class="page-item disabled">
                                            <span class="page-link page-link-last">
                                                <span class="d-none d-sm-inline">末页</span>
                                                <i class="fas fa-angle-double-right"></i>
                                            </span>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                        
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">太好了！没有逾期的商品房</h4>
                            <p class="text-muted">所有商品房的物业费都是正常状态</p>
                            <a href="{% url 'commercial_properties:list' %}" class="btn btn-primary">
                                <i class="fas fa-list me-1"></i>查看商品房管理
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table-warning {
        background-color: #fff3cd !important;
    }
    .table-danger {
        background-color: #f8d7da !important;
    }

    /* 搜索框样式 */
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1200px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1200px;
    }

    #body-table thead {
        margin: 0 !important;
        padding: 0 !important;
        height: 0 !important;
        line-height: 0 !important;
    }

    #body-table thead th {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        line-height: 0 !important;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        font-size: 0.9rem;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    .table-body-scroll tr:hover {
        background-color: #f8f9fa;
    }

    .table-body-scroll tr:last-child td {
        border-bottom: none;
    }

    /* 列宽设置 - 物业费到期列表表格 */
    .table-header-fixed th:nth-child(1),
    .table-body-scroll td:nth-child(1) { width: 45px; }
    .table-header-fixed th:nth-child(2),
    .table-body-scroll td:nth-child(2) { width: 50px; }
    .table-header-fixed th:nth-child(3),
    .table-body-scroll td:nth-child(3) { width: 55px; }
    .table-header-fixed th:nth-child(4),
    .table-body-scroll td:nth-child(4) { width: 60px; }
    .table-header-fixed th:nth-child(5),
    .table-body-scroll td:nth-child(5) { width: 80px; }
    .table-header-fixed th:nth-child(6),
    .table-body-scroll td:nth-child(6) { width: 60px; }
    .table-header-fixed th:nth-child(7),
    .table-body-scroll td:nth-child(7) { width: 60px; }
    .table-header-fixed th:nth-child(8),
    .table-body-scroll td:nth-child(8) { width: 240px; }
    .table-header-fixed th:nth-child(9),
    .table-body-scroll td:nth-child(9) { width: 80px; }
    .table-header-fixed th:nth-child(10),
    .table-body-scroll td:nth-child(10) { width: 140px; }
    .table-header-fixed th:nth-child(11),
    .table-body-scroll td:nth-child(11) { width: 80px; }
    .table-header-fixed th:nth-child(12),
    .table-body-scroll td:nth-child(12) { width: 140px; }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }
    }

    /* 商品房管理专用操作按钮样式 */
    .action-buttons-container {
        display: flex;
        gap: 8px;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        padding: 4px;
    }

    .action-btn {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border-radius: 20px;
        text-decoration: none;
        font-size: 13px;
        font-weight: 600;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        min-width: 70px;
        backdrop-filter: blur(10px);
        text-transform: uppercase;
        letter-spacing: 0.8px;
        overflow: hidden;
    }

    .action-btn i {
        font-size: 16px;
        margin-right: 6px;
        transition: transform 0.3s ease;
    }

    .action-btn span {
        font-size: 12px;
        font-weight: 700;
        position: relative;
        z-index: 2;
    }

    .action-btn:hover {
        transform: translateY(-3px) scale(1.05);
        text-decoration: none;
    }

    .action-btn:hover i {
        transform: rotate(360deg);
    }

    .action-btn:active {
        transform: translateY(-1px) scale(1.02);
    }

    /* 编辑按钮 - 现代蓝色玻璃效果 */
    .edit-btn {
        background: linear-gradient(145deg, rgba(74, 144, 226, 0.9), rgba(56, 103, 214, 0.9));
        color: white;
        border: 2px solid rgba(74, 144, 226, 0.3);
        box-shadow: 0 8px 32px rgba(74, 144, 226, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .edit-btn:hover {
        background: linear-gradient(145deg, rgba(74, 144, 226, 1), rgba(56, 103, 214, 1));
        border-color: rgba(74, 144, 226, 0.6);
        box-shadow: 0 12px 40px rgba(74, 144, 226, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* 续费按钮 - 现代绿色玻璃效果 */
    .renew-btn {
        background: linear-gradient(145deg, rgba(34, 197, 94, 0.9), rgba(21, 128, 61, 0.9));
        color: white;
        border: 2px solid rgba(34, 197, 94, 0.3);
        box-shadow: 0 8px 32px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .renew-btn:hover {
        background: linear-gradient(145deg, rgba(34, 197, 94, 1), rgba(21, 128, 61, 1));
        border-color: rgba(34, 197, 94, 0.6);
        box-shadow: 0 12px 40px rgba(34, 197, 94, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* 按钮波纹效果 */
    .action-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .action-btn:active::after {
        width: 300px;
        height: 300px;
    }

    /* 按钮闪光效果 */
    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.8s ease;
        z-index: 1;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .action-buttons-container {
            gap: 6px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 6px 12px;
            min-width: 60px;
            font-size: 11px;
        }

        .action-btn i {
            font-size: 14px;
            margin-right: 4px;
        }

        .action-btn span {
            font-size: 10px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头表体列宽
        syncTableColumns();

        // 同步表头表体列宽函数
        function syncTableColumns() {
            const headerTable = document.getElementById('header-table');
            const bodyTable = document.getElementById('body-table');

            if (!headerTable || !bodyTable) {
                console.log('表头或表体表格未找到');
                return;
            }

            const headerCells = headerTable.querySelectorAll('th');
            const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

            // 定义列宽 - 物业费到期列表表格的列宽配置
            const columnWidths = [
                '45px',   // 楼号
                '50px',   // 单元号
                '55px',   // 房号
                '60px',   // 平米数
                '80px',   // 业主姓名
                '60px',   // 地下室
                '60px',   // 车位
                '240px',  // 业主电话
                '80px',   // 年物业费
                '140px',  // 到期日期
                '80px',   // 逾期天数
                '140px'   // 操作
            ];

            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 设置表体隐藏表头列宽（用于对齐）
            bodyHeaderCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 确保两个表格宽度一致
            headerTable.style.width = '100%';
            bodyTable.style.width = '100%';
            headerTable.style.minWidth = '1200px';
            bodyTable.style.minWidth = '1200px';

            console.log('✅ 物业费到期列表表头表体列宽同步完成');
        }

        // 窗口大小改变时重新同步列宽
        window.addEventListener('resize', function() {
            setTimeout(syncTableColumns, 50);
        });
    });
</script>
{% endblock %}
