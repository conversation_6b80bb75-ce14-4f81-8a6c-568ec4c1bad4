{% extends 'base/base.html' %}
{% load static %}

{% block title %}车位缴费流水 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-history me-2 text-success"></i>车位缴费流水
                        <span class="badge bg-success ms-2">{{ total_count }}条</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 统计信息 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">总收入</h6>
                                            <h4 class="mb-0">¥{{ total_amount }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-coins fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">本月收入</h6>
                                            <h4 class="mb-0">¥{{ this_month_amount }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">本月笔数</h6>
                                            <h4 class="mb-0">{{ this_month_count }}笔</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-file-invoice fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">上月收入</h6>
                                            <h4 class="mb-0">¥{{ last_month_amount }}</h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-chart-line fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-6">
                                <a href="{% url 'parking:list' %}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-1"></i>返回车位列表
                                </a>
                                <a href="{% url 'parking:export_payment_history' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-success">
                                    <i class="fas fa-download me-1"></i>导出流水
                                </a>
                                <button type="button" class="btn btn-danger" onclick="showBatchDeleteModal()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                            <div class="col-md-6">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <div class="me-2">
                                        {{ search_form.start_date }}
                                    </div>
                                    <div class="me-2">
                                        {{ search_form.end_date }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'parking:payment_history' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 - 分离表头表体结构 -->
                    <div class="table-container">
                        <!-- 固定表头 -->
                        <div class="table-header-fixed">
                            <table class="table" id="header-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>缴费时间</th>
                                        <th>车位号</th>
                                        <th>房号</th>
                                        <th>租户姓名</th>
                                        <th>缴费金额</th>
                                        <th>费用期间</th>
                                        <th>缴费方式</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <!-- 可滚动表体 -->
                        <div class="table-body-scroll">
                            <table class="table table-hover" id="body-table">
                                <thead style="visibility: hidden;">
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input" style="visibility: hidden;">
                                        </th>
                                        <th>缴费时间</th>
                                        <th>车位号</th>
                                        <th>房号</th>
                                        <th>租户姓名</th>
                                        <th>缴费金额</th>
                                        <th>费用期间</th>
                                        <th>缴费方式</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="payment_ids" value="{{ payment.id }}" class="form-check-input payment-checkbox">
                                    </td>
                                    <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                                    <td>{{ payment.parking.parking_number }}</td>
                                    <td>{{ payment.parking.room_number }}</td>
                                    <td>{{ payment.parking.tenant_name }}</td>
                                    <td class="text-success fw-bold">¥{{ payment.amount }}</td>
                                    <td>{{ payment.fee_start_date }} 至 {{ payment.fee_end_date }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ payment.payment_method }}</span>
                                    </td>
                                    <td>{{ payment.notes|default:"-" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无缴费记录</p>
                                        <a href="{% url 'parking:list' %}" class="btn btn-primary">
                                            <i class="fas fa-arrow-left me-1"></i>返回车位列表
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 美化的分页导航 -->
                    {% if is_paginated %}
                    <div class="pagination-container">
                        <!-- 分页统计信息 -->
                        <div class="pagination-info">
                            <div class="info-item">
                                <i class="fas fa-list-ol text-primary"></i>
                                <span class="info-text">
                                    共 <strong class="text-primary">{{ page_obj.paginator.count }}</strong> 条记录
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-file-alt text-success"></i>
                                <span class="info-text">
                                    每页 <strong class="text-success">{{ page_obj.paginator.per_page }}</strong> 条
                                </span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-bookmark text-info"></i>
                                <span class="info-text">
                                    第 <strong class="text-info">{{ page_obj.number }}</strong> / {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </div>
                        </div>

                        <!-- 分页按钮 -->
                        <nav aria-label="分页导航" class="pagination-nav">
                            <ul class="pagination pagination-modern">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link page-link-first" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-prev" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-first">
                                            <i class="fas fa-angle-double-left"></i>
                                            <span class="d-none d-sm-inline">首页</span>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-prev">
                                            <i class="fas fa-angle-left"></i>
                                            <span class="d-none d-sm-inline">上一页</span>
                                        </span>
                                    </li>
                                {% endif %}

                                <!-- 页码显示 -->
                                <li class="page-item active">
                                    <span class="page-link page-link-current">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ page_obj.number }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link page-link-next" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="下一页">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link page-link-last" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}" title="末页">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-next">
                                            <span class="d-none d-sm-inline">下一页</span>
                                            <i class="fas fa-angle-right"></i>
                                        </span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link page-link-last">
                                            <span class="d-none d-sm-inline">末页</span>
                                            <i class="fas fa-angle-double-right"></i>
                                        </span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量删除确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除选中的 <span id="selectedCount">0</span> 条缴费记录吗？</p>
                <p class="text-danger">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="batchDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 10;
    }

    .search-box input {
        padding-left: 35px;
    }

    .toolbar {
        margin-bottom: 20px;
    }

    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    /* 表格容器 */
    .table-container {
        position: relative;
        width: 100%;
        margin-bottom: 1rem;
    }

    /* 固定表头样式 */
    .table-header-fixed {
        position: relative;
        z-index: 10;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 1px solid #dee2e6;
        border-bottom: none;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .table-header-fixed table {
        margin: 0 !important;
        background: transparent;
        table-layout: fixed;
        width: 100%;
        min-width: 1100px;
        border-spacing: 0;
        border-collapse: separate;
    }

    .table-header-fixed th {
        background: transparent !important;
        color: white !important;
        font-weight: 600;
        font-size: 15px;
        text-align: center;
        vertical-align: middle;
        padding: 0.8rem 0.6rem;
        border: none;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        white-space: nowrap;
    }

    .table-header-fixed th:last-child {
        border-right: none;
    }

    /* 可滚动表体样式 */
    .table-body-scroll {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        position: relative;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 表体表格样式 */
    #body-table {
        width: 100%;
        margin: 0 !important;
        border-collapse: separate;
        border-spacing: 0;
        table-layout: fixed;
        min-width: 1100px;
    }

    #body-table thead {
        margin: 0 !important;
        padding: 0 !important;
        height: 0 !important;
        line-height: 0 !important;
    }

    #body-table thead th {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        line-height: 0 !important;
    }

    .table-body-scroll td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        font-size: 0.9rem;
    }

    .table-body-scroll td:last-child {
        border-right: none;
    }

    .table-body-scroll tr:hover {
        background-color: #f8f9fa;
    }

    .table-body-scroll tr:last-child td {
        border-bottom: none;
    }

    /* 列宽设置 - 车位缴费流水表格 */
    .table-header-fixed th:nth-child(1),
    .table-body-scroll td:nth-child(1) { width: 60px; }
    .table-header-fixed th:nth-child(2),
    .table-body-scroll td:nth-child(2) { width: 150px; }
    .table-header-fixed th:nth-child(3),
    .table-body-scroll td:nth-child(3) { width: 100px; }
    .table-header-fixed th:nth-child(4),
    .table-body-scroll td:nth-child(4) { width: 100px; }
    .table-header-fixed th:nth-child(5),
    .table-body-scroll td:nth-child(5) { width: 120px; }
    .table-header-fixed th:nth-child(6),
    .table-body-scroll td:nth-child(6) { width: 100px; }
    .table-header-fixed th:nth-child(7),
    .table-body-scroll td:nth-child(7) { width: 200px; }
    .table-header-fixed th:nth-child(8),
    .table-body-scroll td:nth-child(8) { width: 100px; }
    .table-header-fixed th:nth-child(9),
    .table-body-scroll td:nth-child(9) { width: 170px; }

    /* 美化分页样式 */
    .pagination-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        gap: 1rem;
    }

    .pagination-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.375rem 0.75rem;
        background: white;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .info-item i {
        font-size: 0.9rem;
    }

    .info-text {
        font-size: 0.8rem;
        color: #495057;
        font-weight: 500;
    }

    .pagination-nav {
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .pagination-modern {
        margin-bottom: 0;
        gap: 0.25rem;
    }

    .pagination-modern .page-item {
        margin: 0 0.125rem;
    }

    .pagination-modern .page-link {
        border: 2px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        min-width: 2.5rem;
        justify-content: center;
        font-size: 0.875rem;
    }

    .pagination-modern .page-link:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .pagination-modern .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
        color: white;
        box-shadow: 0 3px 6px rgba(102, 126, 234, 0.4);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    .page-link-current {
        background: linear-gradient(135deg, #667eea, #764ba2) !important;
        border-color: #667eea !important;
        color: white !important;
        font-weight: 600;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .pagination-info {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .info-item {
            padding: 0.25rem 0.5rem;
        }

        .info-text {
            font-size: 0.75rem;
        }

        .pagination-nav {
            justify-content: center;
        }

        .pagination-modern .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 2rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 同步表头表体列宽
        syncTableColumns();

        // 同步表头表体列宽函数
        function syncTableColumns() {
            const headerTable = document.getElementById('header-table');
            const bodyTable = document.getElementById('body-table');

            if (!headerTable || !bodyTable) {
                console.log('表头或表体表格未找到');
                return;
            }

            const headerCells = headerTable.querySelectorAll('th');
            const bodyHeaderCells = bodyTable.querySelectorAll('thead th');

            // 定义列宽 - 车位缴费流水表格的列宽配置
            const columnWidths = [
                '60px',   // 复选框
                '150px',  // 缴费时间
                '100px',  // 车位号
                '100px',  // 房号
                '120px',  // 租户姓名
                '100px',  // 缴费金额
                '200px',  // 费用期间
                '100px',  // 缴费方式
                '170px'   // 备注
            ];

            // 设置表头列宽
            headerCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 设置表体隐藏表头列宽（用于对齐）
            bodyHeaderCells.forEach((th, index) => {
                if (index < columnWidths.length) {
                    th.style.width = columnWidths[index];
                    th.style.minWidth = columnWidths[index];
                    th.style.maxWidth = columnWidths[index];
                }
            });

            // 确保两个表格宽度一致
            headerTable.style.width = '100%';
            bodyTable.style.width = '100%';
            headerTable.style.minWidth = '1100px';
            bodyTable.style.minWidth = '1100px';

            console.log('✅ 车位缴费流水表头表体列宽同步完成');
        }

        // 窗口大小改变时重新同步列宽
        window.addEventListener('resize', function() {
            setTimeout(syncTableColumns, 50);
        });

        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('selectAll');
        const paymentCheckboxes = document.querySelectorAll('.payment-checkbox');

        selectAllCheckbox.addEventListener('change', function() {
            paymentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });

        paymentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.payment-checkbox:checked').length;
            document.getElementById('selectedCount').textContent = selectedCount;

            // 更新全选状态
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < paymentCheckboxes.length;
            selectAllCheckbox.checked = selectedCount === paymentCheckboxes.length;
        }
    });

    function showBatchDeleteModal() {
        const selectedCount = document.querySelectorAll('.payment-checkbox:checked').length;
        if (selectedCount === 0) {
            alert('请先选择要删除的记录');
            return;
        }

        document.getElementById('selectedCount').textContent = selectedCount;
        const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
        modal.show();
    }

    function batchDelete() {
        const selectedIds = Array.from(document.querySelectorAll('.payment-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedIds.length === 0) {
            alert('请先选择要删除的记录');
            return;
        }

        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "parking:batch_delete_payment" %}';

        // 添加CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // 添加选中的ID
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'payment_ids';
            input.value = id;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
</script>
{% endblock %}
