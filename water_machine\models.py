from django.db import models
from django.utils import timezone
from decimal import Decimal
import random
import string


class VillageCustomer(models.Model):
    """村售水机客户信息模型"""
    name = models.CharField('姓名', max_length=50)
    address = models.CharField('住址', max_length=200)
    phone = models.CharField('电话', max_length=20)
    water_bucket = models.PositiveIntegerField('水桶数量', default=1, help_text='水桶数量')

    # 水卡信息
    normal_card_number = models.CharField('正常卡号', max_length=20, blank=True, null=True)
    lost_card_numbers = models.TextField('丢失卡号', blank=True, null=True, help_text='多个卡号用逗号分隔')

    # 充值信息
    people_count = models.PositiveIntegerField('使用人数', default=1)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '村售水机客户'
        verbose_name_plural = '村售水机客户'
        ordering = ['address']

    def __str__(self):
        return f"{self.name} - {self.phone}"

    def save(self, *args, **kwargs):
        # 保存时不自动生成卡号，由用户手动输入
        super().save(*args, **kwargs)

    def generate_card_number(self):
        """生成唯一的卡号（保留方法，但不自动调用）"""
        while True:
            # 生成8位数字卡号
            card_number = ''.join(random.choices(string.digits, k=8))
            if not VillageCustomer.objects.filter(normal_card_number=card_number).exists():
                return card_number

    def get_lost_card_list(self):
        """获取丢失卡号列表"""
        if self.lost_card_numbers:
            return [card.strip() for card in self.lost_card_numbers.split(',') if card.strip()]
        return []

    def get_lost_card_display(self):
        """获取丢失卡号显示文本"""
        lost_cards = self.get_lost_card_list()
        return ', '.join(lost_cards) if lost_cards else '-'

    def get_fixed_amount(self):
        """获取固定金额：人数 × 15元 × 3个月（一个季度）"""
        return Decimal(str(self.people_count * 15 * 3))

    def get_quarter_status(self):
        """获取当年四个季度的充值状态"""
        import datetime
        from collections import OrderedDict
        current_year = datetime.datetime.now().year
        quarter_status = OrderedDict()

        # 获取当年的充值记录
        recharge_records = self.recharge_records.filter(
            quarter__startswith=str(current_year)
        ).values_list('quarter', flat=True)

        # 按顺序检查四个季度的充值状态
        for quarter in range(1, 5):
            quarter_code = f"{current_year}Q{quarter}"
            quarter_status[f"Q{quarter}"] = quarter_code in recharge_records

        return quarter_status


class TenantWaterCard(models.Model):
    """租户售水机卡号记录模型"""
    name = models.CharField('姓名', max_length=50)
    address = models.CharField('住址', max_length=200)
    phone = models.CharField('电话', max_length=11, help_text='11位手机号码')
    water_bucket = models.PositiveIntegerField('水桶数量', default=0, help_text='水桶数量')

    # 水卡信息
    normal_card_number = models.CharField('正常卡号', max_length=20, blank=True, null=True)
    lost_card_numbers = models.TextField('丢失卡号', blank=True, null=True, help_text='多个卡号用逗号分隔')

    # 记录时间
    record_time = models.DateTimeField('记录时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '租户售水机卡号记录'
        verbose_name_plural = '租户售水机卡号记录'
        ordering = ['-record_time']

    def __str__(self):
        return f"{self.name} - {self.phone}"

    def get_lost_card_list(self):
        """获取丢失卡号列表"""
        if self.lost_card_numbers:
            return [card.strip() for card in self.lost_card_numbers.split(',') if card.strip()]
        return []

    def get_lost_card_display(self):
        """获取丢失卡号显示文本"""
        lost_cards = self.get_lost_card_list()
        if lost_cards:
            return ', '.join(lost_cards)
        return '-'

    def report_card_lost(self, card_number, new_card_number=None):
        """挂失水卡"""
        if not card_number:
            return False, "卡号不能为空"

        # 检查是否是正常卡号
        if self.normal_card_number == card_number:
            # 将正常卡号移到丢失卡号列表
            lost_cards = self.get_lost_card_list()
            lost_cards.append(card_number)
            self.lost_card_numbers = ', '.join(lost_cards)

            # 设置新的正常卡号
            self.normal_card_number = new_card_number or ''
            self.save()
            return True, f"卡号 {card_number} 已挂失"

        # 检查是否已经在丢失列表中
        lost_cards = self.get_lost_card_list()
        if card_number in lost_cards:
            return False, f"卡号 {card_number} 已经在挂失列表中"

        return False, f"卡号 {card_number} 不属于该租户"


class VillageRechargeRecord(models.Model):
    """村售水机充值记录模型"""
    customer = models.ForeignKey(VillageCustomer, on_delete=models.CASCADE, related_name='recharge_records', verbose_name='客户')
    card_number = models.CharField('使用卡号', max_length=20)
    people_count = models.PositiveIntegerField('使用人数')
    amount = models.DecimalField('充值金额', max_digits=10, decimal_places=2)
    quarter = models.CharField('充值季度', max_length=10, help_text='格式：2025Q1')
    recharge_date = models.DateTimeField('充值时间', auto_now_add=True)
    notes = models.TextField('备注', blank=True, null=True)

    class Meta:
        verbose_name = '村售水机充值记录'
        verbose_name_plural = '村售水机充值记录'
        ordering = ['-recharge_date']
        # 注释掉唯一性约束，先通过表单验证防止重复
        # unique_together = ['customer', 'quarter']

    def __str__(self):
        return f"{self.customer.name} - {self.quarter} - ¥{self.amount}"

    def save(self, *args, **kwargs):
        # 自动计算充值金额（人数 × 15元 × 3个月）
        self.amount = Decimal(str(self.people_count * 15 * 3))
        # 使用客户当前的正常卡号
        if not self.card_number:
            self.card_number = self.customer.normal_card_number
        super().save(*args, **kwargs)


def get_quarter_choices():
    """生成季度选项 - 只返回当年的四个季度"""
    import datetime
    current_year = datetime.datetime.now().year
    choices = []

    # 只生成当前年份的四个季度
    for quarter in range(1, 5):
        quarter_code = f"{current_year}Q{quarter}"
        quarter_name = f"{current_year}年第{quarter}季度"
        choices.append((quarter_code, quarter_name))

    return choices
