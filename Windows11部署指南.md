# 东悦物业管理系统 - Windows 11 部署指南

## 🚀 Windows 11 环境部署

### 系统要求

#### 硬件配置
- **操作系统**：Windows 11 专业版或企业版
- **内存**：最低 4GB，推荐 8GB+
- **存储**：最低 50GB 可用空间，推荐 100GB+
- **CPU**：最低 4核，推荐 8核+

#### 软件要求
- **Python**：3.8+ (推荐 3.11)
- **MySQL**：8.0+
- **Git**：最新版本
- **Visual Studio Code**：推荐（可选）

### 1. 环境准备

#### 1.1 安装 Python
1. 访问 [Python官网](https://www.python.org/downloads/windows/)
2. 下载 Python 3.11.x 版本（推荐）
3. 运行安装程序，**重要**：勾选 "Add Python to PATH"
4. 选择 "Customize installation"
5. 确保勾选以下选项：
   - pip
   - tcl/tk and IDLE
   - Python test suite
   - py launcher
   - for all users (requires elevation)
6. 在 Advanced Options 中勾选：
   - Install for all users
   - Add Python to environment variables
   - Precompile standard library

验证安装：
```powershell
python --version
pip --version
```

#### 1.2 安装 MySQL
1. 访问 [MySQL官网](https://dev.mysql.com/downloads/mysql/)
2. 下载 MySQL Community Server 8.0.x
3. 运行 MySQL Installer
4. 选择 "Developer Default" 安装类型
5. 配置 MySQL Server：
   - Config Type: Development Computer
   - Connectivity: TCP/IP, Port 3306
   - Authentication Method: Use Strong Password Encryption
   - 设置 root 密码（请记住此密码）
6. 完成安装并启动 MySQL 服务

验证安装：
```powershell
mysql --version
```

#### 1.3 安装 Git
1. 访问 [Git官网](https://git-scm.com/download/win)
2. 下载并安装 Git for Windows
3. 安装时保持默认设置即可

验证安装：
```powershell
git --version
```

### 2. 项目部署

#### 2.1 创建项目目录
```powershell
# 在 C 盘创建项目目录
mkdir C:\dywy
cd C:\dywy
```

#### 2.2 克隆项目代码
```powershell
# 克隆项目（替换为实际的项目地址）
git clone <项目地址> .

# 或者如果已有项目文件，直接复制到 C:\dywy 目录
```

#### 2.3 创建虚拟环境
```powershell
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
.\venv\Scripts\activate

# 验证虚拟环境
where python
# 应该显示：C:\dywy\venv\Scripts\python.exe
```

#### 2.4 安装项目依赖

首先安装 Microsoft Visual C++ 14.0（mysqlclient 需要）：
1. 访问 [Microsoft C++ Build Tools](https://visualstudio.microsoft.com/visual-cpp-build-tools/)
2. 下载并安装 "Microsoft C++ Build Tools"
3. 在安装程序中选择 "C++ build tools" 工作负载

然后安装 Python 依赖：
```powershell
# 确保虚拟环境已激活
.\venv\Scripts\activate

# 升级 pip
python -m pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 如果 mysqlclient 安装失败，可以尝试：
pip install mysqlclient==2.2.0 --only-binary=all
```

### 3. 数据库配置

#### 3.1 创建数据库
打开 MySQL Command Line Client 或使用 MySQL Workbench：

```sql
-- 创建数据库
CREATE DATABASE dywy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选，也可以使用 root 用户）
CREATE USER 'dywy_user'@'localhost' IDENTIFIED BY 'dywy123456';

-- 授权
GRANT ALL PRIVILEGES ON dywy_db.* TO 'dywy_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 3.2 配置 Django 数据库设置
编辑 `property_management/settings.py` 文件：

```python
# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dywy_db',
        'USER': 'dywy_user',  # 或使用 'root'
        'PASSWORD': 'dywy123456',  # 使用您设置的密码
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

### 4. 初始化项目

#### 4.1 数据库迁移
```powershell
# 确保虚拟环境已激活
.\venv\Scripts\activate

# 创建迁移文件
python manage.py makemigrations

# 执行迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

#### 4.2 收集静态文件
```powershell
# 收集静态文件
python manage.py collectstatic --noinput
```

### 5. 运行项目

#### 5.1 开发环境运行
```powershell
# 确保虚拟环境已激活
.\venv\Scripts\activate

# 启动开发服务器
python manage.py runserver 0.0.0.0:8000
```

访问 http://localhost:8000 查看系统

#### 5.2 生产环境运行（使用 Waitress）

安装 Waitress（Windows 推荐的 WSGI 服务器）：
```powershell
pip install waitress
```

创建启动脚本 `start_server.py`：
```python
from waitress import serve
from property_management.wsgi import application

if __name__ == '__main__':
    serve(application, host='0.0.0.0', port=8000)
```

运行生产服务器：
```powershell
python start_server.py
```

### 6. 创建 Windows 服务（可选）

#### 6.1 安装 pywin32
```powershell
pip install pywin32
```

#### 6.2 创建服务脚本
创建 `dywy_service.py` 文件：

```python
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
from waitress import serve
from property_management.wsgi import application

class DywyService(win32serviceutil.ServiceFramework):
    _svc_name_ = "DywyPropertyManagement"
    _svc_display_name_ = "东悦物业管理系统"
    _svc_description_ = "东悦物业管理系统 Web 服务"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()

    def main(self):
        # 设置工作目录
        os.chdir(r'C:\dywy')
        
        # 启动 Waitress 服务器
        serve(application, host='0.0.0.0', port=8000)

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(DywyService)
```

#### 6.3 安装和管理服务
```powershell
# 以管理员身份运行 PowerShell

# 安装服务
python dywy_service.py install

# 启动服务
python dywy_service.py start

# 停止服务
python dywy_service.py stop

# 卸载服务
python dywy_service.py remove
```

### 7. 自动化部署脚本

#### 7.1 创建一键部署脚本
创建 `deploy.ps1` PowerShell 脚本：

```powershell
# deploy.ps1 - 东悦物业管理系统一键部署脚本
param(
    [string]$ProjectPath = "C:\dywy",
    [string]$DatabaseName = "dywy_db",
    [string]$DatabaseUser = "dywy_user",
    [string]$DatabasePassword = "dywy123456"
)

Write-Host "=== 东悦物业管理系统部署脚本 ===" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "请以管理员身份运行此脚本！" -ForegroundColor Red
    exit 1
}

# 创建项目目录
Write-Host "创建项目目录..." -ForegroundColor Yellow
if (!(Test-Path $ProjectPath)) {
    New-Item -ItemType Directory -Path $ProjectPath -Force
}
Set-Location $ProjectPath

# 检查 Python 安装
Write-Host "检查 Python 安装..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version
    Write-Host "Python 版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Python 未安装或未添加到 PATH！请先安装 Python。" -ForegroundColor Red
    exit 1
}

# 创建虚拟环境
Write-Host "创建虚拟环境..." -ForegroundColor Yellow
if (!(Test-Path "venv")) {
    python -m venv venv
}

# 激活虚拟环境并安装依赖
Write-Host "安装项目依赖..." -ForegroundColor Yellow
& "$ProjectPath\venv\Scripts\activate.ps1"
python -m pip install --upgrade pip
pip install -r requirements.txt

# 配置数据库
Write-Host "配置数据库..." -ForegroundColor Yellow
$settingsPath = "$ProjectPath\property_management\settings.py"
if (Test-Path $settingsPath) {
    # 备份原始设置文件
    Copy-Item $settingsPath "$settingsPath.backup"

    # 更新数据库配置
    $settingsContent = Get-Content $settingsPath -Raw
    $newDbConfig = @"
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': '$DatabaseName',
        'USER': '$DatabaseUser',
        'PASSWORD': '$DatabasePassword',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
"@

    # 替换数据库配置
    $settingsContent = $settingsContent -replace "DATABASES = \{[^}]+\}", $newDbConfig
    Set-Content $settingsPath $settingsContent
}

# 数据库迁移
Write-Host "执行数据库迁移..." -ForegroundColor Yellow
python manage.py makemigrations
python manage.py migrate

# 收集静态文件
Write-Host "收集静态文件..." -ForegroundColor Yellow
python manage.py collectstatic --noinput

Write-Host "=== 部署完成！===" -ForegroundColor Green
Write-Host "请运行以下命令启动服务器：" -ForegroundColor Cyan
Write-Host "cd $ProjectPath" -ForegroundColor White
Write-Host ".\venv\Scripts\activate" -ForegroundColor White
Write-Host "python manage.py runserver 0.0.0.0:8000" -ForegroundColor White
```

#### 7.2 创建启动脚本
创建 `start.bat` 批处理文件：

```batch
@echo off
title 东悦物业管理系统
cd /d C:\dywy
call venv\Scripts\activate.bat
echo 启动东悦物业管理系统...
python manage.py runserver 0.0.0.0:8000
pause
```

#### 7.3 创建生产环境启动脚本
创建 `start_production.bat`：

```batch
@echo off
title 东悦物业管理系统 - 生产环境
cd /d C:\dywy
call venv\Scripts\activate.bat
echo 启动生产环境服务器...
python start_server.py
pause
```

### 8. 数据备份和恢复

#### 8.1 创建备份脚本
创建 `backup.ps1`：

```powershell
# backup.ps1 - 数据备份脚本
param(
    [string]$BackupPath = "C:\dywy\backups",
    [string]$DatabaseName = "dywy_db",
    [string]$DatabaseUser = "dywy_user",
    [string]$DatabasePassword = "dywy123456"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "$BackupPath\dywy_backup_$timestamp.sql"

# 创建备份目录
if (!(Test-Path $BackupPath)) {
    New-Item -ItemType Directory -Path $BackupPath -Force
}

Write-Host "开始备份数据库..." -ForegroundColor Yellow

# 使用 mysqldump 备份数据库
$mysqldumpPath = "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe"
if (Test-Path $mysqldumpPath) {
    & $mysqldumpPath -u $DatabaseUser -p$DatabasePassword $DatabaseName > $backupFile
    Write-Host "数据库备份完成: $backupFile" -ForegroundColor Green
} else {
    Write-Host "找不到 mysqldump.exe，请检查 MySQL 安装路径" -ForegroundColor Red
}

# 备份媒体文件
$mediaBackupFile = "$BackupPath\media_backup_$timestamp.zip"
if (Test-Path "C:\dywy\media") {
    Compress-Archive -Path "C:\dywy\media\*" -DestinationPath $mediaBackupFile
    Write-Host "媒体文件备份完成: $mediaBackupFile" -ForegroundColor Green
}

# 清理7天前的备份
Get-ChildItem $BackupPath -Name "*.sql" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-7) } | Remove-Item
Get-ChildItem $BackupPath -Name "*.zip" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-7) } | Remove-Item

Write-Host "备份完成！" -ForegroundColor Green
```

#### 8.2 创建恢复脚本
创建 `restore.ps1`：

```powershell
# restore.ps1 - 数据恢复脚本
param(
    [Parameter(Mandatory=$false)]
    [string]$BackupFile = "",
    [string]$DatabaseName = "dywy_db",
    [string]$DatabaseUser = "root",
    [string]$DatabasePassword = ""
)

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

Write-ColorOutput "=== 东悦物业管理系统数据恢复 ===" "Cyan"

# 如果没有指定备份文件，列出可用的备份
if ([string]::IsNullOrEmpty($BackupFile)) {
    $backupDir = "C:\dywy\backups"
    if (Test-Path $backupDir) {
        Write-ColorOutput "可用的备份文件：" "Yellow"
        $backups = Get-ChildItem "$backupDir\*.sql" | Sort-Object CreationTime -Descending
        if ($backups.Count -eq 0) {
            Write-ColorOutput "未找到备份文件！" "Red"
            exit 1
        }

        for ($i = 0; $i -lt $backups.Count; $i++) {
            $backup = $backups[$i]
            Write-ColorOutput "$($i + 1). $($backup.Name) ($(Get-Date $backup.CreationTime -Format 'yyyy-MM-dd HH:mm:ss'))" "White"
        }

        $selection = Read-Host "请选择要恢复的备份文件编号"
        try {
            $selectedIndex = [int]$selection - 1
            if ($selectedIndex -ge 0 -and $selectedIndex -lt $backups.Count) {
                $BackupFile = $backups[$selectedIndex].FullName
            } else {
                Write-ColorOutput "无效的选择！" "Red"
                exit 1
            }
        } catch {
            Write-ColorOutput "无效的输入！" "Red"
            exit 1
        }
    } else {
        Write-ColorOutput "备份目录不存在: $backupDir" "Red"
        exit 1
    }
}

# 检查备份文件是否存在
if (!(Test-Path $BackupFile)) {
    Write-ColorOutput "备份文件不存在: $BackupFile" "Red"
    exit 1
}

Write-ColorOutput "选择的备份文件: $BackupFile" "Green"

# 获取数据库密码
if ([string]::IsNullOrEmpty($DatabasePassword)) {
    $securePassword = Read-Host "请输入MySQL密码" -AsSecureString
    $DatabasePassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($securePassword))
}

# 确认恢复操作
Write-ColorOutput "警告：此操作将覆盖当前数据库中的所有数据！" "Red"
$confirm = Read-Host "确认继续？(y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-ColorOutput "操作已取消" "Yellow"
    exit 0
}

Write-ColorOutput "开始恢复数据库..." "Yellow"

# 检查MySQL服务
$mysqlService = Get-Service MySQL80 -ErrorAction SilentlyContinue
if ($mysqlService -and $mysqlService.Status -ne "Running") {
    Write-ColorOutput "启动MySQL服务..." "Yellow"
    Start-Service MySQL80
    Start-Sleep -Seconds 3
}

# 查找mysql.exe路径
$mysqlPaths = @(
    "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe",
    "C:\Program Files\MySQL\MySQL Server 8.1\bin\mysql.exe",
    "C:\MySQL\bin\mysql.exe"
)

$mysqlPath = $null
foreach ($path in $mysqlPaths) {
    if (Test-Path $path) {
        $mysqlPath = $path
        break
    }
}

if (-not $mysqlPath) {
    # 尝试从PATH中查找
    try {
        $mysqlPath = (Get-Command mysql.exe -ErrorAction Stop).Source
    } catch {
        Write-ColorOutput "找不到 mysql.exe，请检查 MySQL 安装" "Red"
        Write-ColorOutput "常见安装路径：" "Yellow"
        foreach ($path in $mysqlPaths) {
            Write-ColorOutput "  $path" "White"
        }
        exit 1
    }
}

Write-ColorOutput "使用MySQL路径: $mysqlPath" "Gray"

# 执行恢复
try {
    Write-ColorOutput "正在导入数据..." "Yellow"
    $startTime = Get-Date

    # 使用cmd执行，避免PowerShell重定向问题
    $cmd = "cmd.exe /c `"$mysqlPath`" -u $DatabaseUser -p$DatabasePassword $DatabaseName < `"$BackupFile`""
    Invoke-Expression $cmd

    if ($LASTEXITCODE -eq 0) {
        $endTime = Get-Date
        $duration = $endTime - $startTime
        Write-ColorOutput "数据库恢复完成！" "Green"
        Write-ColorOutput "耗时: $($duration.TotalSeconds.ToString('F2')) 秒" "Green"

        # 验证恢复结果
        Write-ColorOutput "验证数据库连接..." "Yellow"
        $testCmd = "cmd.exe /c `"$mysqlPath`" -u $DatabaseUser -p$DatabasePassword $DatabaseName -e `"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DatabaseName';`""
        $result = Invoke-Expression $testCmd
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "数据库连接验证成功！" "Green"
        }
    } else {
        Write-ColorOutput "数据库恢复失败！错误代码: $LASTEXITCODE" "Red"
        exit 1
    }
} catch {
    Write-ColorOutput "恢复过程中发生错误: $($_.Exception.Message)" "Red"
    exit 1
}

Write-ColorOutput ""
Write-ColorOutput "=== 恢复完成 ===" "Green"
Write-ColorOutput "数据库: $DatabaseName" "White"
Write-ColorOutput "备份文件: $BackupFile" "White"
Write-ColorOutput ""
Write-ColorOutput "建议操作：" "Cyan"
Write-ColorOutput "1. 重启Django应用" "White"
Write-ColorOutput "2. 清理缓存文件" "White"
Write-ColorOutput "3. 验证系统功能" "White"
```

### 9. 系统监控和维护

#### 9.1 创建系统监控脚本
创建 `monitor.ps1`：

```powershell
# monitor.ps1 - 系统监控脚本
Write-Host "=== 东悦物业管理系统状态监控 ===" -ForegroundColor Green

# 检查 Python 进程
$pythonProcesses = Get-Process python -ErrorAction SilentlyContinue
if ($pythonProcesses) {
    Write-Host "Python 进程运行中:" -ForegroundColor Green
    $pythonProcesses | Format-Table Id, ProcessName, CPU, WorkingSet -AutoSize
} else {
    Write-Host "未发现 Python 进程" -ForegroundColor Yellow
}

# 检查端口占用
Write-Host "`n检查端口 8000 占用情况:" -ForegroundColor Cyan
$port8000 = netstat -an | Select-String ":8000"
if ($port8000) {
    Write-Host $port8000 -ForegroundColor Green
} else {
    Write-Host "端口 8000 未被占用" -ForegroundColor Yellow
}

# 检查 MySQL 服务
Write-Host "`n检查 MySQL 服务状态:" -ForegroundColor Cyan
$mysqlService = Get-Service MySQL80 -ErrorAction SilentlyContinue
if ($mysqlService) {
    Write-Host "MySQL 服务状态: $($mysqlService.Status)" -ForegroundColor Green
} else {
    Write-Host "MySQL 服务未找到" -ForegroundColor Red
}

# 检查磁盘空间
Write-Host "`n检查磁盘空间:" -ForegroundColor Cyan
Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 } | ForEach-Object {
    $freeSpaceGB = [math]::Round($_.FreeSpace / 1GB, 2)
    $totalSpaceGB = [math]::Round($_.Size / 1GB, 2)
    $usedPercentage = [math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 2)
    Write-Host "$($_.DeviceID) 总空间: ${totalSpaceGB}GB, 可用: ${freeSpaceGB}GB, 已用: ${usedPercentage}%" -ForegroundColor White
}

# 检查内存使用
Write-Host "`n检查内存使用:" -ForegroundColor Cyan
$memory = Get-WmiObject -Class Win32_OperatingSystem
$totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
$freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
$usedMemoryGB = $totalMemoryGB - $freeMemoryGB
$memoryUsagePercentage = [math]::Round(($usedMemoryGB / $totalMemoryGB) * 100, 2)
Write-Host "总内存: ${totalMemoryGB}GB, 已用: ${usedMemoryGB}GB (${memoryUsagePercentage}%), 可用: ${freeMemoryGB}GB" -ForegroundColor White
```

### 10. 故障排除

#### 10.1 常见问题及解决方案

**问题1：Python 模块导入错误**
```
ModuleNotFoundError: No module named 'xxx'
```
解决方案：
```powershell
# 确保虚拟环境已激活
.\venv\Scripts\activate
# 重新安装依赖
pip install -r requirements.txt
```

**问题2：MySQL 连接失败**
```
django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")
```
解决方案：
```powershell
# 检查 MySQL 服务状态
Get-Service MySQL80
# 如果服务未启动，启动服务
Start-Service MySQL80
# 检查防火墙设置
netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=3306
```

**问题3：端口被占用**
```
OSError: [WinError 10048] 通常每个套接字地址只允许使用一次
```
解决方案：
```powershell
# 查找占用端口的进程
netstat -ano | findstr :8000
# 结束进程（替换 PID）
taskkill /PID <进程ID> /F
```

**问题4：静态文件404错误**
解决方案：
```powershell
# 重新收集静态文件
python manage.py collectstatic --noinput
# 检查 settings.py 中的 STATIC_URL 和 STATIC_ROOT 配置
```

**问题5：mysqlclient 安装失败**
解决方案：
```powershell
# 方法1：使用预编译的二进制包
pip install mysqlclient==2.2.0 --only-binary=all

# 方法2：安装 Microsoft Visual C++ Build Tools
# 下载并安装：https://visualstudio.microsoft.com/visual-cpp-build-tools/

# 方法3：使用 conda（如果安装了 Anaconda）
conda install mysqlclient
```

#### 10.2 日志查看和调试

创建 `debug.ps1` 调试脚本：
```powershell
# debug.ps1 - 调试信息收集脚本
Write-Host "=== 系统调试信息收集 ===" -ForegroundColor Green

# Python 环境信息
Write-Host "`nPython 环境信息:" -ForegroundColor Cyan
python --version
pip --version
where python

# 虚拟环境检查
Write-Host "`n虚拟环境检查:" -ForegroundColor Cyan
if (Test-Path "C:\dywy\venv\Scripts\python.exe") {
    Write-Host "虚拟环境存在" -ForegroundColor Green
    & "C:\dywy\venv\Scripts\python.exe" --version
} else {
    Write-Host "虚拟环境不存在" -ForegroundColor Red
}

# 已安装的包
Write-Host "`n已安装的 Python 包:" -ForegroundColor Cyan
& "C:\dywy\venv\Scripts\pip.exe" list

# Django 检查
Write-Host "`nDjango 系统检查:" -ForegroundColor Cyan
Set-Location "C:\dywy"
& "C:\dywy\venv\Scripts\python.exe" manage.py check

# 数据库连接测试
Write-Host "`n数据库连接测试:" -ForegroundColor Cyan
& "C:\dywy\venv\Scripts\python.exe" manage.py dbshell --command="SELECT 1;"
```

### 11. 安全配置

#### 11.1 Windows 防火墙配置
```powershell
# 允许 Django 应用通过防火墙
netsh advfirewall firewall add rule name="Django App" dir=in action=allow protocol=TCP localport=8000

# 允许 MySQL 通过防火墙（如果需要远程访问）
netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=3306
```

#### 11.2 生产环境安全设置
编辑 `property_management/settings_production.py`：

```python
import os
from .settings import *

# 生产环境安全配置
DEBUG = False
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'your-domain.com']

# 安全密钥（生产环境请更换）
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'your-production-secret-key')

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DB_NAME', 'dywy_db'),
        'USER': os.environ.get('DB_USER', 'dywy_user'),
        'PASSWORD': os.environ.get('DB_PASSWORD', 'dywy123456'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '3306'),
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}

# 安全中间件
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # 静态文件服务
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# 安全设置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# 会话安全
SESSION_COOKIE_SECURE = False  # 如果使用 HTTPS 设置为 True
CSRF_COOKIE_SECURE = False     # 如果使用 HTTPS 设置为 True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# 静态文件配置
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

#### 11.3 环境变量配置
创建 `.env` 文件（不要提交到版本控制）：
```
DJANGO_SECRET_KEY=your-very-secret-key-here
DB_NAME=dywy_db
DB_USER=dywy_user
DB_PASSWORD=your-secure-password
DB_HOST=localhost
DB_PORT=3306
DJANGO_SETTINGS_MODULE=property_management.settings_production
```

安装 python-decouple 来读取环境变量：
```powershell
pip install python-decouple
```

### 12. 性能优化

#### 12.1 MySQL 性能优化
编辑 MySQL 配置文件 `C:\ProgramData\MySQL\MySQL Server 8.0\my.ini`：

```ini
[mysqld]
# 基本设置
port = 3306
basedir = "C:/Program Files/MySQL/MySQL Server 8.0/"
datadir = "C:/ProgramData/MySQL/MySQL Server 8.0/Data/"

# 性能优化
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 连接设置
max_connections = 200
max_connect_errors = 10000

# 字符集
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
```

重启 MySQL 服务：
```powershell
Restart-Service MySQL80
```

#### 12.2 Django 性能优化
在 `settings_production.py` 中添加缓存配置：

```python
# 缓存配置（使用文件缓存）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': os.path.join(BASE_DIR, 'cache'),
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

# 会话缓存
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# 数据库连接池
DATABASES['default']['CONN_MAX_AGE'] = 60
```

#### 12.3 静态文件优化
安装和配置 WhiteNoise：
```powershell
pip install whitenoise[brotli]
```

### 13. 定时任务配置

#### 13.1 使用 Windows 任务计划程序
创建定时备份任务：

```powershell
# 创建定时备份任务（每天凌晨2点执行）
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\dywy\backup.ps1"
$trigger = New-ScheduledTaskTrigger -Daily -At 2:00AM
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

Register-ScheduledTask -TaskName "DywyBackup" -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "东悦物业管理系统数据备份"
```

#### 13.2 Django 定时任务
安装 django-crontab：
```powershell
pip install django-crontab
```

在 `settings.py` 中添加：
```python
INSTALLED_APPS = [
    # ... 其他应用
    'django_crontab',
]

# 定时任务配置
CRONJOBS = [
    ('0 2 * * *', 'myapp.cron.daily_backup'),  # 每天凌晨2点备份
    ('0 0 * * 0', 'myapp.cron.weekly_cleanup'),  # 每周日清理
]
```

### 14. 部署检查清单

#### 14.1 部署前检查
- [ ] Python 3.8+ 已安装并添加到 PATH
- [ ] MySQL 8.0+ 已安装并正常运行
- [ ] Git 已安装
- [ ] Visual Studio Build Tools 已安装（用于 mysqlclient）
- [ ] 项目代码已下载到 C:\dywy
- [ ] 虚拟环境已创建并激活
- [ ] 所有依赖包已安装
- [ ] 数据库已创建并配置
- [ ] 数据库迁移已完成
- [ ] 超级用户已创建
- [ ] 静态文件已收集

#### 14.2 生产环境检查
- [ ] DEBUG = False
- [ ] SECRET_KEY 已更换
- [ ] ALLOWED_HOSTS 已正确配置
- [ ] 数据库密码已更换为强密码
- [ ] 防火墙规则已配置
- [ ] 日志目录已创建
- [ ] 备份脚本已配置
- [ ] 定时任务已设置
- [ ] 系统监控已部署

#### 14.3 安全检查
- [ ] 默认密码已更改
- [ ] 不必要的端口已关闭
- [ ] 文件权限已正确设置
- [ ] 错误页面不显示敏感信息
- [ ] 日志记录已启用
- [ ] 定期备份已配置

### 15. 维护和更新

#### 15.1 系统更新流程
创建 `update.ps1` 更新脚本：

```powershell
# update.ps1 - 系统更新脚本
Write-Host "=== 东悦物业管理系统更新 ===" -ForegroundColor Green

# 备份当前版本
Write-Host "备份当前版本..." -ForegroundColor Yellow
$backupDir = "C:\dywy\backups\update_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force
Copy-Item "C:\dywy\*" $backupDir -Recurse -Exclude @("venv", "backups", "logs", "media")

# 激活虚拟环境
Set-Location "C:\dywy"
& ".\venv\Scripts\activate.ps1"

# 拉取最新代码
Write-Host "拉取最新代码..." -ForegroundColor Yellow
git pull origin main

# 更新依赖
Write-Host "更新依赖包..." -ForegroundColor Yellow
pip install -r requirements.txt --upgrade

# 数据库迁移
Write-Host "执行数据库迁移..." -ForegroundColor Yellow
python manage.py makemigrations
python manage.py migrate

# 收集静态文件
Write-Host "收集静态文件..." -ForegroundColor Yellow
python manage.py collectstatic --noinput

Write-Host "更新完成！请重启服务。" -ForegroundColor Green
```

#### 15.2 日常维护任务
创建 `maintenance.ps1`：

```powershell
# maintenance.ps1 - 日常维护脚本
Write-Host "=== 系统维护任务 ===" -ForegroundColor Green

# 清理临时文件
Write-Host "清理临时文件..." -ForegroundColor Yellow
Remove-Item "C:\dywy\logs\*.log.*" -Force -ErrorAction SilentlyContinue
Remove-Item "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue

# 清理过期会话
Write-Host "清理过期会话..." -ForegroundColor Yellow
Set-Location "C:\dywy"
& ".\venv\Scripts\activate.ps1"
python manage.py clearsessions

# 优化数据库
Write-Host "优化数据库..." -ForegroundColor Yellow
mysql -u dywy_user -pdywy123456 -e "OPTIMIZE TABLE django_session;" dywy_db

# 检查磁盘空间
Write-Host "检查磁盘空间..." -ForegroundColor Yellow
$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
if ($freeSpaceGB -lt 5) {
    Write-Host "警告：C盘剩余空间不足 5GB！" -ForegroundColor Red
}

Write-Host "维护任务完成！" -ForegroundColor Green
```

### 16. 联系和支持

如果在部署过程中遇到问题，请检查：

1. **系统要求**：确保满足所有软件和硬件要求
2. **错误日志**：查看 Django 和 MySQL 的错误日志
3. **网络连接**：确保网络连接正常
4. **权限问题**：确保以管理员身份运行相关命令

**常用命令快速参考：**
```powershell
# 启动开发服务器
cd C:\dywy
.\venv\Scripts\activate
python manage.py runserver 0.0.0.0:8000

# 启动生产服务器
cd C:\dywy
.\venv\Scripts\activate
python start_server.py

# 数据库备份
.\backup.ps1

# 系统监控
.\monitor.ps1

# 系统维护
.\maintenance.ps1
```

---

**部署完成后，请访问 http://localhost:8000 查看系统是否正常运行。**

**注意事项：**
- 生产环境请务必更改默认密码
- 定期备份数据库和重要文件
- 监控系统资源使用情况
- 及时更新系统和依赖包
