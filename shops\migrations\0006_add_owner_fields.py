# Generated manually for adding owner fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shops', '0005_make_id_card_optional'),
    ]

    operations = [
        migrations.AddField(
            model_name='shop',
            name='owner_name',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='业主姓名'),
        ),
        migrations.AddField(
            model_name='shop',
            name='owner_phone',
            field=models.CharField(blank=True, help_text='可输入多个电话号码，用逗号分隔', max_length=50, null=True, verbose_name='业主电话'),
        ),
    ]
