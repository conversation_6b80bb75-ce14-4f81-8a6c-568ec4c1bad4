# 东悦物业管理系统 - Windows 11 部署说明

## 📋 部署文档概览

本项目提供了多种Windows 11部署方案，请根据您的需求选择合适的部署方式：

### 📚 部署文档列表

1. **[Windows11部署指南.md](./Windows11部署指南.md)** - 完整详细的部署指南
   - 包含完整的环境配置、安全设置、性能优化
   - 适合生产环境部署
   - 包含故障排除和维护指南

2. **[Windows11快速部署指南.md](./Windows11快速部署指南.md)** - 5分钟快速部署
   - 简化的部署流程
   - 适合开发和测试环境
   - 快速上手指南

3. **[auto_deploy.ps1](./auto_deploy.ps1)** - 一键自动部署脚本
   - 全自动化部署
   - 适合批量部署
   - 包含错误检查和恢复

## 🚀 推荐部署方式

### 方式一：自动化部署（推荐）

**适用场景**：首次部署、批量部署、快速搭建测试环境

**步骤**：
1. 以管理员身份打开 PowerShell
2. 下载并运行自动部署脚本：
   ```powershell
   # 下载脚本
   Invoke-WebRequest -Uri "https://raw.githubusercontent.com/your-repo/auto_deploy.ps1" -OutFile "auto_deploy.ps1"
   
   # 执行部署
   .\auto_deploy.ps1
   ```

**优点**：
- 全自动化，无需手动配置
- 自动检查依赖和环境
- 自动创建启动脚本和备份脚本
- 包含错误处理和回滚机制

### 方式二：手动部署

**适用场景**：需要自定义配置、学习部署过程、生产环境精细化配置

**步骤**：
1. 参考 [Windows11快速部署指南.md](./Windows11快速部署指南.md) 进行基础部署
2. 参考 [Windows11部署指南.md](./Windows11部署指南.md) 进行高级配置

## 📋 系统要求

### 硬件要求
- **操作系统**：Windows 11 专业版或企业版
- **内存**：最低 4GB，推荐 8GB+
- **存储**：最低 50GB 可用空间
- **CPU**：最低 4核，推荐 8核+

### 软件要求
- **Python**：3.8+ (推荐 3.11)
- **MySQL**：8.0+
- **Git**：最新版本
- **Visual Studio Build Tools**：用于编译 mysqlclient

## 🔧 部署后配置

### 1. 创建管理员用户
```powershell
cd C:\dywy
.\venv\Scripts\activate
python manage.py createsuperuser
```

### 2. 启动系统
```powershell
# 开发环境
.\start.bat

# 生产环境
.\start_production.bat
```

### 3. 访问系统
- 开发环境：http://localhost:8000
- 管理后台：http://localhost:8000/admin

## 🛠️ 常用操作

### 启动和停止服务
```powershell
# 启动开发服务器
cd C:\dywy
.\venv\Scripts\activate
python manage.py runserver 0.0.0.0:8000

# 启动生产服务器
cd C:\dywy
.\venv\Scripts\activate
python start_production.py
```

### 数据库操作
```powershell
# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 数据库备份
.\backup.ps1

# 数据库恢复
.\restore.ps1 -BackupFile "backups\db_backup_20241205_143000.sql"
```

### 系统维护
```powershell
# 系统监控
.\monitor.ps1

# 系统维护
.\maintenance.ps1

# 系统更新
.\update.ps1
```

## 🔒 安全配置

### 生产环境安全检查清单
- [ ] 修改默认密码
- [ ] 配置防火墙规则
- [ ] 启用HTTPS（如果需要）
- [ ] 设置安全头
- [ ] 配置日志监控
- [ ] 定期备份数据
- [ ] 更新系统补丁

### 防火墙配置
```powershell
# 允许应用通过防火墙
netsh advfirewall firewall add rule name="Django App" dir=in action=allow protocol=TCP localport=8000
```

## 📊 性能优化

### MySQL 优化
- 调整 `innodb_buffer_pool_size`
- 启用查询缓存
- 优化连接数设置

### Django 优化
- 启用缓存
- 配置静态文件服务
- 使用连接池

## 🐛 故障排除

### 常见问题

**问题1：Python 命令不识别**
```
'python' 不是内部或外部命令
```
**解决方案**：重新安装 Python 并确保勾选 "Add Python to PATH"

**问题2：MySQL 连接失败**
```
django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")
```
**解决方案**：
```powershell
# 检查 MySQL 服务
Get-Service MySQL80
# 启动服务
Start-Service MySQL80
```

**问题3：端口被占用**
```
OSError: [WinError 10048] 通常每个套接字地址只允许使用一次
```
**解决方案**：
```powershell
# 查找占用进程
netstat -ano | findstr :8000
# 结束进程
taskkill /PID <进程ID> /F
```

**问题4：mysqlclient 安装失败**
**解决方案**：
```powershell
# 安装预编译版本
pip install mysqlclient --only-binary=all
```

### 调试工具
```powershell
# 收集调试信息
.\debug.ps1

# 查看系统状态
.\monitor.ps1

# 检查日志
Get-Content logs\django.log -Tail 50
```

## 📞 技术支持

### 获取帮助
1. 查看相关部署文档
2. 检查错误日志
3. 运行调试脚本
4. 查看常见问题解决方案

### 联系方式
- 项目文档：查看项目根目录下的文档文件
- 错误报告：请提供详细的错误信息和系统环境

## 📝 更新日志

### v1.0.0 (2024-12-05)
- 初始版本发布
- 支持 Windows 11 自动化部署
- 包含完整的部署文档和脚本
- 支持开发和生产环境配置

---

**注意**：部署前请仔细阅读相关文档，确保满足系统要求。建议先在测试环境验证部署流程。
