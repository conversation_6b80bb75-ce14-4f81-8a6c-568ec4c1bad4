{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-store me-2"></i>{{ title }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- 商铺号 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.shop_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-store me-1"></i>商铺号 *
                                </label>
                                {{ form.shop_number }}
                                {% if form.shop_number.errors %}
                                    <div class="text-danger">{{ form.shop_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- 门牌号 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.door_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>门牌号
                                </label>
                                {{ form.door_number }}
                                {% if form.door_number.errors %}
                                    <div class="text-danger">{{ form.door_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- 租户或业主姓名 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.tenant_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>租户或业主姓名 *
                                </label>
                                {{ form.tenant_name }}
                                {% if form.tenant_name.errors %}
                                    <div class="text-danger">{{ form.tenant_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- 电话 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone me-1"></i>电话 *
                                </label>
                                {{ form.phone }}
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>可输入多个电话号码，用逗号分隔
                                </div>
                                {% if form.phone.errors %}
                                    <div class="text-danger">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- 身份证号 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.id_card.id_for_label }}" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>身份证号
                                </label>
                                {{ form.id_card }}
                                {% if form.id_card.errors %}
                                    <div class="text-danger">{{ form.id_card.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- 平米数 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.area.id_for_label }}" class="form-label">
                                    <i class="fas fa-ruler-combined me-1"></i>平米数 *
                                </label>
                                <div class="input-group">
                                    {{ form.area }}
                                    <span class="input-group-text">㎡</span>
                                </div>
                                {% if form.area.errors %}
                                    <div class="text-danger">{{ form.area.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">
                                    物业费：每平米1.5元/月
                                </div>
                            </div>
                        </div>

                        <!-- 业主信息 -->
                        <div class="row">
                            <!-- 业主姓名 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.owner_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tie me-1"></i>业主姓名
                                </label>
                                {{ form.owner_name }}
                                {% if form.owner_name.errors %}
                                    <div class="text-danger">{{ form.owner_name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- 业主电话 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.owner_phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone me-1"></i>业主电话
                                </label>
                                {{ form.owner_phone }}
                                {% if form.owner_phone.errors %}
                                    <div class="text-danger">{{ form.owner_phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- 物业费开始时间 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.lease_start_date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>物业费开始时间 *
                                </label>
                                {% if object and object.lease_start_date %}
                                    <input type="date" name="lease_start_date" value="{{ object.lease_start_date|date:'Y-m-d' }}"
                                           class="form-control" id="{{ form.lease_start_date.id_for_label }}" required>
                                {% else %}
                                    {{ form.lease_start_date }}
                                {% endif %}
                                {% if form.lease_start_date.errors %}
                                    <div class="text-danger">{{ form.lease_start_date.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <!-- 物业费到期时间 -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.property_fee_due_date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-times me-1"></i>物业费到期时间 *
                                </label>
                                {% if object and object.property_fee_due_date %}
                                    <input type="date" name="property_fee_due_date" value="{{ object.property_fee_due_date|date:'Y-m-d' }}"
                                           class="form-control" id="{{ form.property_fee_due_date.id_for_label }}" required>
                                {% else %}
                                    {{ form.property_fee_due_date }}
                                {% endif %}
                                {% if form.property_fee_due_date.errors %}
                                    <div class="text-danger">{{ form.property_fee_due_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- 物业费预览 -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-calculator me-2"></i>物业费计算
                                    </h6>
                                    <div id="fee-calculation">
                                        <span class="fw-bold">月物业费：</span>
                                        <span id="monthly-fee" class="text-success fw-bold">¥0.00</span>
                                        <span class="text-muted ms-3">（平米数 × 1.5元/㎡）</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 表单错误 -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'shops:list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const shopNumberInput = document.getElementById('id_shop_number');
        const doorNumberInput = document.getElementById('id_door_number');
        const areaInput = document.getElementById('id_area');
        const monthlyFeeSpan = document.getElementById('monthly-fee');
        
        // 自动填充商铺信息
        function autoFillShopInfo(shopNumber) {
            if (!shopNumber) return;
            
            fetch(`/shops/api/shop-info/?shop_number=${encodeURIComponent(shopNumber)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        doorNumberInput.value = data.door_number;
                        areaInput.value = data.area;
                        updateFeeCalculation();
                    }
                })
                .catch(error => {
                    console.error('获取商铺信息失败:', error);
                });
        }
        
        // 更新物业费计算
        function updateFeeCalculation() {
            const area = parseFloat(areaInput.value) || 0;
            const monthlyFee = area * 1.5;
            monthlyFeeSpan.textContent = `¥${monthlyFee.toFixed(2)}`;
        }
        
        // 绑定事件
        if (shopNumberInput) {
            shopNumberInput.addEventListener('change', function() {
                autoFillShopInfo(this.value);
            });
        }
        
        if (areaInput) {
            areaInput.addEventListener('input', updateFeeCalculation);
            // 初始计算
            updateFeeCalculation();
        }
        
        // 全局函数供表单使用
        window.autoFillShopInfo = autoFillShopInfo;
    });
</script>
{% endblock %}
