{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客续费 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>租客续费
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 租客信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5 class="alert-heading">
                                    <i class="fas fa-user me-2"></i>租客信息
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>姓名：</strong>{{ tenant.tenant_name }}</p>
                                        <p><strong>房号：</strong>{{ tenant.building_number }}-{{ tenant.room_number }}</p>
                                        <p><strong>面积：</strong>{{ tenant.area }}㎡</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>当前到期时间：</strong>{{ tenant.property_fee_due_date }}</p>
                                        <p><strong>月物业费：</strong>¥{{ tenant.property_fee }}</p>
                                        <p><strong>状态：</strong>
                                            {% if tenant.is_overdue %}
                                                <span class="badge bg-danger">逾期{{ tenant.overdue_days }}天</span>
                                            {% else %}
                                                <span class="badge bg-success">正常</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 续费表单 -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.months.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>续费月数 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.months }}
                                    {% if form.months.errors %}
                                        <div class="text-danger small">{{ form.months.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-calculator me-1"></i>续费金额
                                    </label>
                                    <input type="text" class="form-control" id="total_amount" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.payment_method.id_for_label }}" class="form-label">
                                        <i class="fas fa-credit-card me-1"></i>缴费方式
                                    </label>
                                    {{ form.payment_method }}
                                    {% if form.payment_method.errors %}
                                        <div class="text-danger small">{{ form.payment_method.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-check me-1"></i>新到期时间
                                    </label>
                                    <input type="text" class="form-control" id="new_due_date" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>备注
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- 费用明细 -->
                        <div class="alert alert-light">
                            <h6 class="alert-heading">
                                <i class="fas fa-list me-2"></i>费用明细
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>基础物业费：</strong>{{ tenant.area }}㎡ × ¥1.00 = ¥<span id="base_fee">{{ tenant.area }}</span></p>
                                    <p><strong>电梯费：</strong>{{ tenant.area }}㎡ × ¥<span id="elevator_rate">{% if tenant.floor <= 11 %}0.30{% else %}0.35{% endif %}</span> = ¥<span id="elevator_fee">{% if tenant.floor <= 11 %}{% widthratio tenant.area 1 0.30 %}{% else %}{% widthratio tenant.area 1 0.35 %}{% endif %}</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>月物业费：</strong>¥{{ tenant.property_fee }}</p>
                                    <p><strong>续费月数：</strong><span id="display_months">12</span>个月</p>
                                    <p class="h5 text-primary"><strong>总计：</strong>¥<span id="display_total">{% widthratio tenant.property_fee 1 12 %}</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'tenants:list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>确认续费
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const monthsInput = document.getElementById('id_months');
        const totalAmountInput = document.getElementById('total_amount');
        const newDueDateInput = document.getElementById('new_due_date');
        const displayMonths = document.getElementById('display_months');
        const displayTotal = document.getElementById('display_total');
        
        const monthlyFee = {{ tenant.property_fee }};
        const currentDueDate = new Date('{{ tenant.property_fee_due_date|date:"Y-m-d" }}');

        // 获取今天的日期（只保留年月日，去掉时分秒）
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // 设置当前到期日期的时间为0点，便于比较
        currentDueDate.setHours(0, 0, 0, 0);

        function updateCalculations() {
            const months = parseInt(monthsInput.value) || 0;
            const totalAmount = monthlyFee * months;

            // 更新显示
            totalAmountInput.value = '¥' + totalAmount.toFixed(2);
            displayMonths.textContent = months;
            displayTotal.textContent = totalAmount.toFixed(2);

            if (months === 0) {
                newDueDateInput.value = '';
                return;
            }

            // 计算新到期日期 - 始终基于物业费到期时间计算
            // 从当前物业费到期日期的下一天开始计算（无论是否逾期）
            let feeStartDate = new Date(currentDueDate);
            feeStartDate.setDate(feeStartDate.getDate() + 1);

            // 计算新的到期日期：开始日期 + 月数 - 1天
            // 使用更精确的月份计算，模拟dateutil.relativedelta的行为
            let newDueDate = new Date(feeStartDate);

            // 先添加月数
            let targetMonth = newDueDate.getMonth() + months;
            let targetYear = newDueDate.getFullYear();

            // 处理跨年的情况
            while (targetMonth >= 12) {
                targetMonth -= 12;
                targetYear += 1;
            }
            while (targetMonth < 0) {
                targetMonth += 12;
                targetYear -= 1;
            }

            // 设置新的年月，保持原来的日期
            newDueDate.setFullYear(targetYear, targetMonth, newDueDate.getDate());

            // 处理月末日期的特殊情况（如1月31日+1个月应该是2月28日或29日）
            if (newDueDate.getMonth() !== targetMonth) {
                // 如果设置后月份不对，说明目标月份没有这一天，设置为目标月份的最后一天
                newDueDate = new Date(targetYear, targetMonth + 1, 0);
            }

            // 最后减去1天
            newDueDate.setDate(newDueDate.getDate() - 1);

            // 格式化日期为 YYYY-MM-DD 格式
            const year = newDueDate.getFullYear();
            const month = String(newDueDate.getMonth() + 1).padStart(2, '0');
            const day = String(newDueDate.getDate()).padStart(2, '0');

            const formattedDate = `${year}-${month}-${day}`;
            newDueDateInput.value = formattedDate;

            // 调试信息（可以在浏览器控制台查看）
            console.log('续费计算调试信息:');
            console.log('当前物业费到期日期:', currentDueDate.toISOString().split('T')[0]);
            console.log('费用开始日期:', feeStartDate.toISOString().split('T')[0]);
            console.log('续费月数:', months);
            console.log('新到期日期:', formattedDate);
        }
        
        // 监听月数变化
        monthsInput.addEventListener('input', updateCalculations);
        
        // 初始化计算
        updateCalculations();
    });
</script>
{% endblock %}
