{% extends 'base/base.html' %}
{% load static %}

{% block title %}首页 - 东悦物业管理系统{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -15px 2rem -15px;
        border-radius: 0 0 20px 20px;
    }
    
    .welcome-text {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .current-time {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea, #764ba2);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: #666;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .stats-change {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .stats-change.positive {
        color: #28a745;
    }

    .stats-change.negative {
        color: #dc3545;
    }
    
    .stats-change.negative {
        color: #dc3545;
    }
    
    .icon-tenants { background: linear-gradient(135deg, #667eea, #764ba2); }
    .icon-commercial { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .icon-shops { background: linear-gradient(135deg, #4facfe, #00f2fe); }
    .icon-parking { background: linear-gradient(135deg, #43e97b, #38f9d7); }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-top: 2rem;
    }
    
    .dashboard-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    
    .payment-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .payment-item:last-child {
        border-bottom: none;
    }
    
    .payment-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 1rem;
    }
    
    .payment-info {
        flex: 1;
    }
    
    .payment-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .payment-details {
        font-size: 0.875rem;
        color: #666;
    }
    
    .payment-amount {
        font-weight: 700;
        color: #28a745;
        font-size: 1.1rem;
    }
    
    .expiry-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .expiry-item:last-child {
        border-bottom: none;
    }
    
    .expiry-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffc107;
        color: white;
        margin-right: 1rem;
    }
    
    .expiry-info {
        flex: 1;
    }
    
    .expiry-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .expiry-details {
        font-size: 0.875rem;
        color: #666;
    }
    
    .expiry-days {
        font-weight: 600;
        font-size: 0.875rem;
    }

    .expiry-days.urgent {
        color: #dc3545;
    }

    .expiry-days.warning {
        color: #ffc107;
    }

    .expiry-days.normal {
        color: #28a745;
    }

    .expiry-icon.urgent {
        background: #dc3545;
    }

    .expiry-icon.warning {
        background: #ffc107;
    }

    .expiry-icon.normal {
        background: #28a745;
    }

    /* 空状态样式 */
    .empty-state {
        text-align: center;
        padding: 2rem 1rem;
        color: #6c757d;
    }

    .empty-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-text {
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* 支付记录样式增强 */
    .payment-type {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .payment-room {
        color: #495057;
        font-weight: 500;
    }

    .payment-time {
        color: #6c757d;
        font-style: italic;
    }

    /* 到期提醒样式增强 */
    .expiry-type {
        background: #ffc107;
        color: #212529;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .expiry-room {
        color: #495057;
        font-weight: 500;
    }
    
    /* 收入看板样式 */
    .income-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .income-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .income-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .monthly-income::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .yearly-income::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #007bff, #6610f2);
    }

    .income-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .income-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-right: 1rem;
    }

    .monthly-income .income-icon {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .yearly-income .income-icon {
        background: linear-gradient(135deg, #007bff, #6610f2);
    }

    .income-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #666;
        margin-bottom: 0.5rem;
    }

    .income-amount {
        font-size: 2.2rem;
        font-weight: 700;
        color: #333;
        margin: 0;
    }

    .income-breakdown {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .breakdown-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .breakdown-label {
        font-weight: 500;
        color: #666;
    }

    .breakdown-value {
        font-weight: 600;
        color: #333;
    }

    .income-stats {
        margin-top: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        margin-right: 1rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #666;
        display: block;
    }

    .stat-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }

    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .income-grid {
            grid-template-columns: 1fr;
        }

        .income-card {
            padding: 1.5rem;
        }

        .income-amount {
            font-size: 1.8rem;
        }

        .breakdown-item {
            font-size: 0.8rem;
        }

        .dashboard-header {
            margin: -1.5rem -15px 1rem -15px;
            padding: 1.5rem 0;
        }

        .welcome-text {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="welcome-text">
                    <i class="fas fa-sun me-2"></i>欢迎回来，{{ user.username }}
                </div>
                <div class="current-time">
                    <i class="fas fa-clock me-2"></i>
                    <span id="current-time"></span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-none d-md-block">
                    <i class="fas fa-building" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- 收入看板 -->
    <div class="income-grid">
        <div class="income-card monthly-income">
            <div class="income-header">
                <div class="income-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="income-info">
                    <h3 class="income-title">本月总收入</h3>
                    <h2 class="income-amount">¥{{ monthly_income|floatformat:2 }}</h2>
                </div>
            </div>
            <div class="income-breakdown">
                <div class="breakdown-item">
                    <span class="breakdown-label">租客</span>
                    <span class="breakdown-value">¥{{ tenant_monthly_income|floatformat:2 }}</span>
                </div>
                <div class="breakdown-item">
                    <span class="breakdown-label">商品房</span>
                    <span class="breakdown-value">¥{{ commercial_monthly_income|floatformat:2 }}</span>
                </div>
                <div class="breakdown-item">
                    <span class="breakdown-label">商铺</span>
                    <span class="breakdown-value">¥{{ shop_monthly_income|floatformat:2 }}</span>
                </div>
                <div class="breakdown-item">
                    <span class="breakdown-label">车位</span>
                    <span class="breakdown-value">¥{{ parking_monthly_income|floatformat:2 }}</span>
                </div>
                <div class="breakdown-item">
                    <span class="breakdown-label">其它</span>
                    <span class="breakdown-value">¥{{ other_monthly_income|floatformat:2 }}</span>
                </div>
            </div>
        </div>

        <div class="income-card yearly-income">
            <div class="income-header">
                <div class="income-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="income-info">
                    <h3 class="income-title">本年总收入</h3>
                    <h2 class="income-amount">¥{{ yearly_income|floatformat:2 }}</h2>
                </div>
            </div>
            <div class="income-stats">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-trending-up"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-label">月平均收入</span>
                        <span class="stat-value">¥{% widthratio yearly_income 12 1 %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-icon icon-tenants">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number">{{ total_tenants }}</div>
            <div class="stats-label">租客总数</div>
            <div class="stats-change {% if overdue_tenants > 0 %}negative{% else %}positive{% endif %}">
                {% if overdue_tenants > 0 %}
                    <i class="fas fa-exclamation-triangle me-1"></i>{{ overdue_tenants }} 逾期
                {% else %}
                    <i class="fas fa-check me-1"></i>无逾期
                {% endif %}
            </div>
        </div>

        <div class="stats-card">
            <div class="stats-icon icon-commercial">
                <i class="fas fa-home"></i>
            </div>
            <div class="stats-number">{{ total_commercial_properties }}</div>
            <div class="stats-label">商品房总数</div>
            <div class="stats-change {% if overdue_commercial > 0 %}negative{% else %}positive{% endif %}">
                {% if overdue_commercial > 0 %}
                    <i class="fas fa-exclamation-triangle me-1"></i>{{ overdue_commercial }} 逾期
                {% else %}
                    <i class="fas fa-check me-1"></i>无逾期
                {% endif %}
            </div>
        </div>

        <div class="stats-card">
            <div class="stats-icon icon-shops">
                <i class="fas fa-store"></i>
            </div>
            <div class="stats-number">{{ total_shops }}</div>
            <div class="stats-label">商铺总数</div>
            <div class="stats-change {% if overdue_shops > 0 %}negative{% else %}positive{% endif %}">
                {% if overdue_shops > 0 %}
                    <i class="fas fa-exclamation-triangle me-1"></i>{{ overdue_shops }} 逾期
                {% else %}
                    <i class="fas fa-check me-1"></i>无逾期
                {% endif %}
            </div>
        </div>

        <div class="stats-card">
            <div class="stats-icon icon-parking">
                <i class="fas fa-car"></i>
            </div>
            <div class="stats-number">{{ total_parking_spaces }}</div>
            <div class="stats-label">车位总数</div>
            <div class="stats-change {% if overdue_parking > 0 %}negative{% else %}positive{% endif %}">
                {% if overdue_parking > 0 %}
                    <i class="fas fa-exclamation-triangle me-1"></i>{{ overdue_parking }} 逾期
                {% else %}
                    <i class="fas fa-check me-1"></i>无逾期
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="dashboard-grid">
        <!-- 最近收款记录 -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-money-bill-wave me-2"></i>最近收款记录
                </h3>
                <span class="badge bg-info">{{ recent_payments|length }}</span>
            </div>

            {% if recent_payments %}
                {% for payment in recent_payments %}
                <div class="payment-item">
                    <div class="payment-avatar" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        {% if payment.name %}{{ payment.name|first|upper }}{% else %}?{% endif %}
                    </div>
                    <div class="payment-info">
                        <div class="payment-name">{{ payment.name|default:"未知" }}</div>
                        <div class="payment-details">
                            <span class="payment-type">{{ payment.type }}</span> ·
                            <span class="payment-room">{{ payment.room|default:"-" }}</span> ·
                            <span class="payment-time">{{ payment.date|timesince }}前</span>
                        </div>
                    </div>
                    <div class="payment-amount">
                        ¥{{ payment.amount|floatformat:2 }}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="empty-text">暂无收款记录</div>
                </div>
            {% endif %}
        </div>
        
        <!-- 即将到期提醒 -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>即将到期
                </h3>
                <span class="badge {% if upcoming_expiries|length > 0 %}bg-warning{% else %}bg-success{% endif %}">
                    {{ upcoming_expiries|length }}
                </span>
            </div>

            {% if upcoming_expiries %}
                {% for expiry in upcoming_expiries %}
                <div class="expiry-item">
                    <div class="expiry-icon {% if expiry.days_until_due <= 7 %}urgent{% elif expiry.days_until_due <= 15 %}warning{% else %}normal{% endif %}">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="expiry-info">
                        <div class="expiry-name">{{ expiry.name|default:"未知" }}</div>
                        <div class="expiry-details">
                            <span class="expiry-type">{{ expiry.type }}</span> ·
                            <span class="expiry-room">{{ expiry.room|default:"-" }}</span>
                        </div>
                    </div>
                    <div class="expiry-days {% if expiry.days_until_due <= 7 %}urgent{% elif expiry.days_until_due <= 15 %}warning{% else %}normal{% endif %}">
                        {% if expiry.days_until_due <= 0 %}
                            已到期
                        {% elif expiry.days_until_due == 1 %}
                            明天到期
                        {% else %}
                            {{ expiry.days_until_due }}天后到期
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="empty-text">暂无即将到期项目</div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 实时时间显示
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            weekday: 'long'
        });
        document.getElementById('current-time').textContent = timeString;
    }
    
    // 初始化时间显示
    updateTime();
    setInterval(updateTime, 1000);
    
    // 统计卡片动画
    document.addEventListener('DOMContentLoaded', function() {
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
{% endblock %}
