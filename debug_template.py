#!/usr/bin/env python
"""
调试模板数据传递
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'property_management.settings')
django.setup()

from commercial_properties.models import CommercialProperty

def debug_property_data():
    """调试商品房数据"""
    # 查找9号楼1单元201
    try:
        property = CommercialProperty.objects.get(
            building_number='9', 
            unit_number='1', 
            room_number='201'
        )
        
        print("=== 9号楼1单元201 数据调试 ===")
        print(f"ID: {property.id}")
        print(f"房号: {property.building_number}-{property.unit_number}-{property.room_number}")
        print(f"面积: {property.area}")
        print(f"楼层: {property.floor}")
        print(f"车位: {property.has_parking}")
        print(f"地下室: {property.has_basement}")
        print()
        
        print("=== 费用数据 ===")
        print(f"数据库存储的年费用 (property_fee_total): {property.property_fee_total}")
        print(f"计算的年费用 (calculate_property_fee): {property.calculate_property_fee()}")
        print(f"计算的月费用 (calculate_monthly_property_fee): {property.calculate_monthly_property_fee()}")
        print()
        
        print("=== 模板变量测试 ===")
        print(f"{{ property.property_fee_total }} = {property.property_fee_total}")
        print(f"{{ property.calculate_property_fee }} = {property.calculate_property_fee()}")
        print()
        
        # 检查数据类型
        print("=== 数据类型检查 ===")
        print(f"property_fee_total 类型: {type(property.property_fee_total)}")
        print(f"calculate_property_fee() 类型: {type(property.calculate_property_fee())}")
        
        return property
        
    except CommercialProperty.DoesNotExist:
        print("未找到9号楼1单元201")
        return None

if __name__ == "__main__":
    debug_property_data()
