#!/usr/bin/env python
"""
测试续费计算是否与年费用总额一致
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'property_management.settings')
django.setup()

from commercial_properties.models import CommercialProperty
from decimal import Decimal

def test_renew_calculation():
    """测试续费计算"""
    # 查找9号楼1单元201
    try:
        property = CommercialProperty.objects.get(
            building_number='9', 
            unit_number='1', 
            room_number='201'
        )
        
        print("=== 续费计算测试 - 9号楼1单元201 ===")
        print(f"房号: {property.building_number}-{property.unit_number}-{property.room_number}")
        print(f"面积: {property.area}㎡, 楼层: {property.floor}层")
        print(f"车位: {'有' if property.has_parking else '无'}")
        print()
        
        # 获取各种费用计算方法
        raw_monthly_fee = property._calculate_raw_monthly_fee()
        display_monthly_fee = property.calculate_monthly_property_fee()
        yearly_fee = property.calculate_property_fee()
        stored_yearly_fee = property.property_fee_total
        
        print("=== 费用对比 ===")
        print(f"原始月费用 (_calculate_raw_monthly_fee): {raw_monthly_fee}")
        print(f"显示月费用 (calculate_monthly_property_fee): {display_monthly_fee}")
        print(f"计算年费用 (calculate_property_fee): {yearly_fee}")
        print(f"存储年费用 (property_fee_total): {stored_yearly_fee}")
        print()
        
        print("=== 续费计算测试 ===")
        for months in [1, 3, 6, 12]:
            # 旧方法（有精度问题）
            old_method = display_monthly_fee * months
            
            # 新方法（修复后）
            new_method = (raw_monthly_fee * months).quantize(Decimal('0.01'))
            
            # 如果是12个月，应该等于年费用
            expected = yearly_fee if months == 12 else new_method
            
            print(f"{months}个月续费:")
            print(f"  旧方法: {old_method}")
            print(f"  新方法: {new_method}")
            if months == 12:
                print(f"  年费用: {yearly_fee}")
                print(f"  是否一致: {'✓' if new_method == yearly_fee else '✗'}")
            print()
        
        # 验证12个月续费是否等于年费用
        twelve_month_renew = (raw_monthly_fee * 12).quantize(Decimal('0.01'))
        print("=== 关键验证 ===")
        print(f"12个月续费金额: {twelve_month_renew}")
        print(f"年物业费总额: {yearly_fee}")
        print(f"是否一致: {'✓ 正确' if twelve_month_renew == yearly_fee else '✗ 不一致'}")
        
        return property
        
    except CommercialProperty.DoesNotExist:
        print("未找到9号楼1单元201")
        return None

if __name__ == "__main__":
    test_renew_calculation()
